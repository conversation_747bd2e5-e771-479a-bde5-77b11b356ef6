/**
 * API 兼容性工具
 * 用于处理不同API响应格式之间的兼容性问题
 */

import { ConversationItem, PaginatedResponse } from '@/types/conversation';
import { transformApiConversationList } from './data-transform';

/**
 * 标准化会话列表响应
 * 将各种可能的响应格式转换为统一的格式
 */
export function normalizeConversationsResponse(
  response: any
): { 
  data: ConversationItem[], 
  pagination?: { 
    total: number, 
    currentPage: number, 
    totalPages: number, 
    hasMore: boolean 
  } 
} {
  // 如果是标准分页响应
  if (response && typeof response === 'object' && response.pager && response.data) {
    return {
      data: response.data,
      pagination: {
        total: response.pager.total,
        currentPage: response.pager.page,
        totalPages: response.pager.totalPages,
        hasMore: response.pager.page < response.pager.totalPages
      }
    };
  }
  
  // 如果是直接的数组
  if (Array.isArray(response)) {
    const transformedData = transformApiConversationList(response);
    return {
      data: transformedData,
      pagination: {
        total: transformedData.length,
        currentPage: 1,
        totalPages: 1,
        hasMore: false
      }
    };
  }
  
  // 如果是 { data: [...] } 格式
  if (response && response.data && Array.isArray(response.data)) {
    const transformedData = transformApiConversationList(response.data);
    return {
      data: transformedData,
      pagination: {
        total: transformedData.length,
        currentPage: 1,
        totalPages: 1,
        hasMore: false
      }
    };
  }
  
  // 🔧 新增：如果是 { items: [...], count: number } 格式
  if (response && response.items && Array.isArray(response.items)) {
    const transformedData = transformApiConversationList(response.items);
    return {
      data: transformedData,
      pagination: {
        total: response.count || transformedData.length,
        currentPage: 1,
        totalPages: 1,
        hasMore: false
      }
    };
  }
  
  // 如果无法识别，返回空结果
  console.warn('⚠️ 无法识别的会话列表响应格式:', response);
  return {
    data: [],
    pagination: {
      total: 0,
      currentPage: 1,
      totalPages: 1,
      hasMore: false
    }
  };
}

/**
 * 检查响应是否支持分页
 */
export function isApiPaginationSupported(response: any): boolean {
  return response && 
         typeof response === 'object' && 
         response.pager && 
         typeof response.pager.total === 'number';
}

/**
 * 构建兼容的分页参数
 * 当API不支持分页时，使用默认参数
 */
export function buildCompatiblePagerParams(
  page: number, 
  size: number,
  supportsPagination: boolean = true
): any {
  if (!supportsPagination) {
    // 如果不支持分页，只传递基础参数
    return {
      page: 1,
      size: 100, // 尝试获取更多数据
      count: 0
    };
  }
  
  return {
    page,
    size,
    count: 0,
    sort: 'updated_time',
    order: 'desc'
  };
}