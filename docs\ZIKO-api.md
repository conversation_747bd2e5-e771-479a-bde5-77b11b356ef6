---
title: ZIKO

---

# ZIKO

Base URLs:

* <a href="http://localhost:9380">测试环境: http://localhost:9380</a>

# Authentication

# App/App - Search & Retrieval

## POST 创建搜索

POST /v1/search/create

创建搜索配置

> Body 请求参数

```json
{
  "description": "string",
  "filters": {},
  "kb_ids": [
    "string"
  ],
  "name": "string",
  "rerank_model": "string",
  "retrieval_method": "vector",
  "similarity_threshold": 0.7,
  "top_k": 5
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» description|body|string| 否 |搜索配置描述|
|» filters|body|object| 否 |过滤条件|
|» kb_ids|body|[string]| 是 |知识库ID列表|
|» name|body|string| 是 |搜索配置名称|
|» rerank_model|body|string| 否 |重排序模型ID|
|» retrieval_method|body|string| 否 |检索方法|
|» similarity_threshold|body|number| 否 |相似度阈值|
|» top_k|body|integer| 否 |返回结果数量|

#### 枚举值

|属性|值|
|---|---|
|» retrieval_method|vector|
|» retrieval_method|keyword|
|» retrieval_method|hybrid|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "search_id": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|创建成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» search_id|string|false|none||搜索配置ID|
|» message|string|false|none||消息|

## GET 搜索详情

GET /v1/search/detail

获取搜索配置详情

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|search_id|query|string| 是 |搜索配置ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "create_time": "2019-08-24T14:15:22Z",
    "description": "string",
    "filters": {},
    "id": "string",
    "kb_ids": [
      "string"
    ],
    "name": "string",
    "rerank_model": "string",
    "retrieval_method": "string",
    "similarity_threshold": 0,
    "top_k": 0,
    "update_time": "2019-08-24T14:15:22Z"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» create_time|string(date-time)|false|none||创建时间|
|»» description|string|false|none||搜索配置描述|
|»» filters|object|false|none||过滤条件|
|»» id|string|false|none||搜索配置ID|
|»» kb_ids|[string]|false|none||知识库ID列表|
|»» name|string|false|none||搜索配置名称|
|»» rerank_model|string|false|none||重排序模型ID|
|»» retrieval_method|string|false|none||检索方法|
|»» similarity_threshold|number|false|none||相似度阈值|
|»» top_k|integer|false|none||返回结果数量|
|»» update_time|string(date-time)|false|none||更新时间|
|» message|string|false|none||消息|

## POST 搜索列表

POST /v1/search/list

获取搜索配置列表

> Body 请求参数

```json
{
  "keywords": "string",
  "page": 1,
  "page_size": 10,
  "retrieval_method": "vector"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» keywords|body|string| 否 |搜索关键词|
|» page|body|integer| 否 |页码|
|» page_size|body|integer| 否 |每页数量|
|» retrieval_method|body|string| 否 |检索方法筛选|

#### 枚举值

|属性|值|
|---|---|
|» retrieval_method|vector|
|» retrieval_method|keyword|
|» retrieval_method|hybrid|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "page": 0,
    "page_size": 0,
    "search_configs": [
      {
        "create_time": "2019-08-24T14:15:22Z",
        "description": "string",
        "id": "string",
        "kb_count": 0,
        "name": "string",
        "retrieval_method": "string",
        "update_time": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» page|integer|false|none||当前页码|
|»» page_size|integer|false|none||每页数量|
|»» search_configs|[object]|false|none||搜索配置列表|
|»»» create_time|string(date-time)|false|none||创建时间|
|»»» description|string|false|none||搜索配置描述|
|»»» id|string|false|none||搜索配置ID|
|»»» kb_count|integer|false|none||关联知识库数量|
|»»» name|string|false|none||搜索配置名称|
|»»» retrieval_method|string|false|none||检索方法|
|»»» update_time|string(date-time)|false|none||更新时间|
|»» total|integer|false|none||总数|
|» message|string|false|none||消息|

## POST 删除搜索

POST /v1/search/rm

删除搜索配置

> Body 请求参数

```json
{
  "search_ids": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» search_ids|body|[string]| 是 |搜索配置ID列表|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "deleted_count": 0,
    "deleted_ids": [
      "string"
    ]
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|删除成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» deleted_count|integer|false|none||删除数量|
|»» deleted_ids|[string]|false|none||已删除的搜索配置ID列表|
|» message|string|false|none||消息|

## POST 更新搜索

POST /v1/search/update

更新搜索配置

> Body 请求参数

```json
{
  "description": "string",
  "filters": {},
  "kb_ids": [
    "string"
  ],
  "name": "string",
  "rerank_model": "string",
  "retrieval_method": "vector",
  "search_id": "string",
  "similarity_threshold": 1,
  "top_k": 1
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» description|body|string| 否 |搜索配置描述|
|» filters|body|object| 否 |过滤条件|
|» kb_ids|body|[string]| 否 |知识库ID列表|
|» name|body|string| 否 |搜索配置名称|
|» rerank_model|body|string| 否 |重排序模型ID|
|» retrieval_method|body|string| 否 |检索方法|
|» search_id|body|string| 是 |搜索配置ID|
|» similarity_threshold|body|number| 否 |相似度阈值|
|» top_k|body|integer| 否 |返回结果数量|

#### 枚举值

|属性|值|
|---|---|
|» retrieval_method|vector|
|» retrieval_method|keyword|
|» retrieval_method|hybrid|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "search_id": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|更新成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» search_id|string|false|none||搜索配置ID|
|» message|string|false|none||消息|

# App/App - Knowledge Management

## DELETE 删除知识图谱

DELETE /v1/kb/<kb_id>/knowledge_graph

删除知识图谱

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|kb_id|path|string| 是 |知识库ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "kb_id": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|删除成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» kb_id|string|false|none||知识库ID|
|» message|string|false|none||消息|

## GET 知识图谱

GET /v1/kb/<kb_id>/knowledge_graph

获取知识图谱

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|kb_id|path|string| 是 |知识库ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "edges": [
      {
        "relation": "string",
        "source": "string",
        "target": "string",
        "weight": 0
      }
    ],
    "nodes": [
      {
        "id": "string",
        "label": "string",
        "properties": {},
        "type": "string"
      }
    ]
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» edges|[object]|false|none||图谱边|
|»»» relation|string|false|none||关系类型|
|»»» source|string|false|none||源节点ID|
|»»» target|string|false|none||目标节点ID|
|»»» weight|number|false|none||权重|
|»» nodes|[object]|false|none||图谱节点|
|»»» id|string|false|none||节点ID|
|»»» label|string|false|none||节点标签|
|»»» properties|object|false|none||节点属性|
|»»» type|string|false|none||节点类型|
|» message|string|false|none||消息|

## POST 重命名文档块标签

POST /v1/kb/<kb_id>/rename_tag

重命名文档块标签。此操作会更新指定知识库中所有文档块的指定标签名称。

> Body 请求参数

```json
{
  "from_tag": "string",
  "to_tag": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|kb_id|path|string| 是 |知识库ID|
|body|body|object| 否 |none|
|» from_tag|body|string| 是 |原标签名|
|» to_tag|body|string| 是 |新标签名|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": true,
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|重命名成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|boolean|false|none||操作结果|
|» message|string|false|none||消息|

## POST 删除文档块标签

POST /v1/kb/<kb_id>/rm_tags

删除文档块标签。此操作会从指定知识库的所有文档块中移除指定的标签。

> Body 请求参数

```json
{
  "tags": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|kb_id|path|string| 是 |知识库ID|
|body|body|object| 否 |none|
|» tags|body|[string]| 是 |要删除的标签名称列表|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": true,
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|删除成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|boolean|false|none||操作结果|
|» message|string|false|none||消息|

## GET 获取文档块标签统计

GET /v1/kb/<kb_id>/tags

获取指定知识库中所有文档块的标签统计。这些标签是通过标签集自动分配给文档块的，用于RAG检索增强。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|kb_id|path|string| 是 |知识库ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": [
    [
      "技术文档",
      15
    ],
    [
      "产品介绍",
      8
    ],
    [
      "用户指南",
      23
    ]
  ],
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|[array]|false|none||文档块标签列表，每个元素为[标签名, 使用该标签的文档块数量]的数组|

*oneOf*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|»» *anonymous*|string|false|none||none|

*xor*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|»» *anonymous*|integer|false|none||none|

*continued*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|message|string|false|none||消息|

## POST 创建知识库

POST /v1/kb/create

创建知识库

> Body 请求参数

```json
{
  "chunk_method": "naive",
  "description": "string",
  "embedding_model": "string",
  "language": "zh",
  "name": "string",
  "permission": "private",
  "tags": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» chunk_method|body|string| 否 |分块方法|
|» description|body|string| 否 |知识库描述|
|» embedding_model|body|string| 否 |嵌入模型ID|
|» language|body|string| 否 |语言设置|
|» name|body|string| 是 |知识库名称|
|» permission|body|string| 否 |权限设置|
|» tags|body|[string]| 否 |知识库标签|

#### 枚举值

|属性|值|
|---|---|
|» chunk_method|naive|
|» chunk_method|recursive|
|» chunk_method|semantic|
|» language|zh|
|» language|en|
|» language|auto|
|» permission|private|
|» permission|public|
|» permission|team|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "kb_id": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|创建成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» kb_id|string|false|none||知识库ID|
|» message|string|false|none||消息|

## GET 知识库详情

GET /v1/kb/detail

获取知识库详情

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|kb_id|query|string| 是 |知识库ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "chunk_count": 0,
    "chunk_method": "string",
    "create_time": "2019-08-24T14:15:22Z",
    "description": "string",
    "document_count": 0,
    "embedding_model": "string",
    "id": "string",
    "language": "string",
    "name": "string",
    "permission": "string",
    "size": 0,
    "tags": [
      "string"
    ],
    "update_time": "2019-08-24T14:15:22Z"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» chunk_count|integer|false|none||分块数量|
|»» chunk_method|string|false|none||分块方法|
|»» create_time|string(date-time)|false|none||创建时间|
|»» description|string|false|none||知识库描述|
|»» document_count|integer|false|none||文档数量|
|»» embedding_model|string|false|none||嵌入模型ID|
|»» id|string|false|none||知识库ID|
|»» language|string|false|none||语言设置|
|»» name|string|false|none||知识库名称|
|»» permission|string|false|none||权限设置|
|»» size|integer|false|none||知识库大小（字节）|
|»» tags|[string]|false|none||知识库标签|
|»» update_time|string(date-time)|false|none||更新时间|
|» message|string|false|none||消息|

## POST 知识库列表

POST /v1/kb/list

获取知识库列表

> Body 请求参数

```json
{
  "keywords": "string",
  "page": 1,
  "page_size": 10,
  "orderby": "private",
  "desc": true,
  "role": "string",
  "tags": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» keywords|body|string| 否 |搜索关键词|
|» page|body|integer| 否 |页码|
|» page_size|body|integer| 否 |每页数量|
|» orderby|body|string| 否 |筛选条件|
|» desc|body|boolean| 是 |按创建时间倒序|
|» role|body|string| 是 |角色筛选|
|» tags|body|[string]| 否 |标签筛选，多个标签用逗号分隔|

#### 枚举值

|属性|值|
|---|---|
|» orderby|private|
|» orderby|public|
|» orderby|team|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "kbs": [
      {
        "chunk_count": 0,
        "create_time": "2019-08-24T14:15:22Z",
        "creator_name": "string",
        "creator_role": "string",
        "description": "string",
        "document_count": 0,
        "id": "string",
        "name": "string",
        "permission": "string",
        "tags": [
          "string"
        ],
        "update_time": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» kbs|[object]|false|none||知识库列表|
|»»» chunk_count|integer|false|none||分块数量|
|»»» create_time|string(date-time)|false|none||创建时间|
|»»» creator_name|string|false|none||创建人名称|
|»»» creator_role|string|false|none||创建人角色|
|»»» description|string|false|none||知识库描述|
|»»» document_count|integer|false|none||文档数量|
|»»» id|string|false|none||知识库ID|
|»»» name|string|false|none||知识库名称|
|»»» permission|string|false|none||权限设置|
|»»» tags|[string]|false|none||知识库标签|
|»»» update_time|string(date-time)|false|none||更新时间|
|»» total|integer|false|none||总数|
|» message|string|false|none||消息|

## POST 删除知识库

POST /v1/kb/rm

删除知识库

> Body 请求参数

```json
{
  "kb_ids": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» kb_ids|body|[string]| 是 |知识库ID列表|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "deleted_count": 0,
    "deleted_ids": [
      "string"
    ]
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|删除成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» deleted_count|integer|false|none||删除数量|
|»» deleted_ids|[string]|false|none||已删除的知识库ID列表|
|» message|string|false|none||消息|

## GET 获取指定知识库文档块的标签统计

GET /v1/kb/tags

获取指定知识库文档块的标签统计

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|kb_ids|query|string| 是 |知识库ID列表，用逗号分隔|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": [
    [
      "技术文档",
      15
    ],
    [
      "产品介绍",
      8
    ],
    [
      "用户指南",
      23
    ]
  ],
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|[array]|false|none||文档块标签列表，每个元素为[标签名, 使用该标签的文档块数量]的数组|

*oneOf*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|»» *anonymous*|string|false|none||none|

*xor*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|»» *anonymous*|integer|false|none||none|

*continued*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|message|string|false|none||消息|

## GET 获取所有知识库的标签

GET /v1/kb/kb_tags

获取所有知识库的标签

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "tags": [
      "string"
    ]
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||标签数据|
|»» tags|[string]|false|none||标签列表|
|» message|string|false|none||消息|

## POST 更新知识库

POST /v1/kb/update

更新知识库

> Body 请求参数

```json
{
  "chunk_method": "naive",
  "description": "string",
  "embedding_model": "string",
  "kb_id": "string",
  "language": "zh",
  "name": "string",
  "permission": "private",
  "tags": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» chunk_method|body|string| 否 |分块方法|
|» description|body|string| 否 |知识库描述|
|» embedding_model|body|string| 否 |嵌入模型ID|
|» kb_id|body|string| 是 |知识库ID|
|» language|body|string| 否 |语言设置|
|» name|body|string| 否 |知识库名称|
|» permission|body|string| 否 |权限设置|
|» tags|body|[string]| 否 |知识库标签|

#### 枚举值

|属性|值|
|---|---|
|» chunk_method|naive|
|» chunk_method|recursive|
|» chunk_method|semantic|
|» language|zh|
|» language|en|
|» language|auto|
|» permission|private|
|» permission|public|
|» permission|team|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "kb_id": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|更新成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» kb_id|string|false|none||知识库ID|
|» message|string|false|none||消息|

# App/App - File to Document Conversion

## POST 转换文件为文档

POST /v1/file2document/convert

将指定的文件转换为文档并关联到知识库

> Body 请求参数

```json
{
  "file_ids": [
    "string"
  ],
  "kb_ids": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» file_ids|body|[string]| 否 |要转换的文件或文件夹ID列表|
|» kb_ids|body|[string]| 是 |目标知识库ID列表|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": [
    {
      "create_time": "2019-08-24T14:15:22Z",
      "document_id": "string",
      "file_id": "string",
      "kb_id": "string",
      "status": "string"
    }
  ],
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|文件转换成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|认证失败|None|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|文件或知识库未找到|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|[object]|false|none||文件转换结果列表|
|»» create_time|string(date-time)|false|none||创建时间|
|»» document_id|string|false|none||转换后的文档ID|
|»» file_id|string|false|none||文件ID|
|»» kb_id|string|false|none||知识库ID|
|»» status|string|false|none||转换状态|
|» message|string|false|none||消息|

状态码 **400**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||错误码|
|» message|string|false|none||错误信息|

# App/App - File Management

## GET 获取所有父文件夹

GET /v1/file/all_parent_folder

获取所有父文件夹

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": [
    {
      "create_time": "2019-08-24T14:15:22Z",
      "id": "string",
      "name": "string",
      "parent_id": "string",
      "path": "string"
    }
  ],
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|[object]|false|none||父文件夹列表|
|»» create_time|string(date-time)|false|none||创建时间|
|»» id|string|false|none||文件夹ID|
|»» name|string|false|none||文件夹名称|
|»» parent_id|string|false|none||父文件夹ID|
|»» path|string|false|none||文件夹路径|
|» message|string|false|none||消息|

## POST 创建文件

POST /v1/file/create

创建文件或文件夹

> Body 请求参数

```json
{
  "description": "string",
  "name": "string",
  "parent_id": "string",
  "tags": [
    "string"
  ],
  "type": "file"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» description|body|string| 否 |描述|
|» name|body|string| 是 |文件/文件夹名称|
|» parent_id|body|string| 否 |父文件夹ID|
|» tags|body|[string]| 否 |标签列表|
|» type|body|string| 是 |类型（文件或文件夹）|

#### 枚举值

|属性|值|
|---|---|
|» type|file|
|» type|folder|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "id": "string",
    "name": "string",
    "type": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|创建成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» id|string|false|none||文件/文件夹ID|
|»» name|string|false|none||名称|
|»» type|string|false|none||类型|
|» message|string|false|none||消息|

## GET 获取文件

GET /v1/file/get/<file_id>

获取文件详情

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|file_id|path|string| 是 |文件ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "content": "string",
    "create_time": "2019-08-24T14:15:22Z",
    "id": "string",
    "mime_type": "string",
    "name": "string",
    "parent_id": "string",
    "path": "string",
    "size": 0,
    "type": "string",
    "update_time": "2019-08-24T14:15:22Z"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» content|string|false|none||文件内容|
|»» create_time|string(date-time)|false|none||创建时间|
|»» id|string|false|none||文件ID|
|»» mime_type|string|false|none||MIME类型|
|»» name|string|false|none||文件名称|
|»» parent_id|string|false|none||父文件夹ID|
|»» path|string|false|none||文件路径|
|»» size|integer|false|none||文件大小（字节）|
|»» type|string|false|none||文件类型|
|»» update_time|string(date-time)|false|none||更新时间|
|» message|string|false|none||消息|

## GET 文件列表

GET /v1/file/list

获取文件列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|parent_id|query|string| 否 |父文件夹ID|
|keywords|query|string| 否 |关键字|
|page|query|integer| 否 |页码|
|page_size|query|integer| 否 |每页数量|
|tags|query|string| 否 |标签过滤，多个标签用逗号分隔|
|orderby|query|string| 否 |筛选条件|
|desc|query|string| 否 |按创建时间倒序|
|role|query|string| 否 |角色筛选|

#### 枚举值

|属性|值|
|---|---|
|keywords|file|
|keywords|folder|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "files": [
      {
        "create_date": "string",
        "create_time": "2019-08-24T14:15:22Z",
        "created_by": "string",
        "creator_name": "string",
        "creator_role": "string",
        "description": "string",
        "has_child_folder": true,
        "id": "string",
        "kbs_info": [
          "string"
        ],
        "location": "string",
        "mime_type": "string",
        "name": "string",
        "parent_id": "string",
        "size": 0,
        "file_count": 0,
        "source_type": "string",
        "tags": "string",
        "tenant_id": "string",
        "type": "string",
        "update_date": "string",
        "update_time": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» files|[object]|false|none||文件列表|
|»»» create_date|string|false|none||创建日期|
|»»» create_time|string(date-time)|false|none||创建时间|
|»»» created_by|string|false|none||创建人ID|
|»»» creator_name|string|false|none||创建人名称|
|»»» creator_role|string|false|none||创建人角色|
|»»» description|string¦null|false|none||描述|
|»»» has_child_folder|boolean|false|none||是否有子文件夹|
|»»» id|string|false|none||文件ID|
|»»» kbs_info|[string]|false|none||关联知识库信息|
|»»» location|string|false|none||文件路径或位置|
|»»» mime_type|string|false|none||MIME类型|
|»»» name|string|false|none||文件名称|
|»»» parent_id|string|false|none||父文件夹ID|
|»»» size|integer|false|none||文件大小|
|»»» file_count|integer|true|none||文件数量|
|»»» source_type|string|false|none||来源类型|
|»»» tags|string¦null|false|none||标签|
|»»» tenant_id|string|false|none||租户ID|
|»»» type|string|false|none||文件类型|
|»»» update_date|string|false|none||更新日期|
|»»» update_time|string(date-time)|false|none||更新时间|
|»» total|integer|false|none||总数|
|» message|string|false|none||消息|

## POST 移动文件

POST /v1/file/mv

移动文件或文件夹

> Body 请求参数

```json
{
  "file_id": "string",
  "target_parent_id": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» file_id|body|string| 是 |文件ID|
|» target_parent_id|body|string| 是 |目标父文件夹ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "file_id": "string",
    "new_path": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|移动成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» file_id|string|false|none||文件ID|
|»» new_path|string|false|none||新路径|
|» message|string|false|none||消息|

## GET 获取父文件夹

GET /v1/file/parent_folder

获取当前父文件夹

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|file_id|query|string| 是 |文件ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "id": "string",
    "name": "string",
    "path": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» id|string|false|none||父文件夹ID|
|»» name|string|false|none||父文件夹名称|
|»» path|string|false|none||父文件夹路径|
|» message|string|false|none||消息|

## POST 编辑文件或文件夹

POST /v1/file/edit

编辑文件或文件夹

> Body 请求参数

```json
{
  "description": "string",
  "file_id": "string",
  "new_name": "string",
  "tags": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» description|body|string| 否 |描述|
|» file_id|body|string| 是 |文件ID|
|» new_name|body|string| 是 |新名称|
|» tags|body|[string]| 否 |标签列表|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "file_id": "string",
    "new_name": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|编辑成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» file_id|string|false|none||文件ID|
|»» new_name|string|false|none||新名称|
|» message|string|false|none||消息|

## POST 删除文件

POST /v1/file/rm

删除文件或文件夹

> Body 请求参数

```json
{
  "file_ids": [
    "string"
  ],
  "force": false
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» file_ids|body|[string]| 是 |文件ID列表|
|» force|body|boolean| 否 |是否强制删除（删除非空文件夹）|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "deleted_count": 0,
    "deleted_ids": [
      "string"
    ]
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|删除成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» deleted_count|integer|false|none||删除数量|
|»» deleted_ids|[string]|false|none||已删除的文件ID列表|
|» message|string|false|none||消息|

## GET 获取根文件夹

GET /v1/file/root_folder

获取根文件夹

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "id": "string",
    "name": "string",
    "path": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» id|string|false|none||根文件夹ID|
|»» name|string|false|none||根文件夹名称|
|»» path|string|false|none||根文件夹路径|
|» message|string|false|none||消息|

## POST 上传文件

POST /v1/file/upload

上传文件

> Body 请求参数

```yaml
description: ""
file: ""
parent_id: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» description|body|string| 否 |文件描述|
|» file|body|string(binary)| 是 |上传的文件|
|» parent_id|body|string| 否 |父文件夹ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "file_id": "string",
    "mime_type": "string",
    "name": "string",
    "size": 0
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|上传成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» file_id|string|false|none||文件ID|
|»» mime_type|string|false|none||MIME类型|
|»» name|string|false|none||文件名称|
|»» size|integer|false|none||文件大小|
|» message|string|false|none||消息|

## GET 获取文件夹所有标签

GET /v1/file/tags

获取当前租户下所有文件的标签列表

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "tags": [
      "string"
    ]
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» tags|[string]|false|none||标签列表|
|» message|string|false|none||消息|

# App/App - Document Management

## POST 更改解析器

POST /v1/document/change_parser

更改文档解析器

> Body 请求参数

```json
{
  "doc_id": "string",
  "parser_id": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» doc_id|body|string| 是 |文档ID|
|» parser_id|body|string| 是 |解析器ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "doc_id": "string",
    "parser_id": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|更改成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» doc_id|string|false|none||文档ID|
|»» parser_id|string|false|none||解析器ID|
|» message|string|false|none||消息|

## POST 更改状态

POST /v1/document/change_status

更改文档状态

> Body 请求参数

```json
{
  "doc_id": "string",
  "status": "active"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» doc_id|body|string| 是 |文档ID|
|» status|body|string| 是 |文档状态|

#### 枚举值

|属性|值|
|---|---|
|» status|active|
|» status|inactive|
|» status|processing|
|» status|failed|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "doc_id": "string",
    "status": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|状态更改成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» doc_id|string|false|none||文档ID|
|»» status|string|false|none||新状态|
|» message|string|false|none||消息|

## POST 创建文档

POST /v1/document/create

创建新文档

> Body 请求参数

```json
{
  "content": "string",
  "kb_id": "string",
  "metadata": {},
  "name": "string",
  "parser_id": "string",
  "type": "pdf"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» content|body|string| 否 |文档内容|
|» kb_id|body|string| 是 |知识库ID|
|» metadata|body|object| 否 |文档元数据|
|» name|body|string| 是 |文档名称|
|» parser_id|body|string| 否 |解析器ID|
|» type|body|string| 否 |文档类型|

#### 枚举值

|属性|值|
|---|---|
|» type|pdf|
|» type|docx|
|» type|txt|
|» type|md|
|» type|html|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "doc_id": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|创建成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» doc_id|string|false|none||文档ID|
|» message|string|false|none||消息|

## GET 获取文档

GET /v1/document/get/<doc_id>

获取文档详情

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|doc_id|path|string| 是 |文档ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "chunks_count": 0,
    "content": "string",
    "create_time": "2019-08-24T14:15:22Z",
    "id": "string",
    "kb_id": "string",
    "metadata": {},
    "name": "string",
    "parser_id": "string",
    "size": 0,
    "status": "string",
    "type": "string",
    "update_time": "2019-08-24T14:15:22Z"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» chunks_count|integer|false|none||分块数量|
|»» content|string|false|none||文档内容|
|»» create_time|string(date-time)|false|none||创建时间|
|»» id|string|false|none||文档ID|
|»» kb_id|string|false|none||知识库ID|
|»» metadata|object|false|none||文档元数据|
|»» name|string|false|none||文档名称|
|»» parser_id|string|false|none||解析器ID|
|»» size|integer|false|none||文档大小（字节）|
|»» status|string|false|none||文档状态|
|»» type|string|false|none||文档类型|
|»» update_time|string(date-time)|false|none||更新时间|
|» message|string|false|none||消息|

## GET 获取图片

GET /v1/document/image/<image_id>

获取文档中的图片

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|image_id|path|string| 是 |图片ID|

> 返回示例

> 200 Response

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|图片获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

## POST 文档信息

POST /v1/document/infos

批量获取文档信息

> Body 请求参数

```json
{
  "doc_ids": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» doc_ids|body|[string]| 是 |文档ID列表|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": [
    {
      "create_time": "2019-08-24T14:15:22Z",
      "id": "string",
      "name": "string",
      "size": 0,
      "status": "string",
      "type": "string"
    }
  ],
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|[object]|false|none||文档信息列表|
|»» create_time|string(date-time)|false|none||创建时间|
|»» id|string|false|none||文档ID|
|»» name|string|false|none||文档名称|
|»» size|integer|false|none||文档大小|
|»» status|string|false|none||文档状态|
|»» type|string|false|none||文档类型|
|» message|string|false|none||消息|

## POST 文档列表

POST /v1/document/list

获取文档列表

> Body 请求参数

```json
{
  "run_status": [
    "active"
  ],
  "types": [
    "pdf"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|kb_id|query|string| 是 |知识库ID|
|keywords|query|string| 否 |搜索关键词|
|page|query|integer| 否 |页码|
|page_size|query|integer| 否 |每页数量|
|orderby|query|string| 否 |排序字段|
|desc|query|string| 否 |是否降序排序|
|body|body|object| 否 |none|
|» run_status|body|[string]| 否 |状态筛选|
|» types|body|[string]| 否 |文档类型筛选|

#### 枚举值

|属性|值|
|---|---|
|» run_status|active|
|» run_status|inactive|
|» run_status|processing|
|» run_status|failed|
|» types|pdf|
|» types|docx|
|» types|txt|
|» types|md|
|» types|html|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "docs": [
      {
        "chunks_count": 0,
        "create_time": "2019-08-24T14:15:22Z",
        "id": "string",
        "name": "string",
        "size": 0,
        "status": "string",
        "thumbnail": "string",
        "type": "string",
        "update_time": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» docs|[object]|false|none||文档列表|
|»»» chunks_count|integer|false|none||分块数量|
|»»» create_time|string(date-time)|false|none||创建时间|
|»»» id|string|false|none||文档ID|
|»»» name|string|false|none||文档名称|
|»»» size|integer|false|none||文档大小|
|»»» status|string|false|none||文档状态|
|»»» thumbnail|string|false|none||缩略图|
|»»» type|string|false|none||文档类型|
|»»» update_time|string(date-time)|false|none||更新时间|
|»» total|integer|false|none||总数|
|» message|string|false|none||消息|

## POST 解析文档

POST /v1/document/parse

解析文档

> Body 请求参数

```json
{
  "chunk_overlap": 50,
  "chunk_size": 1024,
  "doc_id": "string",
  "parser_id": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» chunk_overlap|body|integer| 否 |分块重叠|
|» chunk_size|body|integer| 否 |分块大小|
|» doc_id|body|string| 是 |文档ID|
|» parser_id|body|string| 否 |解析器ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "doc_id": "string",
    "status": "string",
    "task_id": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|解析成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» doc_id|string|false|none||文档ID|
|»» status|string|false|none||解析状态|
|»» task_id|string|false|none||解析任务ID|
|» message|string|false|none||消息|

## POST 重命名文档

POST /v1/document/rename

重命名文档

> Body 请求参数

```json
{
  "doc_id": "string",
  "new_name": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» doc_id|body|string| 是 |文档ID|
|» new_name|body|string| 是 |新文档名称|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "doc_id": "string",
    "new_name": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|重命名成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» doc_id|string|false|none||文档ID|
|»» new_name|string|false|none||新名称|
|» message|string|false|none||消息|

## POST 删除文档

POST /v1/document/rm

删除文档

> Body 请求参数

```json
{
  "doc_ids": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» doc_ids|body|[string]| 是 |文档ID列表|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "deleted_count": 0,
    "deleted_ids": [
      "string"
    ]
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|删除成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» deleted_count|integer|false|none||删除数量|
|»» deleted_ids|[string]|false|none||已删除的文档ID列表|
|» message|string|false|none||消息|

## POST 运行任务

POST /v1/document/run

运行文档处理任务

> Body 请求参数

```json
{
  "doc_id": "string",
  "parameters": {},
  "task_type": "parse"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» doc_id|body|string| 是 |文档ID|
|» parameters|body|object| 否 |任务参数|
|» task_type|body|string| 是 |任务类型|

#### 枚举值

|属性|值|
|---|---|
|» task_type|parse|
|» task_type|extract|
|» task_type|analyze|
|» task_type|summarize|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "status": "string",
    "task_id": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|任务启动成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» status|string|false|none||任务状态|
|»» task_id|string|false|none||任务ID|
|» message|string|false|none||消息|

## POST 设置元数据

POST /v1/document/set_meta

设置文档元数据

> Body 请求参数

```json
{
  "doc_id": "string",
  "metadata": {
    "author": "string",
    "custom_fields": {},
    "description": "string",
    "tags": [
      "string"
    ],
    "title": "string"
  }
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» doc_id|body|string| 是 |文档ID|
|» metadata|body|object| 是 |元数据|
|»» author|body|string| 否 |作者|
|»» custom_fields|body|object| 否 |自定义字段|
|»» description|body|string| 否 |描述|
|»» tags|body|[string]| 否 |标签|
|»» title|body|string| 否 |标题|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "doc_id": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|设置成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» doc_id|string|false|none||文档ID|
|» message|string|false|none||消息|

## GET 缩略图列表

GET /v1/document/thumbnails

获取文档缩略图列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|doc_id|query|string| 否 |文档ID|
|page|query|integer| 否 |页码|
|page_size|query|integer| 否 |每页数量|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "thumbnails": [
      {
        "doc_id": "string",
        "page_number": 0,
        "thumbnail_url": "string"
      }
    ],
    "total": 0
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|获取成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» thumbnails|[object]|false|none||缩略图列表|
|»»» doc_id|string|false|none||文档ID|
|»»» page_number|integer|false|none||页码|
|»»» thumbnail_url|string|false|none||缩略图URL|
|»» total|integer|false|none||总数|
|» message|string|false|none||消息|

## POST 上传文档

POST /v1/document/upload

上传文档文件

> Body 请求参数

```yaml
chunk_overlap: 50
chunk_size: 1024
file: ""
kb_id: ""
parser_id: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» chunk_overlap|body|integer| 否 |分块重叠|
|» chunk_size|body|integer| 否 |分块大小|
|» file|body|string(binary)| 是 |文档文件|
|» kb_id|body|string| 是 |知识库ID|
|» parser_id|body|string| 否 |解析器ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "doc_id": "string",
    "name": "string",
    "size": 0
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|上传成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» doc_id|string|false|none||文档ID|
|»» name|string|false|none||文档名称|
|»» size|integer|false|none||文件大小|
|» message|string|false|none||消息|

## POST 上传并解析

POST /v1/document/upload_and_parse

上传文档并立即解析

> Body 请求参数

```yaml
chunk_overlap: 50
chunk_size: 1024
file: ""
kb_id: ""
parser_id: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» chunk_overlap|body|integer| 否 |分块重叠|
|» chunk_size|body|integer| 否 |分块大小|
|» file|body|string(binary)| 是 |文档文件|
|» kb_id|body|string| 是 |知识库ID|
|» parser_id|body|string| 否 |解析器ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "doc_id": "string",
    "status": "string",
    "task_id": "string"
  },
  "message": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|上传并解析成功|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|参数错误|None|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权|None|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|None|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||状态码|
|» data|object|false|none||none|
|»» doc_id|string|false|none||文档ID|
|»» status|string|false|none||解析状态|
|»» task_id|string|false|none||解析任务ID|
|» message|string|false|none||消息|

# 数据模型

