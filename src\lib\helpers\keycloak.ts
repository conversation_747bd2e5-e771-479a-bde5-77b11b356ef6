// 最简单的Keycloak登录实现
import authorizationUtil from '@/utils/authorization-util';


const KEYCLOAK_CONFIG = {
  authority: (typeof process !== 'undefined' && process.env?.KEYCLOAK_HOST) || 'http://localhost:8080',
  realm: (typeof process !== 'undefined' && process.env?.KEYCLOAK_REALM) || 'ragflow',
  clientId: (typeof process !== 'undefined' && process.env?.KEYCLOAK_CLIENT_ID) || 'ragclient',
};

export function keycloakLogin(): void {
  // 构建登录URL
  const loginUrl = new URL(
    `${KEYCLOAK_CONFIG.authority}/realms/${KEYCLOAK_CONFIG.realm}/protocol/openid-connect/auth`,
  );

  // 获取当前页面的完整URL作为重定向URI
  const currentOrigin = window.location.origin;
  const redirectUri = currentOrigin + '/login';

  // 基本参数
  loginUrl.searchParams.append('client_id', KEYCLOAK_CONFIG.clientId);
  loginUrl.searchParams.append('redirect_uri', redirectUri);
  loginUrl.searchParams.append('response_type', 'code');
  loginUrl.searchParams.append('scope', 'openid profile email organization');

  console.log('跳转到Keycloak登录:', {
    keycloakUrl: loginUrl.toString(),
    redirectUri,
    currentOrigin,
    keycloakAuthority: KEYCLOAK_CONFIG.authority,
  });

  // 直接跳转
  window.location.href = loginUrl.toString();
}

/**
 * 使用授权码交换token
 */
async function exchangeCodeForToken(code: string): Promise<{
  success: boolean;
  access_token?: string;
  refresh_token?: string;
  expires_in?: number;
  token_type?: string;
  userInfo?: any;
  error?: string;
}> {
  try {
    console.log('开始token交换...');

    // 构建token端点URL
    const tokenUrl = `${KEYCLOAK_CONFIG.authority}/realms/${KEYCLOAK_CONFIG.realm}/protocol/openid-connect/token`;

    // 获取重定向URI
    const redirectUri = window.location.origin + '/login';

    // 构建请求参数
    const formData = new URLSearchParams();
    formData.append('grant_type', 'authorization_code');
    formData.append('client_id', KEYCLOAK_CONFIG.clientId);
    formData.append('code', code);
    formData.append('redirect_uri', redirectUri);

    console.log('Token交换请求:', {
      tokenUrl,
      redirectUri,
      clientId: KEYCLOAK_CONFIG.clientId,
      hasCode: !!code,
    });

    // 发送token请求
    const response = await fetch(tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData.toString(),
    });

    console.log('Token交换响应:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Token交换失败:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      });

      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}\n${errorText}`,
      };
    }

    const tokenData = await response.json();

    console.log('Token交换成功:', {
      hasAccessToken: !!tokenData.access_token,
      hasRefreshToken: !!tokenData.refresh_token,
      expiresIn: tokenData.expires_in,
      tokenType: tokenData.token_type,
    });

    // 可选：获取用户信息
    let userInfo = null;
    try {
      if (tokenData.access_token) {
        const userInfoUrl = `${KEYCLOAK_CONFIG.authority}/realms/${KEYCLOAK_CONFIG.realm}/protocol/openid-connect/userinfo`;
        const userInfoResponse = await fetch(userInfoUrl, {
          headers: {
            Authorization: `Bearer ${tokenData.access_token}`,
          },
        });

        if (userInfoResponse.ok) {
          userInfo = await userInfoResponse.json();
          console.log('获取用户信息成功:', {
            hasUserInfo: !!userInfo,
            userName: userInfo?.name || userInfo?.preferred_username,
            email: userInfo?.email,
          });
        }
      }
    } catch (error) {
      console.warn('获取用户信息失败，将使用默认信息:', error);
    }

    return {
      success: true,
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
      expires_in: tokenData.expires_in,
      token_type: tokenData.token_type,
      userInfo: userInfo
        ? {
            avatar: userInfo.picture || '',
            name:
              userInfo.name || userInfo.preferred_username || 'Keycloak User',
            email: userInfo.email || '<EMAIL>',
          }
        : null,
    };
  } catch (error: any) {
    console.error('Token交换异常:', error);
    return {
      success: false,
      error: error?.message || String(error),
    };
  }
}

/**
 * 最简单的回调处理
 */
export async function handleCallback(): Promise<boolean> {
  console.log('开始处理Keycloak回调...');

  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');
  const error = urlParams.get('error');
  const errorDescription = urlParams.get('error_description');

  console.log('回调参数:', {
    hasCode: !!code,
    hasError: !!error,
    code: code ? code.substring(0, 15) + '...' : null,
    error,
    errorDescription,
    fullUrl: window.location.href,
  });

  if (error) {
    console.error('Keycloak登录错误:', {
      error,
      errorDescription,
      timestamp: new Date().toISOString(),
    });

    // 保存错误信息供调试
    localStorage.setItem(
      'keycloakLastError',
      JSON.stringify({
        error,
        errorDescription,
        timestamp: new Date().toISOString(),
        url: window.location.href,
      }),
    );

    return false;
  }

  if (code) {
    console.log('收到授权码，开始处理登录...', {
      codeLength: code.length,
      codePrefix: code.substring(0, 15) + '...',
      timestamp: new Date().toISOString(),
    });

    try {
      // 🎯 使用授权码换取真实token
      const tokenResponse = await exchangeCodeForToken(code);

      if (tokenResponse.success && tokenResponse.access_token) {
        console.log('成功获取token:', {
          hasAccessToken: !!tokenResponse.access_token,
          hasRefreshToken: !!tokenResponse.refresh_token,
          expiresIn: tokenResponse.expires_in,
          tokenType: tokenResponse.token_type,
        });

        // 设置登录状态
        localStorage.setItem('userStatus', 'login');

        // 设置真实的Authorization头
        authorizationUtil.setAuthorization(
          `Bearer ${tokenResponse.access_token}`,
        );

        // 如果有用户信息，使用真实信息，否则使用默认信息
        const userInfo = tokenResponse.userInfo || {
          avatar: '',
          name: 'Keycloak User',
          email: '<EMAIL>',
        };

        authorizationUtil.setUserInfo(userInfo);

        // 保存token信息供后续使用
        localStorage.setItem('keycloakAccessToken', tokenResponse.access_token);
        if (tokenResponse.refresh_token) {
          localStorage.setItem(
            'keycloakRefreshToken',
            tokenResponse.refresh_token,
          );
        }

        console.log('已设置认证状态:', {
          userInfo,
          hasAccessToken: true,
          tokenLength: tokenResponse.access_token.length,
          userStatus: 'login',
        });

        // 保存登录成功信息
        localStorage.setItem(
          'keycloakLoginSuccess',
          JSON.stringify({
            timestamp: new Date().toISOString(),
            code: code.substring(0, 15) + '...',
            userInfo,
            hasRealToken: true,
            tokenLength: tokenResponse.access_token.length,
          }),
        );

        // 清除URL参数
        console.log('清理URL参数...');
        window.history.replaceState(
          {},
          document.title,
          window.location.pathname,
        );

        console.log('Keycloak登录处理完成', {
          userStatus: localStorage.getItem('userStatus'),
          hasUserInfo: !!authorizationUtil.getUserInfo(),
          hasRealToken: true,
          timestamp: new Date().toISOString(),
        });

        // 清理错误记录
        localStorage.removeItem('keycloakLastError');

        return true;
      } else {
        console.error('Token交换失败:', tokenResponse.error);
        localStorage.setItem(
          'keycloakLastError',
          JSON.stringify({
            error: 'Token交换失败',
            details: tokenResponse.error,
            timestamp: new Date().toISOString(),
          }),
        );
        return false;
      }
    } catch (error: any) {
      console.error('处理登录时发生错误:', error);
      localStorage.setItem(
        'keycloakProcessingError',
        JSON.stringify({
          error: error?.message || String(error),
          timestamp: new Date().toISOString(),
        }),
      );
      return false;
    }
  }

  console.log('没有找到有效的回调参数');
  return false;
}

/**
 * 清理所有 Keycloak 相关缓存
 */
export function clearKeycloakCache(): void {
  console.log('清理所有 Keycloak 相关缓存...');

  // 清理所有 Keycloak 相关的 localStorage 项
  const keycloakCacheKeys = [
    'userStatus',
    'keycloakAccessToken',
    'keycloakRefreshToken',
    'keycloakCallbackProcessing',
    'keycloakLoginSuccess',
    'keycloakLastError',
    'keycloakProcessingError',
    'keycloakLoginAttempt',
  ];

  keycloakCacheKeys.forEach((key) => {
    const value = localStorage.getItem(key);
    if (value) {
      localStorage.removeItem(key);
      console.log(`已清理 Keycloak 缓存: ${key}`);
    }
  });

  console.log('Keycloak 缓存清理完成');
}

/**
 * 直接重定向到 Keycloak 登出页面
 * 这是最可靠的 Keycloak 登出方式
 */
export function keycloakLogout(): void {
  console.log('开始 Keycloak 登出...');

  // 清理本地缓存
  clearKeycloakCache();

  // 构建 Keycloak 登出 URL
  const logoutUrl = new URL(
    `${KEYCLOAK_CONFIG.authority}/realms/${KEYCLOAK_CONFIG.realm}/protocol/openid-connect/logout`,
  );

  // 设置登出后重定向的 URL
  const redirectUri = window.location.origin + '/login';
  logoutUrl.searchParams.append('client_id', KEYCLOAK_CONFIG.clientId);
  logoutUrl.searchParams.append('post_logout_redirect_uri', redirectUri);

  console.log('重定向到 Keycloak 登出页面:', logoutUrl.toString());

  // 重定向到 Keycloak 登出页面
  window.location.href = logoutUrl.toString();
}

/**
 * 检查当前用户是否通过 Keycloak 登录
 */
export function isKeycloakUser(): boolean {
  // 检查是否存在 Keycloak 相关的缓存
  const keycloakToken = localStorage.getItem('keycloakAccessToken');
  const keycloakLoginSuccess = localStorage.getItem('keycloakLoginSuccess');
  const userStatus = localStorage.getItem('userStatus');

  return !!(keycloakToken || keycloakLoginSuccess || userStatus === 'login');
}

/**
 * 检查是否已登录
 */
export function isLoggedIn(): boolean {
  return localStorage.getItem('userStatus') === 'login';
}

export { KEYCLOAK_CONFIG };
