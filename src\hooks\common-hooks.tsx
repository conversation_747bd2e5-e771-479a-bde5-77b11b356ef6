"use client"
import { ExclamationCircleFilled } from '@ant-design/icons';
import { Modal } from 'antd';
import isEqual from 'lodash/isEqual';
import { ReactNode, useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { createRoot } from 'react-dom/client';

export const useSetModalState = () => {
  const [visible, setVisible] = useState(false);

  const showModal = useCallback(() => {
    setVisible(true);
  }, []);
  const hideModal = useCallback(() => {
    setVisible(false);
  }, []);

  const switchVisible = useCallback(() => {
    setVisible(!visible);
  }, [visible]);

  return { visible, showModal, hideModal, switchVisible };
};

export const useDeepCompareEffect = (
  effect: React.EffectCallback,
  deps: React.DependencyList,
) => {
  const ref = useRef<React.DependencyList>(null);
  let callback: ReturnType<React.EffectCallback> = () => {};
  if (!isEqual(deps, ref.current)) {
    callback = effect();
    ref.current = deps;
  }
  useEffect(() => {
    return () => {
      if (callback) {
        callback();
      }
    };
  }, []);
};

export interface UseDynamicSVGImportOptions {
  onCompleted?: (
    name: string,
    SvgIcon: React.FC<React.SVGProps<SVGSVGElement>> | undefined,
  ) => void;
  onError?: (err: Error) => void;
}
interface IProps {
  title?: string;
  content?: ReactNode;
  onOk?: (...args: any[]) => any;
  onCancel?: (...args: any[]) => any;
}

export const useShowDeleteConfirm = () => {
  const { t } = useTranslation();

  const showDeleteConfirm = useCallback(
    ({ title, content, onOk, onCancel }: IProps): Promise<number> => {
      return new Promise((resolve, reject) => {
        // 使用兼容React 19的方式创建Modal
        const container = document.createElement('div');
        document.body.appendChild(container);
        const root = createRoot(container);
        
        const handleOk = async () => {
          try {
            const ret = await onOk?.();
            resolve(ret);
            console.info(ret);
          } catch (error) {
            reject(error);
          } finally {
            root.unmount();
            document.body.removeChild(container);
          }
        };
        
        const handleCancel = () => {
          onCancel?.();
          root.unmount();
          document.body.removeChild(container);
        };
        
        root.render(
          <Modal
            open={true}
            title={title ?? t('common.deleteModalTitle')}
            onOk={handleOk}
            onCancel={handleCancel}
            okText={t('common.ok')}
            cancelText={t('common.cancel')}
            okType="danger"
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <ExclamationCircleFilled style={{ color: '#faad14', fontSize: '16px' }} />
              {content}
            </div>
          </Modal>
        );
      });
    },
    [t],
  );

  return showDeleteConfirm;
};

export const useShowFileManagerDeleteConfirm = () => {
  const { t } = useTranslation();

  const showDeleteConfirm = useCallback(
    ({ title, content, onOk, onCancel }: IProps): Promise<number> => {
      return new Promise((resolve, reject) => {
        // 使用兼容React 19的方式创建Modal
        const container = document.createElement('div');
        document.body.appendChild(container);
        const root = createRoot(container);
        
        const handleOk = async () => {
          try {
            const ret = await onOk?.();
            resolve(ret);
            console.info(ret);
          } catch (error) {
            reject(error);
          } finally {
            root.unmount();
            document.body.removeChild(container);
          }
        };
        
        const handleCancel = () => {
          onCancel?.();
          root.unmount();
          document.body.removeChild(container);
        };
        
        root.render(
          <Modal
            open={true}
            centered={true}
            width={480}
            className="custom-delete-modal"
            onOk={handleOk}
            onCancel={handleCancel}
            okText="删除"
            cancelText="取消"
            okType="danger"
            styles={{
              content: {
                borderRadius: '16px',
                padding: '24px',
              },
              header: {
                borderBottom: 'none',
                padding: '0',
                marginBottom: '16px',
              },
              body: {
                padding: '0',
              },
              footer: {
                borderTop: 'none',
                padding: '0',
                display: 'flex',
                justifyContent: 'flex-end',
                gap: '12px',
              },
            }}
            okButtonProps={{
              style: {
                height: '40px',
                padding: '8px 24px',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '500',
                backgroundColor: '#d54941',
                border: 'none',
                color: '#ffffff',
              },
            }}
            cancelButtonProps={{
              style: {
                height: '40px',
                padding: '8px 24px',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '500',
                backgroundColor: '#ffffff',
                border: '1px solid #e8e8e8',
                color: '#666666',
              },
            }}
          >
            <div>
              <div style={{ 
                fontSize: '20px', 
                fontWeight: '600', 
                color: '#333333',
                marginBottom: '16px'
              }}>
                删除文件组
              </div>
              <div style={{ 
                fontSize: '14px', 
                color: '#333333',
                lineHeight: '20px',
                marginBottom: '12px'
              }}>
                {content}
              </div>
            </div>
          </Modal>
        );
      });
    },
    [t],
  );

  return showDeleteConfirm;
};

export const useTranslate = (keyPrefix: string) => {
  return useTranslation('translation', { keyPrefix });
};

export const useCommonTranslation = () => {
  return useTranslation('translation', { keyPrefix: 'common' });
};
