/**
 * 知识库API响应类型定义
 * 定义所有知识库相关的API请求和响应接口
 * 确保类型安全的API调用
 */

import {
  IKnowledge,
  IDocument,
  IChunk,
  RetrievalResult,
  RetrievalSettings,
  KnowledgeCreateParams,
  KnowledgeUpdateParams,
  UploadFileParams,
  BatchOperationParams,
  ProcessingTask,
  OperationLog,
  KnowledgeStats,
  PaginationInfo,
  RunningStatus,
  KnowledgePermission,
  KnowledgeStatus,
  DocumentType
} from './knowledge';

// 重新导出需要的类型
export type {
  KnowledgeCreateParams,
  KnowledgeUpdateParams,
  UploadFileParams,
  RetrievalSettings,
  RunningStatus,
  KnowledgePermission,
  KnowledgeStatus,
  DocumentType
};
export type ChunkBatchOperationParams = BatchOperationParams;

// 通用API响应结构
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
  timestamp?: number;
}

// 分页响应结构
export interface PaginatedResponse<T = any> {
  data: T[];
  pagination: PaginationInfo;
  total: number;
}

// ==================== 知识库相关API类型 ====================

// 知识库列表请求参数
export interface KnowledgeListParams {
  page?: number;
  pageSize?: number;
  keywords?: string;
  labels?: string[];
  status?: KnowledgeStatus;
  permission?: KnowledgePermission;
  sortBy?: 'name' | 'create_time' | 'update_time' | 'doc_num' | 'chunk_num';
  sortOrder?: 'asc' | 'desc';
}

// 知识库列表响应
export interface KnowledgeListResponse extends ApiResponse<{
  kbs: IKnowledge[];
  total: number;
}> {}

// 知识库详情响应
export interface KnowledgeDetailResponse extends ApiResponse<IKnowledge> {}

// 知识库创建响应
export interface KnowledgeCreateResponse extends ApiResponse<{
  kb_id: string;
}> {}

// 知识库更新响应
export interface KnowledgeUpdateResponse extends ApiResponse<IKnowledge> {}

// 知识库删除响应
export interface KnowledgeDeleteResponse extends ApiResponse<{ id: string }> {}

// 知识库统计信息响应
export interface KnowledgeStatsResponse extends ApiResponse<KnowledgeStats> {}

// ==================== 文档相关API类型 ====================

// 文档列表请求参数
export interface DocumentListParams {
  kb_id: string;
  page?: number;
  pageSize?: number;
  keywords?: string;
  status?: RunningStatus;
  type?: DocumentType;
  sortBy?: 'name' | 'create_time' | 'update_time' | 'size' | 'progress';
  sortOrder?: 'asc' | 'desc';
}

// 文档列表响应
export interface DocumentListResponse extends ApiResponse<{
  docs: IDocument[];
  total: number;
}> {}

// 文档处理响应
export interface DocumentProcessResponse extends ApiResponse<{
  task_id: string;
  status: string;
}> {}

// 知识块更新参数
export interface ChunkUpdateParams {
  content: string;
  available: boolean;
  keywords?: string[];
  questions?: string[];
}

// 文档详情响应
export interface DocumentDetailResponse extends ApiResponse<IDocument> {}

// 文档上传响应
export interface DocumentUploadResponse extends ApiResponse<{
  doc_id: string;
  name: string;
  size: number;
  task_id?: string;
  status?: string;
}> {}

// 文档删除响应
export interface DocumentDeleteResponse extends ApiResponse<{ id: string }> {}

// 文档重新处理响应
export interface DocumentReprocessResponse extends ApiResponse<{
  doc_id: string;
  task_id: string;
  status: RunningStatus;
}> {}

// 文档处理状态响应
export interface DocumentProcessStatusResponse extends ApiResponse<ProcessingTask> {}

// ==================== 知识块相关API类型 ====================

// 知识块列表请求参数
export interface ChunkListParams {
  kb_id: string;
  doc_id?: string;
  page?: number;
  pageSize?: number;
  keywords?: string;
  available?: boolean; // true: 仅已启用, false: 仅已禁用, undefined: 全部
  sortBy?: 'create_time' | 'update_time' | 'similarity';
  sortOrder?: 'asc' | 'desc';
}

// 知识块列表响应
export interface ChunkListResponse extends PaginatedResponse<IChunk> {}

// 知识块更新响应
export interface ChunkUpdateResponse extends ApiResponse<IChunk> {}

// 知识块删除响应
export interface ChunkDeleteResponse extends ApiResponse<{ chunk_id: string }> {}

// 知识块批量操作响应
export interface ChunkBatchOperationResponse extends ApiResponse<{
  success_count: number;
  failed_count: number;
  failed_items?: Array<{
    chunk_id: string;
    error: string;
  }>;
}> {}

// 知识块详情响应
export interface ChunkDetailResponse extends ApiResponse<IChunk> {}

// 知识块创建请求参数
export interface ChunkCreateParams {
  kb_id: string;
  doc_id: string;
  content: string;
  available_int?: number;
  question_kwd?: string[];
  tag_kwd?: string[];
}

// 知识块创建响应
export interface ChunkCreateResponse extends ApiResponse<IChunk> {}

// 知识块更新参数（API版本）
export interface ChunkUpdateParamsV2 {
  content?: string;
  available_int?: number;
  question_kwd?: string[];
  tag_kwd?: string[];
}

// 知识块更新响应
export interface ChunkUpdateResponse extends ApiResponse<IChunk> {}

// 知识块删除响应
export interface ChunkDeleteResponse extends ApiResponse<{ chunk_id: string }> {}

// 知识块批量操作响应
export interface ChunkBatchOperationResponse extends ApiResponse<{
  success_count: number;
  failed_count: number;
  failed_items?: Array<{
    chunk_id: string;
    error: string;
  }>;
}> {}

// ==================== 检索相关API类型 ====================

// 检索请求参数
export interface RetrievalParams extends RetrievalSettings {
  kb_id: string;
  query: string;
  doc_ids?: string[]; // 限制检索的文档范围
  highlight?: boolean; // 是否返回高亮信息
}

// 检索响应
export interface RetrievalResponse extends ApiResponse<{
  query: string;
  results: RetrievalResult[];
  total_found: number;
  search_time: number; // 搜索耗时(ms)
}> {}

// 检索测试参数 (用于管理界面的检索测试功能)
export interface RetrievalTestParams extends RetrievalParams {
  save_history?: boolean; // 是否保存检索历史
}

// 检索测试响应
export interface RetrievalTestResponse extends RetrievalResponse {}

// ==================== 高级功能API类型 ====================

// 操作日志请求参数
export interface OperationLogParams {
  kb_id: string;
  page?: number;
  pageSize?: number;
  action?: string;
  user_id?: string;
  start_time?: number;
  end_time?: number;
}

// 操作日志响应
export interface OperationLogResponse extends PaginatedResponse<OperationLog> {}

// 知识库导出请求参数
export interface KnowledgeExportParams {
  kb_id: string;
  format: 'json' | 'csv' | 'txt';
  include_metadata?: boolean;
  doc_ids?: string[]; // 指定导出的文档
  chunk_ids?: string[]; // 指定导出的知识块
}

// 知识库导出响应
export interface KnowledgeExportResponse extends ApiResponse<{
  task_id: string;
  download_url?: string; // 如果是同步导出，直接返回下载链接
  estimated_time?: number; // 预估完成时间(秒)
}> {}

// 知识库导入请求参数
export interface KnowledgeImportParams {
  kb_id: string;
  file: File;
  format: 'json' | 'csv' | 'txt';
  merge_strategy: 'append' | 'replace' | 'skip_existing';
}

// 知识库导入响应
export interface KnowledgeImportResponse extends ApiResponse<{
  task_id: string;
  imported_count?: number;
  skipped_count?: number;
  failed_count?: number;
}> {}

// 权限设置请求参数
export interface PermissionSetParams {
  kb_id: string;
  permission: KnowledgePermission;
  user_ids?: string[]; // 当permission为'partial'时指定用户
  group_ids?: string[]; // 当permission为'partial'时指定用户组
}

// 权限设置响应
export interface PermissionSetResponse extends ApiResponse<{
  kb_id: string;
  permission: KnowledgePermission;
  affected_users: number;
}> {}

// 权限检查响应
export interface PermissionCheckResponse extends ApiResponse<{
  kb_id: string;
  user_id: string;
  permissions: {
    read: boolean;
    write: boolean;
    delete: boolean;
    manage: boolean;
  };
}> {}

// ==================== 任务和状态相关API类型 ====================

// 任务状态查询参数
export interface TaskStatusParams {
  task_id: string;
}

// 任务状态响应
export interface TaskStatusResponse extends ApiResponse<ProcessingTask> {}

// 任务列表请求参数
export interface TaskListParams {
  kb_id?: string;
  user_id?: string;
  status?: RunningStatus;
  task_type?: 'upload' | 'process' | 'export' | 'import' | 'delete';
  page?: number;
  pageSize?: number;
}

// 任务列表响应
export interface TaskListResponse extends PaginatedResponse<ProcessingTask> {}

// 任务取消响应
export interface TaskCancelResponse extends ApiResponse<{
  task_id: string;
  cancelled: boolean;
}> {}

// ==================== 错误响应类型 ====================

// API错误响应
export interface ApiErrorResponse {
  code: number;
  message: string;
  data?: any;
  success: false;
  error_type?: 'validation' | 'permission' | 'resource_not_found' | 'internal_error';
  details?: Record<string, any>;
}

// 表单验证错误
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// 批量操作错误
export interface BatchOperationError {
  id: string;
  error: string;
  error_code?: string;
}

// ==================== WebSocket相关类型 ====================

// WebSocket消息类型
export interface WebSocketMessage<T = any> {
  type: 'task_update' | 'processing_progress' | 'error' | 'notification';
  data: T;
  timestamp: number;
  kb_id?: string;
  user_id?: string;
}

// 任务进度更新消息
export interface TaskProgressMessage {
  task_id: string;
  doc_id?: string;
  kb_id: string;
  progress: number;
  status: RunningStatus;
  message: string;
  estimated_remaining?: number; // 预估剩余时间(秒)
}

// 处理完成通知消息
export interface ProcessingCompleteMessage {
  task_id: string;
  doc_id: string;
  kb_id: string;
  status: RunningStatus;
  chunk_count: number;
  error_message?: string;
}

// ==================== 工具函数类型 ====================

// API请求配置
export interface ApiRequestConfig {
  timeout?: number;
  retries?: number;
  cache?: boolean;
  cacheTtl?: number;
  headers?: Record<string, string>;
}

// API响应处理器类型
export type ApiResponseHandler<T> = (response: ApiResponse<T>) => T;
export type ApiErrorHandler = (error: ApiErrorResponse) => void;