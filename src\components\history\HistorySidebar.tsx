'use client';

import React, { useCallback, useRef, useEffect, useMemo, useState } from 'react';
import { useConversationList } from '@/store/chatStore';
import ConversationContextMenu from './ConversationContextMenu';

interface HistorySidebarProps {
  selectedConversationId: string;
  onConversationSelect: (id: string) => void | Promise<void>;
}

interface ConversationGroup {
  label: string;
  conversations: any[];
}

export default function HistorySidebar({
  selectedConversationId,
  onConversationSelect,
}: HistorySidebarProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  
  // 上下文菜单状态
  const [contextMenu, setContextMenu] = useState<{
    isVisible: boolean;
    position: { x: number; y: number };
    conversationId: string | null;
  }>({
    isVisible: false,
    position: { x: 0, y: 0 },
    conversationId: null,
  });

  // 内联编辑状态
  const [editingConversation, setEditingConversation] = useState<{
    id: string | null;
    title: string;
  }>({
    id: null,
    title: '',
  });

  // 环境变量在EnvironmentChecker中已经检查过，这里可以安全使用
  const agentId = process.env.NEXT_PUBLIC_CONVERSATION_AGENT_ID!;

  // 使用原有的hooks，只是修复了底层导入
  const {
    conversations,
    loading,
    error,
    hasMore,
    loadConversations,
    loadMore,
    createConversation,
    deleteConversation,
    renameConversation,
    isCreating,
    isDeletingConversation,
    isRenamingConversation
  } = useConversationList();

  // 初始化加载
  useEffect(() => {
    if (agentId) {
      loadConversations();
    }
  }, [agentId, loadConversations]);

  // 自动选择第一个会话（如果没有选中任何会话）
  useEffect(() => {
    if (!loading && !error && conversations.length > 0 && !selectedConversationId) {
      onConversationSelect(conversations[0].id);
    }
  }, [loading, error, conversations, selectedConversationId, onConversationSelect]);


  // 时间分组逻辑
  const groupedConversations = useMemo(() => {
    const groups: ConversationGroup[] = [];
    
    if (conversations.length === 0) {
      return groups;
    }

    // 按创建时间分组
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const todayConversations: any[] = [];
    const yesterdayConversations: any[] = [];
    const olderConversations: { [key: string]: any[] } = {};

    conversations.forEach(conv => {
      const convDate = new Date(conv.created_time);
      const diffDays = Math.floor((today.getTime() - convDate.getTime()) / (1000 * 60 * 60 * 24));
      
      if (diffDays === 0) {
        todayConversations.push(conv);
      } else if (diffDays === 1) {
        yesterdayConversations.push(conv);
      } else {
        // 按具体日期分组
        let dateLabel: string;
        if (diffDays < 30) {
          dateLabel = `${convDate.getMonth() + 1}月${convDate.getDate()}日`;
        } else if (diffDays < 365) {
          dateLabel = `${convDate.getMonth() + 1}月${convDate.getDate()}日`;
        } else {
          dateLabel = '更早以前';
        }
        
        if (!olderConversations[dateLabel]) {
          olderConversations[dateLabel] = [];
        }
        olderConversations[dateLabel].push(conv);
      }
    });

    // 添加分组
    if (todayConversations.length > 0) {
      groups.push({ label: '今天', conversations: todayConversations });
    }
    if (yesterdayConversations.length > 0) {
      groups.push({ label: '昨天', conversations: yesterdayConversations });
    }
    
    // 添加其他日期分组
    Object.entries(olderConversations).forEach(([dateLabel, convs]) => {
      if (convs.length > 0) {
        groups.push({ label: dateLabel, conversations: convs });
      }
    });

    return groups;
  }, [conversations]);

  // 滚动到底部加载更多
  const handleScroll = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container || loading) return;
    
    const { scrollTop, scrollHeight, clientHeight } = container;
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;
    
    if (isNearBottom && hasMore) {
      loadMore();
    }
  }, [loading, hasMore, loadMore]);
  
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);

  // 创建新会话
  const handleCreateConversation = useCallback(async () => {
    try {
      const newConversation = await createConversation({
        initialMessage: "开始新的对话"
      });
      if (newConversation) {
        onConversationSelect(newConversation.id);
      }
    } catch (error) {
      console.error('创建会话失败:', error);
    }
  }, [createConversation, onConversationSelect]);

  // 显示上下文菜单
  const handleContextMenu = useCallback((e: React.MouseEvent, conversationId: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    setContextMenu({
      isVisible: true,
      position: { x: e.clientX, y: e.clientY },
      conversationId,
    });
  }, []);

  // 三点按钮点击
  const handleMenuButtonClick = useCallback((e: React.MouseEvent, conversationId: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    const rect = (e.target as HTMLElement).getBoundingClientRect();
    setContextMenu({
      isVisible: true,
      position: { x: rect.left - 160, y: rect.bottom + 4 }, // 菜单右对齐
      conversationId,
    });
  }, []);

  // 关闭上下文菜单
  const handleCloseContextMenu = useCallback(() => {
    setContextMenu({
      isVisible: false,
      position: { x: 0, y: 0 },
      conversationId: null,
    });
  }, []);

  // 删除会话
  const handleDeleteConversation = useCallback(async () => {
    if (!contextMenu.conversationId) return;
    
    if (!confirm('确定要删除这个会话吗？删除后无法恢复。')) {
      handleCloseContextMenu();
      return;
    }

    try {
      const success = await deleteConversation(contextMenu.conversationId);
      if (success && selectedConversationId === contextMenu.conversationId) {
        const remainingConversations = conversations.filter(c => c.id !== contextMenu.conversationId);
        if (remainingConversations.length > 0) {
          onConversationSelect(remainingConversations[0].id);
        }
      }
      handleCloseContextMenu();
    } catch (error) {
      console.error('删除会话失败:', error);
      handleCloseContextMenu();
    }
  }, [contextMenu.conversationId, deleteConversation, selectedConversationId, conversations, onConversationSelect, handleCloseContextMenu]);

  // 开始内联编辑
  const handleStartInlineEdit = useCallback(() => {
    if (!contextMenu.conversationId) return;
    
    const conversation = conversations.find(c => c.id === contextMenu.conversationId);
    if (!conversation) {
      handleCloseContextMenu();
      return;
    }
    
    setEditingConversation({
      id: contextMenu.conversationId,
      title: conversation.titleAlias || conversation.title,
    });
    handleCloseContextMenu();
  }, [contextMenu.conversationId, conversations, handleCloseContextMenu]);

  // 保存编辑
  const handleSaveEdit = useCallback(async () => {
    if (!editingConversation.id || !editingConversation.title.trim()) {
      setEditingConversation({ id: null, title: '' });
      return;
    }

    const conversation = conversations.find(c => c.id === editingConversation.id);
    if (!conversation || editingConversation.title.trim() === (conversation.titleAlias || conversation.title)) {
      setEditingConversation({ id: null, title: '' });
      return;
    }

    try {
      await renameConversation(editingConversation.id, editingConversation.title.trim());
      setEditingConversation({ id: null, title: '' });
    } catch (error) {
      console.error('重命名会话失败:', error);
      setEditingConversation({ id: null, title: '' });
    }
  }, [editingConversation, conversations, renameConversation]);

  // 取消编辑
  const handleCancelEdit = useCallback(() => {
    setEditingConversation({ id: null, title: '' });
  }, []);

  // 处理输入框键盘事件
  const handleEditKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelEdit();
    }
  }, [handleSaveEdit, handleCancelEdit]);

  // 错误状态处理
  if (error) {
    return (
      <div className="flex flex-col h-full w-[280px] bg-layer-1 border-r border-border-light">
        <div className="p-4 border-b border-border-light">
          <div className="text-error text-sm">加载会话列表失败</div>
          <button 
            onClick={() => loadConversations()}
            className="mt-2 text-primary text-sm hover:underline"
          >
            点击重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-[280px] h-screen bg-layer-1 border-r border-border-light p-2">
      {/* 顶部操作按钮区域 */}
      <div className="flex flex-row items-center px-4 py-1 w-66 h-10 mb-2">
        {/* 占位按钮（设计稿中标记为display: none） */}
        <div className="w-8 h-8 rounded-lg p-2 justify-center items-center">
          <div className="w-4 h-4" onClick={handleCreateConversation}>
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12H12M12 12H15M12 12V9M12 12V15M4.5 5V6M8.5 5V6M5.5 12H3C1.89543 12 1 11.1046 1 10V3C1 1.89543 1.89543 1 3 1H10C11.1046 1 12 1.89543 12 3V5.5" stroke="black" strokeOpacity="0.72" strokeWidth="1.4" strokeLinecap="round"/>
            </svg>
          </div>
        </div>
      </div>

      {/* 滚动容器 */}
      <div className="relative flex-1 overflow-hidden">
        {/* 渐变背景 */}
        <div 
          className="absolute inset-0 pointer-events-none z-10"
        />
        
        {/* 滚动内容 */}
        <div 
          ref={scrollContainerRef}
          className="absolute inset-0 overflow-y-auto scrollbar-none"
        >
          {/* 会话列表 */}
          <div className="flex flex-col items-start pt-4 pb-20 gap-1">
            {/* 加载状态 */}
            {loading && conversations.length === 0 && (
              <div className="p-4 text-center w-full">
                <div className="text-text-muted text-sm">加载会话中...</div>
              </div>
            )}
            
            {/* 空状态 */}
            {!loading && conversations.length === 0 && (
              <div className="p-8 text-center space-y-3 w-full">
                <svg className="w-12 h-12 mx-auto text-text-muted" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <div className="text-text-muted text-sm">暂无会话记录</div>
                <div className="text-text-muted text-xs">点击上方按钮创建第一个会话</div>
              </div>
            )}
            
            {/* 分组会话列表 */}
            {groupedConversations.map((group, groupIndex) => (
              <div key={groupIndex} className="w-full">
                {/* 时间分组标题 */}
                <div className="flex flex-row justify-between items-center px-2.5 py-1.5 gap-3 w-66 h-7.5">
                  <span className="flex-1 text-xs font-medium leading-4.5 text-text-muted">
                    {group.label}
                  </span>
                </div>
                
                {/* 该分组的会话项 */}
                {group.conversations.map((conversation) => {
                  const isSelected = selectedConversationId === conversation.id;
                  const isDeleting = isDeletingConversation === conversation.id;
                  const isRenaming = isRenamingConversation === conversation.id;
                  const isEditing = editingConversation.id === conversation.id;
                  
                  return (
                    <div
                      key={conversation.id}
                      className={`group flex flex-row justify-between items-center px-2.5 py-1.5 gap-3 w-66 h-9 rounded-lg cursor-pointer transition-colors ${
                        isSelected ? 'bg-layer-3' : 'hover:bg-layer-2'
                      } ${isDeleting ? 'opacity-50' : ''} ${isEditing ? 'cursor-default' : ''}`}
                      onClick={() => !isDeleting && !isRenaming && !isEditing && onConversationSelect(conversation.id)}
                      onContextMenu={(e) => !isEditing && handleContextMenu(e, conversation.id)}
                    >
                      {/* 内容区域：编辑时显示输入框，否则显示文本 */}
                      {isEditing ? (
                        <input
                          ref={(input) => {
                            if (input) {
                              input.focus();
                              input.select();
                            }
                          }}
                          type="text"
                          value={editingConversation.title}
                          onChange={(e) => setEditingConversation(prev => ({ ...prev, title: e.target.value }))}
                          onKeyDown={handleEditKeyDown}
                          onBlur={handleSaveEdit}
                          className="flex-1 text-sm font-normal leading-5 text-text-primary bg-transparent border-none outline-none"
                          placeholder="输入会话标题"
                        />
                      ) : (
                        <span className="flex-1 text-sm font-normal leading-5 text-text-primary line-clamp-1">
                          {isRenaming ? '重命名中...' : (conversation.titleAlias || conversation.title)}
                        </span>
                      )}
                      
                      {/* 三点菜单按钮 - 编辑时隐藏 */}
                      {!isEditing && (
                        <button
                          onClick={(e) => handleMenuButtonClick(e, conversation.id)}
                          disabled={isRenaming || isDeleting}
                          className={`flex justify-center items-center w-6 h-6 p-1 bg-layer-3 rounded-md transition-opacity ${
                            isSelected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
                          }`}
                          title="更多操作"
                        >
                          <div className="w-4 h-4">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                              <path
                                d="M8 8.66667C8.36819 8.66667 8.66667 8.36819 8.66667 8C8.66667 7.63181 8.36819 7.33333 8 7.33333C7.63181 7.33333 7.33333 7.63181 7.33333 8C7.33333 8.36819 7.63181 8.66667 8 8.66667Z"
                                fill="rgba(0, 0, 0, 0.72)"
                              />
                              <path
                                d="M8 4C8.36819 4 8.66667 3.70152 8.66667 3.33333C8.66667 2.96514 8.36819 2.66667 8 2.66667C7.63181 2.66667 7.33333 2.96514 7.33333 3.33333C7.33333 3.70152 7.63181 4 8 4Z"
                                fill="rgba(0, 0, 0, 0.72)"
                              />
                              <path
                                d="M8 13.3333C8.36819 13.3333 8.66667 13.0348 8.66667 12.6667C8.66667 12.2985 8.36819 12 8 12C7.63181 12 7.33333 12.2985 7.33333 12.6667C7.33333 13.0348 7.63181 13.3333 8 13.3333Z"
                                fill="rgba(0, 0, 0, 0.72)"
                              />
                            </svg>
                          </div>
                        </button>
                      )}
                    </div>
                  );
                })}
              </div>
            ))}

            {/* 加载更多指示器 */}
            {loading && conversations.length > 0 && (
              <div className="p-4 text-center w-full">
                <div className="text-text-muted text-sm">加载更多...</div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 上下文菜单 */}
      <ConversationContextMenu
        isVisible={contextMenu.isVisible}
        position={contextMenu.position}
        onClose={handleCloseContextMenu}
        onRename={handleStartInlineEdit}
        onDelete={handleDeleteConversation}
        isRenaming={contextMenu.conversationId ? isRenamingConversation === contextMenu.conversationId : false}
        isDeleting={contextMenu.conversationId ? isDeletingConversation === contextMenu.conversationId : false}
      />
    </div>
  );
}