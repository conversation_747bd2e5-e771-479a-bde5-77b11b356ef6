'use client';

import React, { useState, useRef, DragEvent, ChangeEvent } from 'react';
import { useDocumentUpload } from '@/hooks/useKnowledge';
import { formatFileSize } from '@/types/knowledge-enums';

interface DocumentUploadProps {
  knowledgeId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

interface FileWithProgress extends File {
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

export default function DocumentUpload({
  knowledgeId,
  isOpen,
  onClose,
  onSuccess
}: DocumentUploadProps) {
  const [files, setFiles] = useState<FileWithProgress[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isDragActive, setIsDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { uploadDocuments } = useDocumentUpload();

  // 支持的文件扩展名
  const supportedExtensions = ['.pdf', '.doc', '.docx', '.txt', '.md', '.html', '.json'];
  const maxFileSize = 50 * 1024 * 1024; // 50MB

  // 验证文件类型
  const isValidFileType = (file: File): boolean => {
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    return supportedExtensions.includes(extension);
  };

  // 验证文件大小
  const isValidFileSize = (file: File): boolean => {
    return file.size <= maxFileSize;
  };

  // 处理文件添加
  const handleFiles = (fileList: FileList) => {
    const newFiles: FileWithProgress[] = [];
    
    Array.from(fileList).forEach(file => {
      if (!isValidFileType(file)) {
        console.warn(`文件 ${file.name} 格式不支持`);
        return;
      }
      
      if (!isValidFileSize(file)) {
        console.warn(`文件 ${file.name} 超过大小限制`);
        return;
      }
      
      newFiles.push({
        ...file,
        id: Math.random().toString(36).substr(2, 9),
        progress: 0,
        status: 'pending'
      });
    });
    
    setFiles(prev => [...prev, ...newFiles]);
  };

  // 拖拽事件处理
  const handleDragEnter = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
    
    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      handleFiles(droppedFiles);
    }
  };

  // 文件选择事件处理
  const handleFileSelect = (e: ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (selectedFiles) {
      handleFiles(selectedFiles);
    }
    // 清除input值，允许重复选择相同文件
    e.target.value = '';
  };

  // 点击选择文件
  const handleSelectFiles = () => {
    fileInputRef.current?.click();
  };

  // 删除文件
  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
  };

  // 获取文件类型图标
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return (
          <svg className="w-8 h-8 text-red-500" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm2 2h8v2H6V4zm0 4h8v2H6V8zm0 4h4v2H6v-2z"/>
          </svg>
        );
      case 'doc':
      case 'docx':
        return (
          <svg className="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm2 2h8v2H6V4zm0 4h8v2H6V8zm0 4h4v2H6v-2z"/>
          </svg>
        );
      case 'txt':
      case 'md':
        return (
          <svg className="w-8 h-8 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm2 2h8v2H6V4zm0 4h8v2H6V8zm0 4h8v2H6v-2z"/>
          </svg>
        );
      default:
        return (
          <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm2 2h8v2H6V4zm0 4h8v2H6V8zm0 4h8v2H6v-2z"/>
          </svg>
        );
    }
  };

  // 上传所有文件
  const handleUpload = async () => {
    if (files.length === 0) return;
    
    setIsUploading(true);
    
    try {
      // 根据API限制，需要逐个上传文件
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        // 标记当前文件为上传中
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, status: 'uploading' as const } : f
        ));
        
        try {
          await uploadDocuments(knowledgeId, {
            files: [file as File],
            layout_recognize: true,
            auto_keywords: 5,
            auto_questions: 3
          });

          // 标记当前文件为成功
          setFiles(prev => prev.map(f => 
            f.id === file.id ? { ...f, status: 'success' as const, progress: 100 } : f
          ));
        } catch (fileError: any) {
          // 标记当前文件为失败
          setFiles(prev => prev.map(f => 
            f.id === file.id ? { ...f, status: 'error' as const, error: fileError.message } : f
          ));
        }
      }

      // 检查是否有成功上传的文件
      const hasSuccessful = files.some(file => 
        files.find(f => f.id === file.id)?.status === 'success'
      );
      
      if (hasSuccessful) {
        // 延迟关闭对话框
        setTimeout(() => {
          onSuccess?.();
          onClose();
          setFiles([]);
        }, 1000);
      }

    } catch (error: any) {
      console.error('批量上传失败:', error);
    } finally {
      setIsUploading(false);
    }
  };

  // 模态框背景点击关闭
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && !isUploading) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-layer-1 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-text-primary">上传文档</h2>
          <button
            onClick={onClose}
            disabled={isUploading}
            className="text-text-secondary hover:text-text-primary transition-colors disabled:opacity-50"
          >
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 隐藏的文件输入 */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.txt,.md,.html,.json"
          onChange={handleFileSelect}
          className="hidden"
        />

        {/* 拖拽上传区域 */}
        <div
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={handleSelectFiles}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            isDragActive ? 'border-primary bg-blue-50' : 'border-border-light hover:border-primary'
          }`}
        >
          <div className="space-y-4">
            <div className="flex justify-center">
              <svg className="w-12 h-12 text-text-muted" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
            </div>
            <div>
              <p className="text-text-primary font-medium">
                {isDragActive ? '释放文件以开始上传' : '拖拽文件到此处，或点击选择文件'}
              </p>
              <p className="text-text-muted text-sm mt-2">
                支持 PDF、Word、TXT、Markdown、HTML、JSON 格式，单个文件最大 50MB
              </p>
            </div>
          </div>
        </div>

        {/* 文件列表 */}
        {files.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-medium text-text-primary mb-4">
              待上传文件 ({files.length})
            </h3>
            <div className="space-y-3 max-h-60 overflow-y-auto">
              {files.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center space-x-3 p-3 bg-layer-2 rounded-lg"
                >
                  <div className="flex-shrink-0">
                    {getFileIcon(file.name)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-text-primary font-medium truncate">{file.name}</p>
                    <p className="text-text-muted text-sm">{formatFileSize(file.size)}</p>
                    
                    {/* 进度条 */}
                    {file.status === 'uploading' && (
                      <div className="mt-2">
                        <div className="w-full bg-border-light rounded-full h-2">
                          <div 
                            className="bg-primary h-2 rounded-full transition-all duration-300 animate-pulse"
                            style={{ width: '80%' }}
                          ></div>
                        </div>
                        <p className="text-xs text-text-muted mt-1">上传中...</p>
                      </div>
                    )}

                    {/* 状态显示 */}
                    {file.status === 'success' && (
                      <div className="flex items-center mt-2">
                        <svg className="w-4 h-4 text-success mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span className="text-success text-sm">上传成功</span>
                      </div>
                    )}

                    {file.status === 'error' && (
                      <div className="mt-2">
                        <div className="flex items-center">
                          <svg className="w-4 h-4 text-error mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                          </svg>
                          <span className="text-error text-sm">上传失败</span>
                        </div>
                        {file.error && (
                          <p className="text-error text-xs mt-1">{file.error}</p>
                        )}
                      </div>
                    )}
                  </div>
                  
                  {/* 删除按钮 */}
                  {file.status === 'pending' && (
                    <button
                      onClick={() => removeFile(file.id)}
                      className="flex-shrink-0 text-text-muted hover:text-error transition-colors"
                    >
                      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center justify-end space-x-3 mt-6 pt-4 border-t border-border-light">
          <button
            onClick={onClose}
            disabled={isUploading}
            className="px-4 py-2 text-text-secondary hover:text-text-primary transition-colors disabled:opacity-50"
          >
            取消
          </button>
          <button
            onClick={handleUpload}
            disabled={files.length === 0 || isUploading}
            className="bg-brand text-bg-base px-6 py-2 rounded-lg font-medium hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isUploading ? '上传中...' : `上传 ${files.length} 个文件`}
          </button>
        </div>
      </div>
    </div>
  );
}