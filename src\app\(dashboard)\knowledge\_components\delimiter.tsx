import { Form, Input } from 'antd';

interface IProps {
  value?: string | undefined;
  onChange?: (val: string | undefined) => void;
  maxLength?: number;
}

export const DelimiterInput = ({ value, onChange, maxLength }: IProps) => {
  const nextValue = value?.replaceAll('\n', '\\n');
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;
    const nextValue = val.replaceAll('\\n', '\n');
    onChange?.(nextValue);
  };
  return (
    <Input
      value={nextValue}
      onChange={handleInputChange}
      maxLength={maxLength}
    ></Input>
  );
};

const Delimiter = () => {

  return (
    <Form.Item
      name={['parser_config', 'delimiter']}
      label={'文本分段标识符'}
      initialValue={`\n`}
      rules={[{ required: true }]}
      tooltip={
        '支持多字符作为分隔符，多字符用两个反引号 \\`\\` 分隔符包裹。若配置成：\\n`##`; 系统将首先使用换行符、两个#号以及分号先对文本进行分割，随后再对分得的小文本块按照「建议文本块大小」设定的大小进行拼装。在设置文本分段标识符前请确保理解上述文本分段切片机制。'
      }
    >
      <DelimiterInput />
    </Form.Item>
  );
};

export default Delimiter;
