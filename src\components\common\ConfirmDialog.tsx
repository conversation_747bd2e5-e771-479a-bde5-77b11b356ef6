"use client";

import React from "react";
import { useBoolean } from "ahooks";

interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  content: string;
  confirmText?: string;
  cancelText?: string;
}

/**
 * 全局确认对话框组件
 */
export default function ConfirmDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  content,
  confirmText = "确认删除",
  cancelText = "取消",
}: ConfirmDialogProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#00000080]">
      <div className="bg-white rounded-[16px] w-[480px] shadow-lg p-6">
        {/* 标题区域 */}
        <div className="flex justify-between items-center ">
          <span className="text-[18px] font-medium text-[#000000E0]">
            {title}
          </span>
          <img
            src="/close.svg"
            alt="close"
            onClick={onClose}
            className="cursor-pointer"
          />
        </div>

        {/* 内容区域 */}
        <div className="mt-[16px]">
          <span className="text-[14px] text-[#000000B8] font-normal">
            {content}
          </span>
        </div>

        {/* 按钮区域 */}
        <div className="flex justify-end space-x-3 mt-[40px] h-[37px]">
          <button
            onClick={onClose}
            className="cursor-pointer px-4 py-2 font-medium text-[14px] text-[#000000B8] border border-[#0000000F] rounded-[8px] hover:bg-[#0000000A] transition-colors"
          >
            {cancelText}
          </button>
          <button
            onClick={() => {
              onClose();
              onConfirm();
            }}
            className="cursor-pointer px-4 py-2 font-medium text-[14px] text-white bg-[#E74639] rounded-[8px] hover:bg-[#D03A2F] transition-colors"
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
}

/**
 * 创建一个全局的确认对话框控制器
 */
export function useConfirmDialog() {
  const [isOpen, { setTrue: open, setFalse: close }] = useBoolean(false);
  const [config, setConfig] = React.useState<
    Omit<ConfirmDialogProps, "isOpen" | "onClose">
  >({
    title: "",
    content: "",
    onConfirm: () => {},
    confirmText: "确认删除",
    cancelText: "取消",
  });

  const showConfirm = (
    newConfig: Omit<ConfirmDialogProps, "isOpen" | "onClose">
  ) => {
    setConfig(newConfig);
    open();
  };

  const confirmDialog = (
    <ConfirmDialog isOpen={isOpen} onClose={close} {...config} />
  );

  return {
    confirmDialog,
    showConfirm,
    close,
  };
}
