/**
 * 请求管理器 - 处理请求去重和错误重试
 */

import { getQueryClient } from '@/providers/QueryProvider';

// 正在进行的请求缓存
const pendingRequests = new Map<string, Promise<any>>();

/**
 * 生成请求唯一键
 */
function generateRequestKey(url: string, options?: RequestInit): string {
  const method = options?.method || 'GET';
  const body = options?.body ? JSON.stringify(options.body) : '';
  const headers = options?.headers ? JSON.stringify(options.headers) : '';
  
  return `${method}:${url}:${body}:${headers}`;
}

/**
 * 去重请求 - 如果相同请求正在进行中，直接返回该请求的Promise
 */
export async function deduplicatedRequest<T>(
  url: string,
  options?: RequestInit
): Promise<T> {
  const key = generateRequestKey(url, options);
  
  // 如果请求正在进行中，返回现有的Promise
  if (pendingRequests.has(key)) {
    console.log(`🔄 请求去重: ${key}`);
    return pendingRequests.get(key)!;
  }
  
  // 创建新请求
  const requestPromise = fetch(url, options)
    .then(async (response) => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .finally(() => {
      // 请求完成后从缓存中移除
      pendingRequests.delete(key);
    });
  
  // 缓存请求Promise
  pendingRequests.set(key, requestPromise);
  
  return requestPromise;
}

/**
 * 带重试的请求
 */
export async function retryRequest<T>(
  url: string,
  options?: RequestInit,
  maxRetries = 2,
  retryDelay = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await deduplicatedRequest<T>(url, options);
    } catch (error) {
      lastError = error as Error;
      
      // 最后一次尝试，不再重试
      if (attempt === maxRetries) {
        break;
      }
      
      // 4xx错误不重试
      if (lastError.message.includes('4')) {
        break;
      }
      
      console.log(`🔄 请求重试 ${attempt + 1}/${maxRetries}: ${url}`);
      
      // 指数退避延迟
      const delay = retryDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}

/**
 * 清除所有待处理的请求
 */
export function clearPendingRequests(): void {
  pendingRequests.clear();
}

/**
 * 获取当前待处理请求数量
 */
export function getPendingRequestsCount(): number {
  return pendingRequests.size;
}

/**
 * React Query集成 - 创建带去重和重试的查询函数
 */
export function createQueryFn<T>(
  url: string,
  options?: RequestInit
) {
  return async (): Promise<T> => {
    return retryRequest<T>(url, options);
  };
}

/**
 * React Query集成 - 预加载数据
 */
export function prefetchQuery<T>(
  queryKey: string[],
  url: string,
  options?: RequestInit
): void {
  const queryClient = getQueryClient();
  
  queryClient.prefetchQuery({
    queryKey,
    queryFn: createQueryFn<T>(url, options),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}

/**
 * React Query集成 - 使查询失效
 */
export function invalidateQueries(queryKey: string[]): void {
  const queryClient = getQueryClient();
  queryClient.invalidateQueries({ queryKey });
}

/**
 * React Query集成 - 设置查询数据
 */
export function setQueryData<T>(queryKey: string[], data: T): void {
  const queryClient = getQueryClient();
  queryClient.setQueryData(queryKey, data);
}