"use client";
import "@ant-design/v5-patch-for-react-19";
import { App, ConfigProvider, ThemeConfig } from "antd";
import zhCN from "antd/locale/zh_CN";
import { StyleProvider } from "@ant-design/cssinjs";

const themeConfig: ThemeConfig = {
  components: {
    // 按钮
    Button: {
      contentFontSizeLG: 14,
    },
  },
  token: {
    controlHeightLG: 37, // 较高的组件高度
  },
};

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <StyleProvider layer>
      <ConfigProvider locale={zhCN} theme={themeConfig} componentSize="large">
        <App className="h-full">
            <div className="h-full flex flex-col px-6 py-4">{children}</div>
        </App>
      </ConfigProvider>
    </StyleProvider>
  );
}
