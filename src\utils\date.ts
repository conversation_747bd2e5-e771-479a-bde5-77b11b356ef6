import dayjs from "dayjs";

export function formatDate(date: any, format?: string) {
  if (!date) {
    return "";
  }
  // return dayjs(date).format(format || "DD/MM/YYYY HH:mm:ss");
  return dayjs(date).format(format || "YYYY-MM-DD HH:mm:ss");
}

export function formatTime(date: any) {
  if (!date) {
    return "";
  }
  return dayjs(date).format("HH:mm:ss");
}

export function today() {
  return formatDate(dayjs());
}

export function lastDay() {
  return formatDate(dayjs().subtract(1, "days"));
}

export function lastWeek() {
  return formatDate(dayjs().subtract(1, "weeks"));
}

export function formatPureDate(date: any) {
  if (!date) {
    return "";
  }
  return dayjs(date).format("DD/MM/YYYY");
}
