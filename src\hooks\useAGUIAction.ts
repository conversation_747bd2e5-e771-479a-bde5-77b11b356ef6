'use client';

// useAGUIAction - 简化版本，基于conversationId的独立工具注册
// 提供完全兼容的API，内部基于AG-UI协议实现

import { useEffect, useCallback } from 'react';
import { useAGUIContext } from '@/lib/agui/AGUIProvider';
import { UseAGUIActionParams, RegisteredAction } from '@/lib/agui/types';

/**
 * useAGUIAction Hook
 * 提供与CopilotKit useCopilotAction完全兼容的API
 * 工具定义随消息一起发送，无需单独同步到服务端
 * 
 * @example
 * ```tsx
 * import { useAGUIAction } from '@/hooks/useAGUIAction';
 * 
 * function TaskManager() {
 *   useAGUIAction({
 *     name: "addTask",
 *     description: "添加新任务到任务列表",
 *     parameters: [
 *       {
 *         name: "title",
 *         type: "string",
 *         description: "任务标题",
 *         required: true
 *       },
 *       {
 *         name: "priority",
 *         type: "string",
 *         description: "任务优先级",
 *         enum: ["high", "medium", "low"]
 *       }
 *     ],
 *     handler: async ({ title, priority }) => {
 *       await addTask(title, priority);
 *       console.log(`任务已添加: ${title}`);
 *     },
 *     render: ({ status, args }) => {
 *       if (status === 'executing') {
 *         return <div>正在添加任务: {args.title}...</div>;
 *       }
 *       if (status === 'complete') {
 *         return <div>任务已成功添加: {args.title}</div>;
 *       }
 *       return null;
 *     }
 *   });
 * 
 *   return <div>Task Manager Component</div>;
 * }
 * ```
 */
export function useAGUIAction<T = Record<string, any>>(params: UseAGUIActionParams<T>): void {
  const context = useAGUIContext();

  // 稳定的参数引用
  const stableParams = useCallback(() => ({
    name: params.name,
    description: params.description,
    parameters: params.parameters,
    handler: params.handler,
    render: params.render
  }), [params.name, params.description, params.parameters, params.handler, params.render]);

  useEffect(() => {
    // 创建注册的Action
    const registeredAction: RegisteredAction = {
      name: params.name,
      description: params.description,
      parameters: params.parameters,
      handler: params.handler,
      render: params.render,
      currentExecution: {
        status: 'idle'
      }
    };

    // 注册Action到context
    context.registerAction(registeredAction);

    // 清理函数：组件卸载时注销Action
    return () => {
      context.unregisterAction(params.name);
    };
  }, [stableParams, context]);

  // 注意：与CopilotKit一样，这个hook不返回任何值
  // 它只是注册Action，实际执行由AG-UI协议驱动
  // 工具定义随消息一起发送，无需单独同步
}

// 导出类型以便外部使用
export type { UseAGUIActionParams };