'use client';

import { 
  ConnectionService, 
  EventBus 
} from './types';

/**
 * 连接服务实现 - 简化版
 * 连接管理现在由AGUIProvider负责，这里主要提供状态同步和接口兼容性
 */
export class ConnectionServiceImpl implements ConnectionService {
  private eventBus: EventBus;
  private currentConversationId: string | null = null;
  private currentAgentId: string | null = null;
  private connectionStatus: 'connected' | 'disconnected' | 'connecting' | 'reconnecting' = 'disconnected';
  private enableLogging = false;

  constructor(eventBus: EventBus, options?: { 
    reconnectInterval?: number; 
    maxRetries?: number;
    enableLogging?: boolean;
  }) {
    this.eventBus = eventBus;
    this.enableLogging = options?.enableLogging ?? false;
    
    // 监听外部连接状态变化（来自EventBridge）
    this.setupConnectionStatusListener();
  }

  /**
   * 设置连接状态监听器
   */
  private setupConnectionStatusListener(): void {
    this.eventBus.on('CONNECTION_STATUS', (event) => {
      const payload = event.payload as any;
      if (payload?.status) {
        this.connectionStatus = payload.status;
        
        if (this.enableLogging) {
          console.log('[ConnectionService] 连接状态更新:', payload.status);
        }
      }
    });
  }

  /**
   * 连接到AGUI系统 - 现在由AGUIProvider管理
   */
  async connect(conversationId: string, agentId: string): Promise<void> {
    if (!conversationId || !agentId) {
      throw new Error('会话ID和Agent ID不能为空');
    }

    this.currentConversationId = conversationId;
    this.currentAgentId = agentId;

    if (this.enableLogging) {
      console.log(`[ConnectionService] 记录连接参数: conversationId=${conversationId}, agentId=${agentId}`);
      console.log(`[ConnectionService] 实际连接由AGUIProvider管理`);
    }

    // 发射连接状态事件（实际连接由AGUIProvider管理）
    this.eventBus.emit({
      type: 'CONNECTION_STATUS',
      payload: {
        status: 'connecting',
        conversationId
      },
      timestamp: Date.now(),
      source: 'ConnectionService'
    });
  }

  /**
   * 断开连接 - 现在由AGUIProvider管理
   */
  async disconnect(): Promise<void> {
    this.connectionStatus = 'disconnected';
    
    if (this.enableLogging) {
      console.log('[ConnectionService] 断开连接请求 - 由AGUIProvider管理');
    }

    // 发射断开连接事件
    this.eventBus.emit({
      type: 'CONNECTION_STATUS',
      payload: {
        status: 'disconnected',
        conversationId: this.currentConversationId
      },
      timestamp: Date.now(),
      source: 'ConnectionService'
    });
  }

  /**
   * 获取连接状态
   */
  getConnectionState(): string {
    return this.connectionStatus;
  }

  /**
   * 获取状态（接口要求）
   */
  getStatus(): 'connected' | 'disconnected' | 'connecting' | 'reconnecting' {
    return this.connectionStatus;
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.connectionStatus === 'connected';
  }

  /**
   * 获取当前会话ID
   */
  getCurrentConversationId(): string | null {
    return this.currentConversationId;
  }

  /**
   * 获取当前Agent ID
   */
  getCurrentAgentId(): string | null {
    return this.currentAgentId;
  }

  /**
   * 获取调试信息
   */
  getDebugInfo(): {
    connectionStatus: string;
    conversationId: string | null;
    agentId: string | null;
    note: string;
  } {
    return {
      connectionStatus: this.connectionStatus,
      conversationId: this.currentConversationId,
      agentId: this.currentAgentId,
      note: '连接管理由AGUIProvider负责'
    };
  }

  /**
   * 设置日志开关
   */
  setLogging(enabled: boolean): void {
    this.enableLogging = enabled;
  }

  /**
   * 销毁连接服务
   */
  async destroy(): Promise<void> {
    this.connectionStatus = 'disconnected';
    this.currentConversationId = null;
    this.currentAgentId = null;
    
    if (this.enableLogging) {
      console.log('[ConnectionService] 已销毁（连接管理由AGUIProvider负责）');
    }
  }
}