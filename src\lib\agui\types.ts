// AG-UI 协议类型定义
// 基于 ag-ui-sample 的类型定义，专门针对 SignalR 传输

import React from 'react';

export enum EventType {
  // 生命周期事件
  RUN_STARTED = 'RUN_STARTED',
  RUN_FINISHED = 'RUN_FINISHED',
  RUN_ERROR = 'RUN_ERROR',
  STEP_STARTED = 'STEP_STARTED',
  STEP_FINISHED = 'STEP_FINISHED',
  
  // 文本消息事件
  TEXT_MESSAGE_START = 'TEXT_MESSAGE_START',
  TEXT_MESSAGE_CONTENT = 'TEXT_MESSAGE_CONTENT',
  TEXT_MESSAGE_END = 'TEXT_MESSAGE_END',
  
  // 工具调用事件
  TOOL_CALL_START = 'TOOL_CALL_START',
  TOOL_CALL_ARGS = 'TOOL_CALL_ARGS',
  TOOL_CALL_END = 'TOOL_CALL_END',
  TOOL_CALL_RESULT = 'TOOL_CALL_RESULT',
  
  // 状态管理事件
  STATE_SNAPSHOT = 'STATE_SNAPSHOT',
  STATE_DELTA = 'STATE_DELTA',
  MESSAGES_SNAPSHOT = 'MESSAGES_SNAPSHOT',
  
  // 特殊事件
  RAW = 'RAW',
  CUSTOM = 'CUSTOM'
}

export interface BaseEvent {
  type: EventType;
  timestamp?: number;
  rawEvent?: any;
}

// 生命周期事件
export interface RunStartedEvent extends BaseEvent {
  type: EventType.RUN_STARTED;
  threadId: string;
  runId: string;
}

export interface RunFinishedEvent extends BaseEvent {
  type: EventType.RUN_FINISHED;
  threadId: string;
  runId: string;
}

export interface RunErrorEvent extends BaseEvent {
  type: EventType.RUN_ERROR;
  message: string;
  code?: string;
}

export interface StepStartedEvent extends BaseEvent {
  type: EventType.STEP_STARTED;
  stepName: string;
}

export interface StepFinishedEvent extends BaseEvent {
  type: EventType.STEP_FINISHED;
  stepName: string;
}

// 文本消息事件
export interface TextMessageStartEvent extends BaseEvent {
  type: EventType.TEXT_MESSAGE_START;
  messageId: string;
  role: string;
}

export interface TextMessageContentEvent extends BaseEvent {
  type: EventType.TEXT_MESSAGE_CONTENT;
  messageId: string;
  delta: string;
}

export interface TextMessageEndEvent extends BaseEvent {
  type: EventType.TEXT_MESSAGE_END;
  messageId: string;
}

// 工具调用事件
export interface ToolCallStartEvent extends BaseEvent {
  type: EventType.TOOL_CALL_START;
  toolCallId: string;
  toolCallName: string;
  parentMessageId?: string;
}

export interface ToolCallArgsEvent extends BaseEvent {
  type: EventType.TOOL_CALL_ARGS;
  toolCallId: string;
  delta: string;
}

export interface ToolCallEndEvent extends BaseEvent {
  type: EventType.TOOL_CALL_END;
  toolCallId: string;
}

export interface ToolCallResultEvent extends BaseEvent {
  type: EventType.TOOL_CALL_RESULT;
  messageId: string;
  toolCallId: string;
  content: string;
  role?: string;
}

// 状态管理事件
export interface StateSnapshotEvent extends BaseEvent {
  type: EventType.STATE_SNAPSHOT;
  snapshot: any;
}

export interface StateDeltaEvent extends BaseEvent {
  type: EventType.STATE_DELTA;
  delta: any[];
}

export interface MessagesSnapshotEvent extends BaseEvent {
  type: EventType.MESSAGES_SNAPSHOT;
  messages: Message[];
}

// 特殊事件
export interface RawEvent extends BaseEvent {
  type: EventType.RAW;
  event: any;
  source?: string;
}

export interface CustomEvent extends BaseEvent {
  type: EventType.CUSTOM;
  name: string;
  value: any;
}

export type AGUIEvent = 
  | RunStartedEvent
  | RunFinishedEvent
  | RunErrorEvent
  | StepStartedEvent
  | StepFinishedEvent
  | TextMessageStartEvent
  | TextMessageContentEvent
  | TextMessageEndEvent
  | ToolCallStartEvent
  | ToolCallArgsEvent
  | ToolCallEndEvent
  | ToolCallResultEvent
  | StateSnapshotEvent
  | StateDeltaEvent
  | MessagesSnapshotEvent
  | RawEvent
  | CustomEvent;

// 消息类型（对标CopilotKit Message）
export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  isStreaming?: boolean; // 仅用于AI消息流式更新
}

// 工具调用执行状态（内部使用）
export interface ToolCallExecution {
  id: string;
  name: string;
  args: string; // 流式接收的参数字符串
  parsedArgs?: any; // 解析后的参数
  status: 'receiving_args' | 'executing' | 'completed' | 'failed';
  startTime: number;
  endTime?: number;
  result?: any;
  error?: string;
}

// Action注册信息（内部使用）
export interface RegisteredAction {
  name: string;
  description: string;
  parameters: ActionParameter[];
  handler: (args: any) => void | Promise<void>;
  render?: (props: {
    status: 'idle' | 'executing' | 'complete' | 'failed';
    args: any;
  }) => React.JSX.Element | null;
  currentExecution?: {
    status: 'idle' | 'executing' | 'complete' | 'failed';
    args?: any;
    result?: any;
    error?: string;
  };
}

// 简化的工具调用类型（用于历史追踪）
export interface ToolCall {
  id: string;
  name: string;
  args?: any;
  result?: string;
  startTime: number;
  endTime?: number;
  error?: string;
}

// ==================== CopilotKit风格API类型 ====================

// Action参数定义（完全对标CopilotKit）
export interface ActionParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'object[]';
  description: string;
  required?: boolean;
  enum?: string[];
  attributes?: ActionParameter[];
}

// Action配置（完全对标CopilotKit）
export interface UseAGUIActionParams<T = any> {
  name: string;
  description: string;
  parameters: ActionParameter[];
  handler: (args: T) => void | Promise<void>;
  render?: (props: {
    status: 'idle' | 'executing' | 'complete' | 'failed';
    args: T;
  }) => React.JSX.Element | null;
}

// ==================== 内部AG-UI协议类型 ====================

// 工具定义（发送给AG-UI服务端）
export interface ToolDefinition {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
}

// 工具定义（内部包含handler）
export interface Tool extends ToolDefinition {
  handler: (...args: any[]) => any | Promise<any>; // 前端工具执行函数
}

// 活动状态
export interface ActivityStatus {
  type: 'idle' | 'thinking' | 'streaming' | 'tool_calling';
  message: string;
  toolName?: string;
  toolArgs?: string; // 流式构建的参数
}

// 运行状态
export interface RunStatus {
  isRunning: boolean;
  currentStep?: string;
  error?: string;
}

// Agent 配置
export interface AgentConfig {
  agentId?: string;
  conversationId?: string;
  initialState?: any;
  hubUrl?: string;
  autoConnect?: boolean;
}

// 发送消息的输入参数（包含工具定义）
export interface SendMessageInput {
  conversationId: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  tools?: ToolDefinition[]; // 工具定义随消息一起发送
}

// 连接状态
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  FAILED = 'failed'
}

// SignalR 连接配置
export interface SignalRConfig {
  hubUrl: string;
  withCredentials?: boolean;
  transport?: ('webSockets' | 'serverSentEvents' | 'longPolling')[];
  logLevel?: 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'none';
  automaticReconnect?: boolean;
  reconnectOptions?: {
    maxRetries?: number;
    retryDelay?: number;
  };
}

// 事件监听器类型
export type EventListener<T extends AGUIEvent = AGUIEvent> = (event: T) => void;

// 事件过滤器类型
export type EventFilter<T extends AGUIEvent = AGUIEvent> = (event: T) => boolean;

// 性能指标
export interface PerformanceMetrics {
  totalEvents: number;
  averageResponseTime: number;
  toolCallsCount: number;
  errorsCount: number;
  connectionUptime: number;
  lastEventTime?: number;
}

// AG-UI Context 状态
export interface AGUIContextState {
  // 连接状态
  connectionState: ConnectionState;
  connectionError?: string;
  
  // 配置
  config: AgentConfig;
  
  // Action状态
  registeredActions: Map<string, RegisteredAction>;
  activeToolCalls: Map<string, ToolCallExecution>;
  
  // 会话状态
  conversationId: string;
  
  // 工具调用历史
  toolCallHistory: ToolCall[];
}

// AG-UI Context 操作
export interface AGUIContextActions {
  // 连接管理
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  isConnected: boolean;
  
  // Action管理
  registerAction: (action: RegisteredAction) => void;
  unregisterAction: (name: string) => void;
  
  // 工具调用执行
  executeToolCall: (toolCallId: string, name: string, args: any) => Promise<void>;
  
  // 获取当前工具定义
  getToolDefinitions: () => ToolDefinition[];
  
  // 配置更新
  updateConfig: (config: Partial<AgentConfig>) => void;
}

// 完整的 AG-UI Context 类型
export interface AGUIContext extends AGUIContextState, AGUIContextActions {}