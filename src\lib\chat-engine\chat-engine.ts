'use client';

import { 
  ChatEng<PERSON>, 
  ChatEngineConfig, 
  EventBus, 
  ChatStateMachine, 
  MessageService, 
  ConnectionService 
} from './types';
import { ChatEventBus } from './event-bus';
import { ChatStateMachineImpl } from './state-machine';
import { MessageServiceImpl } from './message-service';
import { ConnectionServiceImpl } from './connection-service';

/**
 * 聊天引擎主类
 * 统一管理事件总线、状态机、消息服务和连接服务
 */
export class ChatEngineImpl implements ChatEngine {
  private eventBus: EventBus;
  private stateMachine: ChatStateMachine;
  private messageService: MessageService;
  private connectionService: ConnectionService;
  private initialized = false;
  private config: ChatEngineConfig | null = null;

  constructor() {
    // 初始化事件总线
    this.eventBus = new ChatEventBus({
      enableLogging: process.env.NODE_ENV === 'development',
      maxHistorySize: 1000
    });

    // 初始化状态机
    this.stateMachine = new ChatStateMachineImpl(this.eventBus, {
      enableLogging: process.env.NODE_ENV === 'development'
    });

    // 初始化连接服务
    this.connectionService = new ConnectionServiceImpl(this.eventBus, {
      reconnectInterval: 5000,
      maxRetries: 3,
      enableLogging: process.env.NODE_ENV === 'development'
    });

    // 初始化消息服务（只需要agentId）
    this.messageService = new MessageServiceImpl(this.eventBus, '', {
      enableLogging: process.env.NODE_ENV === 'development'
    });

    // 设置服务间的事件监听
    this.setupServiceIntegration();
  }

  /**
   * 初始化聊天引擎
   */
  async initialize(config: ChatEngineConfig): Promise<void> {
    if (this.initialized) {
      console.warn('[ChatEngine] Already initialized');
      return;
    }

    this.config = config;

    // 更新各服务的配置
    (this.messageService as MessageServiceImpl).updateAgentId(config.agentId);
    this.stateMachine.setConversationId(config.conversationId);

    // 建立连接
    await this.connectionService.connect(config.conversationId, config.agentId);

    // 加载历史消息
    try {
      await this.messageService.loadMessages(config.conversationId);
    } catch (error) {
      console.warn('[ChatEngine] Failed to load initial messages:', error);
    }

    this.initialized = true;

    if (config.enableLogging) {
      console.log('[ChatEngine] Initialized successfully', config);
    }
  }

  /**
   * 销毁聊天引擎
   */
  async destroy(): Promise<void> {
    if (!this.initialized) {
      return;
    }

    // 断开连接
    await this.connectionService.disconnect();

    // 销毁各个服务
    await this.connectionService.destroy();
    (this.messageService as MessageServiceImpl).destroy();
    this.stateMachine.destroy();
    this.eventBus.destroy();

    this.initialized = false;
    this.config = null;

    console.log('[ChatEngine] Destroyed');
  }

  /**
   * 获取事件总线
   */
  getEventBus(): EventBus {
    return this.eventBus;
  }

  /**
   * 获取状态机
   */
  getStateMachine(): ChatStateMachine {
    return this.stateMachine;
  }

  /**
   * 获取消息服务
   */
  getMessageService(): MessageService {
    return this.messageService;
  }

  /**
   * 获取连接服务
   */
  getConnectionService(): ConnectionService {
    return this.connectionService;
  }

  /**
   * 设置服务间的集成
   */
  private setupServiceIntegration(): void {
    // 监听用户发送消息事件，调用消息服务
    this.eventBus.on('USER_SEND_MESSAGE', async (event) => {
      const payload = event.payload as any;
      if (payload?.conversationId && payload?.content) {
        console.log('📤 [ChatEngine] USER_SEND_MESSAGE事件处理', {
          conversationId: payload.conversationId,
          content: payload.content.slice(0, 50)
        });
        
        try {
          await this.messageService.sendMessage(
            payload.conversationId,
            payload.content
          );
          
          console.log('✅ [ChatEngine] 消息发送成功');
        } catch (error) {
          console.error('[ChatEngine] Failed to send message:', error);
        }
      }
    });

    // 监听消息加载事件
    this.eventBus.on('LOAD_MESSAGES', async (event) => {
      const payload = event.payload as any;
      if (payload?.conversationId) {
        console.log('📂 [ChatEngine] LOAD_MESSAGES事件触发', {
          conversationId: payload.conversationId,
          eventSource: event.source,
          stackTrace: new Error().stack?.split('\n').slice(1, 4).join('\n')
        });
        
        try {
          await this.messageService.loadMessages(payload.conversationId);
        } catch (error) {
          console.error('[ChatEngine] Failed to load messages:', error);
        }
      }
    });

    // 监听消息加载完成事件，更新状态机
    this.eventBus.on('MESSAGES_LOADED', (event) => {
      const payload = event.payload as any;
      if (payload?.messages && Array.isArray(payload.messages)) {
        const currentState = this.stateMachine.getStateData();
        const currentMessages = currentState.messages || [];
        
        console.log('📥 [ChatEngine] MESSAGES_LOADED事件触发', {
          serverMessagesCount: payload.messages.length,
          currentMessagesCount: currentMessages.length,
          conversationId: payload.conversationId || currentState.conversationId,
          currentState: currentState.currentState,
          stackTrace: new Error().stack?.split('\n').slice(1, 4).join('\n')
        });
        
        // 🔧 智能合并消息，而不是直接覆盖
        // 1. 保留本地的pending/sent状态消息（可能还没有被服务器确认）
        const localPendingMessages = currentMessages.filter(msg => 
          msg.status === 'pending' || msg.status === 'sent'
        );
        
        console.log('🔍 [ChatEngine] 消息状态分析', {
          localPendingCount: localPendingMessages.length,
          localPendingMessages: localPendingMessages.map(m => ({
            id: m.id,
            role: m.role,
            content: m.content.slice(0, 50),
            status: m.status,
            timestamp: m.timestamp
          }))
        });
        
        // 2. 从服务器消息中过滤掉与本地pending消息重复的内容
        const serverMessages = payload.messages.filter((serverMsg: any) => {
          return !localPendingMessages.some(localMsg => 
            localMsg.content === serverMsg.content && 
            localMsg.role === serverMsg.role &&
            Math.abs(localMsg.timestamp - serverMsg.timestamp) < 5000 // 5秒内的消息认为是重复
          );
        });
        
        // 3. 合并消息：服务器消息 + 本地pending消息，按时间排序
        const mergedMessages = [...serverMessages, ...localPendingMessages]
          .sort((a, b) => a.timestamp - b.timestamp);
        
        console.log('🔄 [ChatEngine] 消息合并结果', {
          serverCount: serverMessages.length,
          localPendingCount: localPendingMessages.length,
          mergedCount: mergedMessages.length,
          beforeMessageIds: currentMessages.map(m => m.id),
          afterMessageIds: mergedMessages.map(m => m.id)
        });
        
        console.warn('⚠️ [ChatEngine] 即将直接覆盖状态机消息数据!');
        
        // 直接更新状态机的消息数据
        const beforeStateData = { ...currentState };
        (this.stateMachine as ChatStateMachineImpl)['stateData'] = {
          ...currentState,
          messages: mergedMessages
        };
        (this.stateMachine as ChatStateMachineImpl)['notifySubscribers']();
        
        console.log('✅ [ChatEngine] 状态机消息已更新', {
          beforeCount: beforeStateData.messages.length,
          afterCount: mergedMessages.length
        });
      }
    });

    // 监听AGUI流式消息事件
    this.eventBus.on('STREAMING_START', (event) => {
      const payload = event.payload as any;
      if (payload?.messageId) {
        console.log('🎬 [ChatEngine] STREAMING_START事件处理', {
          messageId: payload.messageId,
          conversationId: payload.conversationId
        });
        
        // 转发给状态机处理
        this.stateMachine.transition(event);
      }
    });

    this.eventBus.on('STREAMING_CONTENT', (event) => {
      const payload = event.payload as any;
      if (payload?.messageId && payload?.delta) {
        console.log('📝 [ChatEngine] STREAMING_CONTENT事件处理', {
          messageId: payload.messageId,
          deltaLength: payload.delta.length,
          conversationId: payload.conversationId
        });
        
        // 转发给状态机处理
        this.stateMachine.transition(event);
      }
    });

    this.eventBus.on('STREAMING_END', (event) => {
      const payload = event.payload as any;
      if (payload?.messageId) {
        console.log('🏁 [ChatEngine] STREAMING_END事件处理', {
          messageId: payload.messageId,
          conversationId: payload.conversationId
        });
        
        // 转发给状态机处理
        this.stateMachine.transition(event);
      }
    });

    // 监听工具调用事件
    this.eventBus.on('TOOL_CALL_START', (event) => {
      const payload = event.payload as any;
      if (payload?.toolCallId) {
        console.log('🔧 [ChatEngine] TOOL_CALL_START事件处理', {
          toolCallId: payload.toolCallId,
          toolName: payload.toolName,
          conversationId: payload.conversationId
        });
        
        // 转发给状态机处理
        this.stateMachine.transition(event);
      }
    });

    this.eventBus.on('TOOL_CALL_ARGS', (event) => {
      const payload = event.payload as any;
      if (payload?.toolCallId && payload?.delta) {
        console.log('📋 [ChatEngine] TOOL_CALL_ARGS事件处理', {
          toolCallId: payload.toolCallId,
          deltaLength: payload.delta.length,
          conversationId: payload.conversationId
        });
        
        // 转发给状态机处理
        this.stateMachine.transition(event);
      }
    });

    this.eventBus.on('TOOL_CALL_END', (event) => {
      const payload = event.payload as any;
      if (payload?.toolCallId) {
        console.log('✅ [ChatEngine] TOOL_CALL_END事件处理', {
          toolCallId: payload.toolCallId,
          conversationId: payload.conversationId
        });
        
        // 转发给状态机处理
        this.stateMachine.transition(event);
      }
    });

    this.eventBus.on('TOOL_CALL_RESULT', (event) => {
      const payload = event.payload as any;
      if (payload?.toolCallId) {
        console.log('📊 [ChatEngine] TOOL_CALL_RESULT事件处理', {
          toolCallId: payload.toolCallId,
          conversationId: payload.conversationId
        });
        
        // 转发给状态机处理
        this.stateMachine.transition(event);
      }
    });

    this.eventBus.on('RUN_STARTED', (event) => {
      const payload = event.payload as any;
      if (payload?.runId) {
        console.log('🚀 [ChatEngine] RUN_STARTED事件处理', {
          runId: payload.runId,
          conversationId: payload.conversationId
        });
        
        // 转发给状态机处理
        this.stateMachine.transition(event);
      }
    });

    this.eventBus.on('RUN_FINISHED', (event) => {
      const payload = event.payload as any;
      if (payload?.runId) {
        console.log('🎉 [ChatEngine] RUN_FINISHED事件处理', {
          runId: payload.runId,
          conversationId: payload.conversationId
        });
        
        // 转发给状态机处理
        this.stateMachine.transition(event);
      }
    });

    // 监听连接状态变化
    this.eventBus.on('CONNECTION_STATUS', (event) => {
      const payload = event.payload as any;
      if (payload?.status === 'disconnected') {
        // 连接断开时清除错误状态
        this.stateMachine.clearError();
      }
    });
  }

  /**
   * 更新配置
   */
  async updateConfig(newConfig: Partial<ChatEngineConfig>): Promise<void> {
    if (!this.initialized || !this.config) {
      throw new Error('Engine not initialized');
    }

    const updatedConfig = { ...this.config, ...newConfig };
    const needsReconnect = this.config.conversationId !== updatedConfig.conversationId ||
                          this.config.agentId !== updatedConfig.agentId;

    this.config = updatedConfig;

    // 更新各服务配置
    if (newConfig.agentId) {
      (this.messageService as MessageServiceImpl).updateAgentId(newConfig.agentId);
    }

    if (newConfig.conversationId) {
      this.stateMachine.setConversationId(newConfig.conversationId);
    }

    // 如果需要重连
    if (needsReconnect) {
      await this.connectionService.disconnect();
      await this.connectionService.connect(updatedConfig.conversationId, updatedConfig.agentId);
      
      // 重新加载消息
      try {
        await this.messageService.loadMessages(updatedConfig.conversationId);
      } catch (error) {
        console.warn('[ChatEngine] Failed to reload messages after config update:', error);
      }
    }

    if (this.config.enableLogging) {
      console.log('[ChatEngine] Config updated', updatedConfig);
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): ChatEngineConfig | null {
    return this.config ? { ...this.config } : null;
  }

  /**
   * 检查是否已初始化
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 获取调试信息
   */
  getDebugInfo(): {
    initialized: boolean;
    config: ChatEngineConfig | null;
    eventBus: unknown;
    stateMachine: unknown;
    messageService: unknown;
    connectionService: unknown;
  } {
    return {
      initialized: this.initialized,
      config: this.config,
      eventBus: (this.eventBus as ChatEventBus).getDebugInfo(),
      stateMachine: this.stateMachine.getStateData(),
      messageService: (this.messageService as MessageServiceImpl).getDebugInfo(),
      connectionService: (this.connectionService as ConnectionServiceImpl).getDebugInfo()
    };
  }
}

// 导出单例实例
export const chatEngine = new ChatEngineImpl();