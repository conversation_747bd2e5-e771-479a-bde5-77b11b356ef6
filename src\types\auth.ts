export interface AuthConfig {
  Authority: string;
  Realm: string;
  ClientId: string;
}

export interface UserProfile {
  id?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  name?: string; // 添加name属性
  email?: string;
  emailVerified?: boolean;
  attributes?: Record<string, unknown>;
}

export interface AuthContextType {
  isAuthenticated: boolean;
  setAuthenticated: (authenticated: boolean) => void;
  userProfile: UserProfile | null;
  user: UserProfile | null; // 添加user属性作为userProfile的别名
  keycloakInstance: unknown;
  login: () => void;
  logout: (redirectUri?: string) => void;
  token: string | null;
  isInitialized: boolean; // 添加初始化状态
}

export interface AuthStore {
  token: string | null;
  user: UserProfile | null;
}