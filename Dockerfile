# 多阶段构建：Node官方镜像 + 优化构建
# Stage 1: 依赖安装阶段
FROM node:20-slim AS deps

# 安装系统依赖（Canvas库所需）
RUN apt-get update && apt-get install -y \
    build-essential \
    libcairo2-dev \
    libpango1.0-dev \
    libjpeg-dev \
    libgif-dev \
    librsvg2-dev \
    python3 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制包管理文件
COPY package.json pnpm-lock.yaml* ./

# 安装 pnpm
RUN npm install -g pnpm

# 安装依赖
RUN pnpm install --frozen-lockfile --prod=false

# Stage 2: 构建阶段
FROM node:20-slim AS builder

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    libcairo2-dev \
    libpango1.0-dev \
    libjpeg-dev \
    libgif-dev \
    librsvg2-dev \
    python3 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 从依赖阶段复制node_modules
COPY --from=deps /app/node_modules ./node_modules

# 复制源代码
COPY . .

# 安装 pnpm
RUN npm install -g pnpm

# 构建参数
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_ZIKO_API_URL
ARG NEXT_PUBLIC_CONVERSATION_AGENT_ID
ARG NEXT_PUBLIC_ENV=production

# 设置环境变量
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_ZIKO_API_URL=$NEXT_PUBLIC_ZIKO_API_URL
ENV NEXT_PUBLIC_CONVERSATION_AGENT_ID=$NEXT_PUBLIC_CONVERSATION_AGENT_ID
ENV NEXT_PUBLIC_ENV=$NEXT_PUBLIC_ENV
ENV NODE_ENV=production

# 构建应用
RUN pnpm build

# Stage 3: 运行阶段
FROM node:20-slim AS runner

# 安装运行时系统依赖
RUN apt-get update && apt-get install -y \
    libcairo2 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libjpeg62-turbo \
    libgif7 \
    librsvg2-2 \
    dumb-init \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 创建非root用户
RUN groupadd --gid 1001 nodejs && \
    useradd --uid 1001 --gid nodejs --shell /bin/bash --create-home nextjs

# 复制构建产物
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# 设置文件权限
RUN chown -R nextjs:nodejs /app
USER nextjs

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "server.js"]