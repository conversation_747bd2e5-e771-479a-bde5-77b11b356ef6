"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  getEmployees,
  getEmployeeTags,
  enableEmployee,
  disableEmployee,
  deleteEmployee,
  getManageEmployees,
} from "@/api/employee";
import { useAuthContext } from "@/lib/auth/AuthProvider";
import EmployeeCard from "@/components/ai-staff/EmployeeCard";
import { createConversation } from "@/services/conversation";
import { useRouter } from "next/navigation";
import Dropdown from "@/components/common/Dropdown";
import { Employee } from "@/types/employee";
import { useConfirmDialog } from "@/components/common/ConfirmDialog";
import toast from "react-hot-toast";

export default function AIStaffPage() {
  const { isAuthenticated, user } = useAuthContext();
  const [search, setSearch] = useState("");
  const [employees, setEmployees] = useState<any[]>([]);
  const [notFound, setNotFound] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0); // 添加总数状态
  const router = useRouter();
  const PAGE_SIZE = 10;

  // 添加标签和排序状态
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortOrder, setSortOrder] = useState("created_time");

  // 添加tab状态
  const [activeTab, setActiveTab] = useState<"personal" | "shared">("personal");

  // 标签列表状态
  const [tagOptions, setTagOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const [loadingTags, setLoadingTags] = useState(false);

  // 添加编辑和删除状态
  const [editingEmployeeId, setEditingEmployeeId] = useState<string | null>(
    null
  );
  const [deletingEmployeeId, setDeletingEmployeeId] = useState<string | null>(
    null
  );

  // 使用确认对话框
  const { confirmDialog, showConfirm } = useConfirmDialog();

  // 排序选项 - 只保留一个按创建时间降序的选项
  const sortOptions = [{ label: "按创建时间降序", value: "created_time" }];

  // 加载标签列表
  const loadTags = useCallback(async () => {
    if (loadingTags) return;
    setLoadingTags(true);
    try {
      const tags = await getEmployeeTags();
      if (Array.isArray(tags)) {
        const formattedTags = tags.map((tag) => ({
          label: tag,
          value: tag,
        }));
        setTagOptions(formattedTags);
      }
    } catch (err) {
      console.error("获取标签列表失败", err);
    } finally {
      setLoadingTags(false);
    }
  }, [loadingTags]);

  const loadEmployees = useCallback(
    async (newPage: number, searchValue = search) => {
      if (loading) return;
      setLoading(true);
      setError(null);
      setNotFound(false);
      try {
        const params: any = {
          "Pager.Page": newPage,
          "Pager.Size": PAGE_SIZE,
        };
        if (searchValue) params.Name = searchValue.trim();
        if (selectedTags.length > 0) {
          params.Tags = selectedTags.join(",");
        }
        params["Pager.Sort"] = "created_time";
        params["Pager.Order"] = "desc";

        let res;
        if (activeTab === "personal") {
          if (user && user.id) {
            params.CreateUserId = user.id;
            res = await getManageEmployees(params);
          } else {
            res = { items: [], count: 0 };
          }
        } else if (activeTab === "shared") {
          params.IsPublic = true;
          params.ExcludePersonalCreated = true;
          res = await getEmployees(params);
        }

        setEmployees(res.items || []);
        const count = res.count || 0;
        setTotalCount(count);
        const totalPages = Math.max(1, Math.ceil(count / PAGE_SIZE));
        setHasMore(newPage < totalPages);
        setPage(newPage);
        setNotFound(!res.items || res.items.length === 0);
      } catch {
        setEmployees([]);
        setError("AI员工列表加载失败");
      } finally {
        setLoading(false);
      }
    },
    [loading, search, selectedTags, activeTab, user]
  );

  // 计算总页数
  const totalPages = Math.max(1, Math.ceil(totalCount / PAGE_SIZE));

  useEffect(() => {
    if (!isAuthenticated) return;
    // loadEmployees(1, "");
    loadTags(); // 加载标签列表
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]);

  // 当切换tab时重新加载数据
  useEffect(() => {
    if (isAuthenticated) {
      loadEmployees(1, search);
    }
  }, [activeTab, isAuthenticated]);

  const handleSearchKeyPress = async (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      await loadEmployees(1, search);
    }
  };

  const handleClear = () => {
    setSearch("");
    loadEmployees(1, "");
  };

  // 处理标签变化
  const handleTagChange = (value: string | string[]) => {
    if (Array.isArray(value)) {
      // 使用函数式更新，确保在最新状态下执行
      setSelectedTags(value);
      // 不在这里立即加载，而是在确认时加载
    }
  };

  // 处理标签选择确认
  const handleTagSelectConfirm = () => {
    // 确认选择后加载数据
    loadEmployees(1, search);
  };

  // 处理排序变化
  const handleSortChange = (value: string | string[]) => {
    if (typeof value === "string") {
      // 使用函数式更新，确保在最新状态下执行
      setSortOrder(value);
      // 使用setTimeout确保状态更新后再调用loadEmployees
      setTimeout(() => {
        loadEmployees(1, search);
      }, 0);
    }
  };

  // 处理编辑员工
  const handleEditEmployee = (employee: Employee) => {
    router.push(`/ai-staff/edit/${employee.id}`);
  };

  // 处理删除员工
  const handleDeleteEmployee = async (employee: Employee) => {
    showConfirm({
      title: "删除AI员工",
      content: `"${employee.name}"将被删除无法恢复`,
      onConfirm: async () => {
        setDeletingEmployeeId(employee.id);
        try {
          await deleteEmployee(employee.id);
          // 显示成功提示
          toast.success(`已成功删除AI员工"${employee.name}"`);
          // 删除成功后重新加载数据
          loadEmployees(page, search);
        } catch (error) {
          console.error("删除AI员工失败:", error);
          toast.error("删除AI员工失败，请稍后重试");
        } finally {
          setDeletingEmployeeId(null);
        }
      },
    });
  };

  // 处理启用/禁用员工
  const handleToggleEmployeeStatus = async (employee: Employee) => {
    try {
      if (employee.disabled) {
        // 当前是禁用状态，需要启用
        await enableEmployee(employee.id);
        toast.success(`已成功启用AI员工"${employee.name}"`);
      } else {
        // 当前是启用状态，需要禁用
        await disableEmployee(employee.id);
        toast.success(`已成功禁用AI员工"${employee.name}"`);
      }
      // 状态切换完成后重新加载数据
      loadEmployees(page, search);
    } catch (error) {
      console.error("切换AI员工状态失败:", error);
      toast.error(
        `${employee.disabled ? "启用" : "禁用"}AI员工失败，请稍后重试`
      );
    }
  };

  return (
    <div className="h-full bg-[#FAFAFA] flex flex-col">
      {/* 头部区域 */}
      <div className="h-[49px] w-full px-6 flex items-center justify-between">
        <div className="flex items-center">
          <button
            className="bg-[#000000E0] text-[#FFFFFF] font-medium rounded-[8px] text-[14px] px-3 py-2 flex items-center justify-center cursor-pointer hover:bg-[#000000F0]"
            onClick={() => router.push("/ai-staff/create")}
          >
            <img
              src="/assets/ai-employees/add.svg"
              alt="add"
              className="mr-2"
            />
            创建员工
          </button>
          <div className="h-[24px] w-px bg-[#0000000F] mx-2" />
          <div className="w-[200px]">
            <div className="flex items-center bg-[#00000008] h-[37px] w-full px-3  rounded-[8px]">
              <img
                src="/assets/search/search.svg"
                alt="search"
                className="w-4 h-4"
              />
              <input
                className="flex-1 min-w-0 h-full px-2 bg-transparent font-normal outline-none text-sm text-[#000000B8] placeholder:text-[#00000066]"
                type="text"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyDown={handleSearchKeyPress}
                placeholder="搜索"
              />
              {search && (
                <button
                  type="button"
                  className="ml-1 focus:outline-none cursor-pointer hover:bg-[#00000014] rounded-full p-0.5 transition-colors"
                  onClick={handleClear}
                  tabIndex={-1}
                >
                  <img
                    src="/assets/search/clean.svg"
                    alt="clear"
                    className="w-4 h-4"
                  />
                </button>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Dropdown
            options={tagOptions}
            value={selectedTags}
            onChange={handleTagChange}
            placeholder="标签"
            className="w-[200px]"
            multiple={true}
            onClose={handleTagSelectConfirm}
          />
          <Dropdown
            options={sortOptions}
            value={sortOrder}
            onChange={handleSortChange}
            placeholder="按创建时间排序"
            className="w-[150px]"
            showClearButton={false}
          />
        </div>
      </div>

      {/* Tab切换区域 */}
      <div className="h-[48px] w-full px-6 flex items-center">
        <div className="flex items-center">
          <button
            className={`px-3 py-[6px] rounded-[8px] text-[13px] font-medium transition-colors cursor-pointer ${
              activeTab === "personal"
                ? "bg-[#0000000A] text-[#000000E0]"
                : "bg-transparent text-[#00000066]"
            }`}
            onClick={() => setActiveTab("personal")}
          >
            个人
          </button>
          <button
            className={`px-3 py-[6px] rounded-[8px] text-[13px] font-medium transition-colors cursor-pointer ${
              activeTab === "shared"
                ? "bg-[#0000000A] text-[#000000E0]"
                : "bg-transparent text-[#00000066]"
            }`}
            onClick={() => setActiveTab("shared")}
          >
            共享
          </button>
        </div>
      </div>

      {/* 主内容区 - 使用flex布局，让内容区和分页区分别占据各自空间 */}
      <div className="flex-1 flex flex-col min-h-0">
        {/* 内容区 - 可滚动 */}
        <div className="flex-1 overflow-auto px-6">
          {!isAuthenticated ? (
            <div className="text-center text-gray-400 py-10">请先登录</div>
          ) : error ? (
            <div className="text-center text-red-500 py-10">{error}</div>
          ) : notFound ? (
            <div className="text-center text-gray-400 py-10">未找到AI员工</div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 w-full">
              {employees.map((employee, idx) =>
                activeTab === "shared" ? (
                  <EmployeeCard
                    key={idx}
                    employee={employee}
                    onStart={() =>
                      router.push(`/ai-staff/preview/${employee.id}`)
                    }
                    hideStatusDot={true}
                    footerRight={null}
                  />
                ) : (
                  <EmployeeCard
                    key={idx}
                    employee={employee}
                    onEdit={handleEditEmployee}
                    onDelete={handleDeleteEmployee}
                    onToggleStatus={handleToggleEmployeeStatus}
                    isEditing={editingEmployeeId === employee.id}
                    isDeleting={deletingEmployeeId === employee.id}
                  />
                )
              )}
            </div>
          )}

          {loading && (
            <div className="text-center text-gray-400 py-4">加载中...</div>
          )}
        </div>

        {/* 分页区 - 固定在底部 */}
        <div className="flex-shrink-0 h-[65px] px-6 flex items-center justify-center">
          {employees.length > 0 && (
            <div className="flex justify-center items-center space-x-4">
              <button
                onClick={() => loadEmployees(page - 1)}
                disabled={page <= 1 || loading}
                className={`${
                  page <= 1 || loading
                    ? "cursor-not-allowed"
                    : "cursor-pointer hover:bg-gray-50"
                } `}
              >
                <img
                  src={
                    page <= 1 || loading
                      ? "/assets/ai-employees/page-left-false.svg"
                      : "/assets/ai-employees/page-left.svg"
                  }
                  alt="page-left"
                />
              </button>

              {/* 页码按钮 */}
              {(() => {
                // 计算要显示的页码范围
                let startPage = Math.max(1, page - 1);
                let endPage = Math.min(page + 1, totalPages);

                // 调整页码范围，确保不超出总页数
                if (endPage > totalPages) {
                  endPage = totalPages;
                  startPage = Math.max(1, endPage - 2);
                }

                // 生成页码按钮
                const pageButtons = [];
                for (let i = startPage; i <= endPage; i++) {
                  pageButtons.push(
                    <span
                      key={i}
                      onClick={() => i !== page && loadEmployees(i)}
                      className={`text-[14px] font-normal cursor-pointer ${
                        i === page
                          ? "text-[#000000E0] bg-white"
                          : "text-[#00000099] bg-[#0000000A] hover:text-[#000000B3]"
                      } px-[12px] py-[6px] rounded-[8px]`}
                      style={
                        i === page
                          ? {
                              boxShadow: "0px 2px 4px 0px #00000014",
                            }
                          : {}
                      }
                    >
                      {i}
                    </span>
                  );
                }

                return pageButtons;
              })()}

              <button
                onClick={() => loadEmployees(page + 1)}
                disabled={!hasMore || loading}
                className={`${
                  !hasMore || loading ? "cursor-not-allowed" : "cursor-pointer"
                }`}
              >
                <img
                  src={
                    !hasMore || loading
                      ? "/assets/ai-employees/page-right-false.svg"
                      : "/assets/ai-employees/page-right.svg"
                  }
                  alt="page-right"
                />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 渲染确认对话框 */}
      {confirmDialog}
    </div>
  );
}
