// 自定义 hooks 导出文件
// 优先使用 ahooks 提供的 Hook，减少重复实现

// 示例：结合 ahooks 的自定义 hook
import { 
  useRequest, 
  useLocalStorageState, 
  useBoolean, 
  useDebounce, 
  useCounter
} from 'ahooks';

/**
 * 用户资料管理 Hook
 * 结合 useRequest 和 useBoolean 提供完整的用户资料管理功能
 */
export function useUserProfile(userId: string) {
  // 获取用户数据
  const { data: user, loading, error, mutate } = useRequest(
    () => {
      if (!userId) return Promise.resolve(null);
      return fetch(`/api/users/${userId}`).then(res => res.json());
    },
    {
      ready: !!userId,
      refreshDeps: [userId]
    }
  );

  // 编辑状态管理
  const [isEditing, { setTrue: startEdit, setFalse: cancelEdit }] = useBoolean(false);

  // 保存用户数据
  const { run: saveUser, loading: saving } = useRequest(
    (userData: Record<string, unknown>) => {
      return fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData)
      }).then(res => res.json());
    },
    {
      manual: true,
      onSuccess: (result) => {
        mutate(result); // 更新本地数据
        cancelEdit(); // 退出编辑状态
      }
    }
  );

  return {
    user,
    loading,
    error,
    isEditing,
    startEdit,
    cancelEdit,
    saveUser,
    saving
  };
}

/**
 * 应用设置 Hook
 * 使用 useLocalStorageState 管理应用设置
 * 体现 ahooks 优先使用原则
 */
export function useAppSettings() {
  // 使用 useLocalStorageState 持久化设置
  const [settings, setSettings] = useLocalStorageState('app-settings', {
    defaultValue: {
      theme: 'light',
      language: 'zh-CN',
      sidebarCollapsed: false,
      notifications: true,
      autoSave: true
    }
  });

  const updateSettings = (newSettings: Partial<typeof settings>) => {
    setSettings({ ...settings, ...newSettings });
  };

  const resetSettings = () => {
    const defaultSettings = {
      theme: 'light',
      language: 'zh-CN',
      sidebarCollapsed: false,
      notifications: true,
      autoSave: true
    };
    setSettings(defaultSettings);
  };

  return {
    settings,
    updateSettings,
    resetSettings
  };
}

/**
 * 搜索 Hook
 * 结合 useDebounce 和 useRequest 实现搜索功能
 * 体现 ahooks 优先使用原则
 */
export function useSearch<T>(
  searchFn: (keyword: string) => Promise<T[]>,
  options: {
    debounceWait?: number;
    minLength?: number;
  } = {}
) {
  const { debounceWait = 300, minLength = 1 } = options;
  
  // 使用 useLocalStorageState 持久化搜索关键词
  const [keyword, setKeyword] = useLocalStorageState('search-keyword', {
    defaultValue: ''
  });

  // 使用 useDebounce 防抖处理
  const debouncedKeyword = useDebounce(keyword, { wait: debounceWait });

  // 使用 useRequest 处理搜索请求
  const { data, loading, error } = useRequest(
    () => searchFn(debouncedKeyword),
    {
      ready: debouncedKeyword.length >= minLength,
      refreshDeps: [debouncedKeyword]
    }
  );

  return {
    keyword,
    setKeyword,
    debouncedKeyword,
    results: data || [],
    loading,
    error
  };
}

/**
 * 表单 Hook
 * 使用 useLocalStorageState 和 useBoolean 管理表单状态
 */
export function useForm<T extends Record<string, unknown>>(initialValues: T) {
  // 使用 useLocalStorageState 管理表单数据
  const [values, setValues] = useLocalStorageState<T>('form-values', {
    defaultValue: initialValues
  });
  
  // 使用 useBoolean 管理表单状态
  const [isSubmitting, { setTrue: setSubmitting, setFalse: setNotSubmitting }] = useBoolean(false);
  const [isDirty, { setTrue: setDirty, setFalse: setNotDirty }] = useBoolean(false);

  const setValue = (field: keyof T, value: T[keyof T]) => {
    setValues({ ...values, [field]: value });
    setDirty();
  };

  const resetForm = () => {
    setValues(initialValues);
    setNotDirty();
    setNotSubmitting();
  };

  return {
    values,
    setValue,
    setValues,
    resetForm,
    isSubmitting,
    setSubmitting,
    setNotSubmitting,
    isDirty,
    setDirty,
    setNotDirty
  };
}

/**
 * 列表管理 Hook
 * 使用 useCounter 和 useRequest 管理分页列表
 */
export function usePaginatedList<T>(
  fetchFn: (page: number, pageSize: number) => Promise<{ data: T[]; total: number }>,
  options: {
    pageSize?: number;
    autoLoad?: boolean;
  } = {}
) {
  const { pageSize = 10, autoLoad = true } = options;
  
  // 使用 useCounter 管理页码
  const [currentPage, { inc: nextPage, dec: prevPage, set: setPage }] = useCounter(1, { min: 1 });
  
  // 使用 useRequest 管理数据加载
  const { data, loading, error, refresh } = useRequest(
    () => fetchFn(currentPage, pageSize),
    {
      ready: autoLoad,
      refreshDeps: [currentPage, pageSize]
    }
  );

  const totalPages = Math.ceil((data?.total || 0) / pageSize);

  return {
    data: data?.data || [],
    total: data?.total || 0,
    loading,
    error,
    currentPage,
    totalPages,
    pageSize,
    nextPage: currentPage < totalPages ? nextPage : undefined,
    prevPage: currentPage > 1 ? prevPage : undefined,
    setPage,
    refresh
  };
}

// ===== 聊天系统相关 Hooks =====

// 🎯 主要聊天 Hooks（推荐使用）
// 位置：@/store/chatStore
// 说明：这是项目的主要聊天状态管理系统，提供完整的会话和消息管理功能
// 
// 可用的 hooks：
// - useChatState() - 聊天状态管理
// - useChatMessages() - 消息列表管理  
// - useChatInput() - 输入状态管理
// - useConversationList() - 会话列表管理
// - useConversationState() - 会话状态管理
//
// 使用示例：
// import { useChatState, useChatMessages, useChatInput } from '@/store/chatStore';

export { useChatEngine } from './useChatEngine'; // AGUI版本的引擎hook

export type { UseAGUIActionParams } from './useAGUIAction';
export type { UseChatEngineOptions, UseChatEngineReturn } from './useChatEngine';

// 导出会话相关类型
export type {
  UseConversationStateReturn,
  UseConversationActionsReturn,
  ConversationItem,
  ConversationMessage,
  CreateConversationParams,
  ConversationConfig,
  ConversationError
} from '@/types/conversation';

// 导出 ahooks 中常用的 hooks，方便统一管理
export {
  useRequest,
  useLocalStorageState,
  useDebounce,
  useThrottle,
  useBoolean,
  useToggle,
  useInterval,
  useTimeout,
  useTitle,
  useResponsive,
  useSize,
  useScroll,
  useKeyPress,
  useClickAway,
  useDrop,
  useDrag
} from 'ahooks';