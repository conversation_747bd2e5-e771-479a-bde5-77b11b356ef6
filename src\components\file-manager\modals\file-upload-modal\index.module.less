.uploader {
  // :global {
  //   .ant-upload-list {
  //     max-height: 40vh;
  //     overflow-y: auto;
  //   }
  // }
}

.uploadLimit {
  color: red;
  font-size: 12px;
}

.uploadModal {
  // :global {
  //   .ant-modal-header {
  //     margin-bottom: 16px;
  //   }

  //   .ant-modal-body {
  //     height: 200px;
  //   }

  //   .ant-modal-content {
  //     padding: 24px;
  //     border-radius: 16px;
  //     box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
  //   }
  // }
}

.customFooter {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;

  // 按钮共同样式
  .cancelButton,
  .confirmButton {
    height: 40px !important;
    padding: 8px 24px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease-in-out !important;
    box-shadow: none !important;
  }

  // 取消按钮特有样式
  .cancelButton {
    border: 1px solid #e8e8e8 !important;
    background-color: #ffffff !important;
    color: #666666 !important;

    &:hover,
    &:focus {
      border-color: #d9d9d9 !important;
      background-color: #fafafa !important;
      color: #333333 !important;
    }
  }

  // 确认按钮特有样式
  .confirmButton {
    border: none !important;
    background-color: #4455f2 !important;
    color: #ffffff !important;

    &:hover,
    &:focus {
      background-color: #3441d9 !important;
      color: #ffffff !important;
    }

    &:disabled {
      background-color: #c3c8fa !important;
      cursor: not-allowed !important;
      color: #ffffff !important;
      border: none !important;
    }
  }
}