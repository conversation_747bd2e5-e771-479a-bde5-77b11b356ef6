import { useFetchFileList } from '@/hooks/file-manager-hooks';
import { IFile } from '@/interfaces/database/file-manager';
import { formatDate } from '@/utils/date';
import { Button, Flex, Space, Table, Tag, Typography } from 'antd';
import { useState } from 'react';
import { ColumnsType } from 'antd/es/table';
import ActionCell from './action-cell';
import FileToolbar from './file-toolbar';
import {
  useGetRowSelection,
  useHandleConnectToKnowledge,
  useHandleCreateFolder,
  useHandleMoveFile,
  useHandleUploadFile,
  useNavigateToOtherFolder,
  useRenameCurrentFile,
  useEditCurrentFile,
  useGetFolderId,
  useHandleCardDelete,
} from './hooks';
import { useDownloadFile } from '@/hooks/file-manager-hooks';

import FileUploadModal from '@/components/file-manager/modals/file-upload-modal';
import RenameModal from '@/components/file-manager/modals/rename-modal';
import SvgIcon from '@/components/file-manager/common/svg-icon';
import { useTranslate } from '@/hooks/common-hooks';
import { formatNumberWithThousandsSeparator } from '@/utils/common-util';
import { getExtension } from '@/utils/document-util';
import ConnectToKnowledgeModal from '@/components/file-manager/modals/connect-to-knowledge-modal';
import FolderCreateModal from '@/components/file-manager/modals/folder-create-modal';
import FileMovingModal from '@/components/file-manager/modals/move-file-modal';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import FileCardList from './file-card-list';
import { MoreOutlined, FolderOutlined, FileOutlined } from '@ant-design/icons';
import { Key } from 'react';



const { Text } = Typography;

// 创建 QueryClient 实例  
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes (React Query v5 用 gcTime)
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

const FileManager = () => {
  const { t } = useTranslate('fileManager');
  const { rowSelection, setSelectedRowKeys } = useGetRowSelection();
  const [isBatchMode, setIsBatchMode] = useState(false);
  const currentFolderId = useGetFolderId();

  // 判断是否为根目录（主页）
  const isRootDirectory = !currentFolderId;

  // 批量操作相关函数
  const handleEnterBatchMode = () => {
    setIsBatchMode(true);
    setSelectedRowKeys([]);
  };

  const handleExitBatchMode = () => {
    setIsBatchMode(false);
    setSelectedRowKeys([]);
  };

  const navigateToOtherFolder = useNavigateToOtherFolder();
  const {
    fileEditVisible,
    fileEditLoading,
    hideFileEditModal,
    showFileEditModal,
    onFileEditOk,
    initialName,
    initialDescription,
    initialTags,
  } = useEditCurrentFile();
  const {
    folderCreateModalVisible,
    showFolderCreateModal,
    hideFolderCreateModal,
    folderCreateLoading,
    onFolderCreateOk,
  } = useHandleCreateFolder();
  const {
    fileUploadVisible,
    hideFileUploadModal,
    showFileUploadModal,
    fileUploadLoading,
    onFileUploadOk,
  } = useHandleUploadFile();
  const {
    connectToKnowledgeVisible,
    hideConnectToKnowledgeModal,
    showConnectToKnowledgeModal,
    onConnectToKnowledgeOk,
    initialValue,
    connectToKnowledgeLoading,
  } = useHandleConnectToKnowledge();
  const {
    showMoveFileModal,
    moveFileVisible,
    onMoveFileOk,
    hideMoveFileModal,
    moveFileLoading,
  } = useHandleMoveFile(setSelectedRowKeys);
  const { pagination, data, searchString, handleInputChange, loading } =
    useFetchFileList();
  const { handleCardDelete } = useHandleCardDelete();


  // 处理卡片重命名
  const handleCardRename = (file: IFile) => {
    showFileEditModal(file);
  };

  // 表格列配置
  const columns: ColumnsType<IFile> = [
    {
      title: t('name'),
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      render(value, record) {
        return (
          <Flex gap={10} align="center">
            <SvgIcon
              name={`file-icon/${record.type === 'folder' ? 'folder' : getExtension(value)}`}
              width={24}
            ></SvgIcon>
            {record.type === 'folder' ? (
              <Button
                type={'link'}
                // className={styles.linkButton}
                className="text-blue-500 hover:underline"
                onClick={() => navigateToOtherFolder(record.id)}
              >
                <Text ellipsis={{ tooltip: value }}>{value}</Text>
              </Button>
            ) : (
              <Text ellipsis={{ tooltip: value }}>{value}</Text>
            )}
          </Flex>
        );
      },
    },
    {
      title: t('uploadDate'),
      dataIndex: 'create_time',
      key: 'create_time',
      render(text) {
        return formatDate(text);
      },
    },
    {
      title: t('size'),
      dataIndex: 'size',
      key: 'size',
      render(value) {
        return (
          formatNumberWithThousandsSeparator((value / 1024).toFixed(2)) + ' KB'
        );
      },
    },
    {
      title: t('knowledgeBase'),
      dataIndex: 'kbs_info',
      key: 'kbs_info',
      render(value) {
        return Array.isArray(value) ? (
          <Space wrap>
            {value?.map((x) => (
              <Tag color="blue" key={x.kb_id}>
                {x.kb_name}
              </Tag>
            ))}
          </Space>  
        ) : (
          ''
        );
      },
    },
    {
      title: t('action'),
      dataIndex: 'action',
      key: 'action',
      render: (text, record) => (
        <ActionCell
          record={record}
          setCurrentRecord={(record: any) => {
            console.info(record);
          }}
          showRenameModal={showFileEditModal}
          showMoveFileModal={showMoveFileModal}
          showConnectToKnowledgeModal={showConnectToKnowledgeModal}
          setSelectedRowKeys={setSelectedRowKeys}
        ></ActionCell>
      ),
    },
  ];

  // 渲染卡片视图（主页）
  const renderCardView = () => {
    if (!data?.files) return null;

    return (
      <FileCardList
        files={data.files}
        onNavigate={navigateToOtherFolder}
        onRename={handleCardRename}
        onDelete={handleCardDelete}
      />
    );
  };

  // 新增：文件列表视图组件
  const FileListView = ({ files, onNavigate, onRename, onDelete, isBatchMode, selectedRowKeys, onSelectRow }: {
    files: IFile[];
    onNavigate: (folderId: string) => void;
    onRename: (file: IFile) => void;
    onDelete: (fileId: string, parentId: string, fileName: string) => void;
    isBatchMode?: boolean;
    selectedRowKeys?: Key[];
    onSelectRow?: (id: Key, checked: boolean) => void;
  }) => {
    const { downloadFile } = useDownloadFile();
    return (
      <div className="w-full" style={{ minHeight: 200 }}>
        {files.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-[500px] w-full" style={{ minHeight: 200 }}>
            <img src="/assets/files/iconempty.svg" alt="暂无文件" className="w-32 h-32 mb-4" />
            <div className="text-[16px] text-[#464646]">还没有内容</div>
          </div>
        ) : (
          <div className="flex flex-col gap-1">
            {files.map((file, idx) => {
              const ext = file.type === 'folder' ? '' : getExtension(file.name).toLowerCase();
              // 图标和颜色
              const iconMap: Record<string, { bg: string; color: string; label: React.ReactNode }> = {
                pdf: { bg: 'bg-[#ffe9d6]', color: 'text-[#ff7900]', label: 'P' },
                docx: { bg: 'bg-[#e1ebfd]', color: 'text-[#4285f4]', label: 'W' },
                xlsx: { bg: 'bg-[#def1e3]', color: 'text-[#34a853]', label: 'X' },
                txt: { bg: 'bg-[#e4dffa]', color: 'text-[#5938e4]', label: 'T' },
                png: { bg: 'bg-[#f5e6ff]', color: 'text-[#9540ff]', label: 'P' },
                jpg: { bg: 'bg-[#f5e6ff]', color: 'text-[#9540ff]', label: 'J' },
                jpeg: { bg: 'bg-[#f5e6ff]', color: 'text-[#9540ff]', label: 'J' },
                gif: { bg: 'bg-[#f5e6ff]', color: 'text-[#9540ff]', label: 'G' },
                bmp: { bg: 'bg-[#f5e6ff]', color: 'text-[#9540ff]', label: 'B' },
                webp: { bg: 'bg-[#f5e6ff]', color: 'text-[#9540ff]', label: 'W' },
                svg: { bg: 'bg-[#f5e6ff]', color: 'text-[#9540ff]', label: 'S' },
                folder: { bg: 'bg-[#ffffff]', color: 'text-[#4285F4]', label: <img src="/assets/files/iconfolder.svg" alt="文件夹" className="w-4.5 h-4.5" /> },
                default: { bg: 'bg-[#F5F5F5]', color: 'text-[#888]', label: <FileOutlined /> },
              };
              const iconInfo = file.type === 'folder' ? iconMap.folder : (iconMap[ext] || iconMap.default);
              const checked = !!selectedRowKeys?.includes(file.id);
              return (
                <div
                  key={file.id}
                  className="flex flex-row items-center px-3 py-2 gap-10 w-full bg-white border border-black/5 shadow-sm rounded-lg hover:shadow-md transition-all duration-150 group cursor-pointer"
                  style={{ height: 40 }}
                  onClick={() => { if (file.type === 'folder') onNavigate(file.id); }}
                >
                  
                  {/* 序号 */}
                  <div className="flex items-center justify-center w-8 text-[13px] text-black/40 font-normal select-none">
                    {idx + 1}
                  </div>
                  {/* 文件icon+名称+tag */}
                  <div className="flex flex-row items-center gap-2 flex-1 min-w-0">
                    {/* icon */}
                    <div className={`flex items-center justify-center w-5 h-5 rounded bg-opacity-20 ${iconInfo.bg}`}>
                      {typeof iconInfo.label === 'string' ? (
                        <span className={`font-bold text-[12px] leading-[18px] ${iconInfo.color}`}>{iconInfo.label}</span>
                      ) : (
                        iconInfo.label
                      )}
                    </div>
                    {/* 文件名 */}
                    <span className="truncate text-[14px] leading-[21px] text-black/80 font-normal max-w-[400px]">{file.name}</span>
                  </div>
                  {/* tag + 大小/文件数 */}
                  <div className="flex flex-row items-center min-w-0">
                    {/* tag */}
                    {file.type !== 'folder' && (
                      <span className="flex items-center justify-center px-2 py-0.5 bg-black/5 rounded text-[12px] leading-[18px] text-black/40 mr-2">
                        {ext}
                      </span>
                    )}
                    {/* 大小/文件数 */}
                    <div className="w-20 text-right text-[12px] leading-[18px] text-black/40 font-normal">
                      {file.type === 'folder'
                        ? `${file.file_count || 0} 文件`
                        : `${formatNumberWithThousandsSeparator((file.size / 1024).toFixed(2))} MB`}
                    </div>
                  </div>
                  {/* 时间 */}
                  <div className="w-32 text-right text-[12px] leading-[18px] text-black/40 font-normal">
                    {formatDate(file.create_time)}
                  </div>
                  {/* 操作按钮 */}
                  <div className="flex flex-row justify-end items-center gap-2 w-14 h-6 opacity-0 group-hover:opacity-100 transition">
                    {/* 重命名按钮 */}
                    <button
                      className="flex items-center justify-center w-6 h-6 rounded-md hover:bg-black/5 text-black/40 hover:text-black/80 transition-all duration-150"
                      title="重命名"
                      onClick={e => { e.stopPropagation(); onRename(file); }}
                    >
                      <svg width="16" height="16" fill="none" viewBox="0 0 16 16"><path d="M2.5 13.5h11M9.5 3.5l3 3-7 7h-3v-3l7-7z" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                    </button>
                    {/* 删除按钮 */}
                    <button
                      className="flex items-center justify-center w-6 h-6 rounded-md hover:bg-black/5 text-black/40 hover:text-red-500 transition-all duration-150"
                      title="删除"
                      onClick={e => { e.stopPropagation(); onDelete(file.id, file.parent_id, file.name); }}
                    >
                      <img src="/assets/files/icondelete.svg" alt="删除" className="w-4 h-4" />
                    </button>
                    {/* 下载按钮，仅文件可见 */}
                    {file.type !== 'folder' && (
                      <button
                        className="flex items-center justify-center w-6 h-6 rounded-md hover:bg-black/5 text-black/40 hover:text-blue-500 transition-all duration-150"
                        title="下载"
                        onClick={e => {
                          e.stopPropagation();
                          downloadFile({ id: file.id, filename: file.name });
                        }}
                      >
                        <img src="/assets/files/icondownload.svg" alt="下载" className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                  {/* 复选框 */}
                  {isBatchMode && (
                    <div className="flex items-center justify-center w-6 h-6 mr-2">
                      <input
                        type="checkbox"
                        checked={checked}
                        onChange={e => onSelectRow && onSelectRow(file.id, e.target.checked)} 
                        onClick={e => e.stopPropagation()}
                        className="accent-[#1d1d1d] w-4 h-4 rounded border border-black/20 focus:ring-0 focus:outline-none cursor-pointer"
                      />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  // 替换表格视图为 FileListView
  const renderTableView = () => {
    return (
      <FileListView
        files={data?.files || []}
        onNavigate={navigateToOtherFolder}
        onRename={handleCardRename}
        onDelete={handleCardDelete}
        isBatchMode={isBatchMode}
        selectedRowKeys={rowSelection.selectedRowKeys as Key[]}
        onSelectRow={(id: Key, checked: boolean) => {
          if (checked) {
            setSelectedRowKeys((prev: Key[]) => [...prev, id]);
          } else {
            setSelectedRowKeys((prev: Key[]) => prev.filter(key => key !== id));
          }
        }}
      />
    );
  };

  return (
  // <section className={styles.fileManagerWrapper}></section>
    <section className="w-full p-4 pt-0">
      <FileToolbar
        searchString={searchString}
        handleInputChange={handleInputChange}
        selectedRowKeys={rowSelection.selectedRowKeys as Key[]}
        showFolderCreateModal={showFolderCreateModal}
        showFileUploadModal={showFileUploadModal}
        setSelectedRowKeys={setSelectedRowKeys}
        showMoveFileModal={showMoveFileModal}
        total={data?.total || 0}
        isBatchMode={isBatchMode}
        onEnterBatchMode={handleEnterBatchMode}
        onExitBatchMode={handleExitBatchMode}
        dataSource={data?.files}
      ></FileToolbar>

      {/* 根据当前路径决定显示卡片还是表格 */}
      {isRootDirectory ? renderCardView() : renderTableView()}

      <RenameModal
        visible={fileEditVisible}
        hideModal={hideFileEditModal}
        onOk={onFileEditOk} 
        loading={fileEditLoading}
        initialName={initialName}
        initialDescription={initialDescription}
        initialTags={initialTags}
      ></RenameModal>
      <FolderCreateModal
        loading={folderCreateLoading}
        visible={folderCreateModalVisible}
        hideModal={hideFolderCreateModal}
        onOk={onFolderCreateOk}
      ></FolderCreateModal>
      <FileUploadModal
        visible={fileUploadVisible}
        hideModal={hideFileUploadModal}
        loading={fileUploadLoading}
        onOk={onFileUploadOk}
      ></FileUploadModal>
      <ConnectToKnowledgeModal
        initialValue={initialValue}
        visible={connectToKnowledgeVisible}
        hideModal={hideConnectToKnowledgeModal}
        onOk={onConnectToKnowledgeOk}
        loading={connectToKnowledgeLoading}
      ></ConnectToKnowledgeModal>
      {moveFileVisible && (
        <FileMovingModal
          visible={moveFileVisible}
          hideModal={hideMoveFileModal}
          onOk={onMoveFileOk}
          loading={moveFileLoading}
        ></FileMovingModal>
      )}
    </section>
  );
};

// 使用 QueryClientProvider 包装组件以提供 React Query 上下文
const FileManagerWithQueryProvider = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <FileManager />
    </QueryClientProvider>
  );
};

export default FileManagerWithQueryProvider;
