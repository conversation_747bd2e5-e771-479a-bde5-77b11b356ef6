import { Form } from 'antd';
import EditTag from './edit-tag';

const initialEntityTypes = [
  'organization',
  'person',
  'geo',
  'event',
  'category',
];

type IProps = {
  field?: string[];
};

const EntityTypesItem = ({
  field = ['parser_config', 'entity_types'],
}: IProps) => {
  return (
    <Form.Item
      name={field}
      label={"实体类型"}
      rules={[{ required: true }]}
      initialValue={initialEntityTypes}
    >
      <EditTag></EditTag>
    </Form.Item>
  );
};

export default EntityTypesItem;
