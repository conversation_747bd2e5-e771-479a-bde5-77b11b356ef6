import {
  useHandleSearchChange,
  useGetPaginationWithRouter,
} from "./logic-hooks";
import { IDocumentInfo } from "@/interfaces/document";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import kbService, { listDocument } from "@/services/knowledge-service";
import { useCallback } from "react";
import { UploadFile, App } from "antd";
import { get } from "lodash";
import {
  useSetPaginationParams,
  useGetKnowledgeSearchParams,
} from "./route-hook";
import {
  IChangeParserConfigRequestBody,
  IDocumentMetaRequestBody,
} from "@/interfaces/request/document";

export const useFetchNextDocumentList = () => {
  const { searchString, handleInputChange } = useHandleSearchChange();
  const { pagination, setPagination } = useGetPaginationWithRouter();
  const { knowledgeId: id } = useGetKnowledgeSearchParams();
  const { data, isFetching: loading } = useQuery<{
    docs: IDocumentInfo[];
    total: number;
  }>({
    queryKey: ["fetchDocumentList", searchString, pagination],
    refetchInterval: 15000,
    enabled: !!id,
    queryFn: async () => {
      const ret = await listDocument({
        kb_id: id,
        keywords: searchString,
        page_size: pagination.pageSize,
        page: pagination.current,
      });
      if (ret.data.code === 0) {
        return ret.data.data;
      }
      return {
        docs: [],
        total: 0,
      };
    },
  });

  const onInputChange: React.ChangeEventHandler<HTMLInputElement> = useCallback(
    (e) => {
      setPagination({ page: 1 });
      handleInputChange(e);
    },
    [handleInputChange, setPagination]
  );

  return {
    loading,
    searchString,
    documents: data?.docs || [],
    pagination: { ...pagination, total: data?.total || 0 },
    handleInputChange: onInputChange,
    setPagination,
  };
};

export const useNextWebCrawl = () => {
  const { knowledgeId } = useGetKnowledgeSearchParams();
  const { message } = App.useApp();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ["webCrawl"],
    mutationFn: async ({ name, url }: { name: string; url: string }) => {
      const formData = new FormData();
      formData.append("name", name);
      formData.append("url", url);
      formData.append("kb_id", knowledgeId);

      const ret = await kbService.web_crawl(formData);
      const code = get(ret, "data.code");
      if (code === 0) {
        message.success("上传成功");
      }

      return code;
    },
  });

  return {
    data,
    loading,
    webCrawl: mutateAsync,
  };
};

export const useCreateNextDocument = () => {
  const { knowledgeId } = useGetKnowledgeSearchParams();
  const { setPaginationParams, page } = useSetPaginationParams();
  const queryClient = useQueryClient();
  const { message } = App.useApp();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ["createDocument"],
    mutationFn: async (name: string) => {
      const { data } = await kbService.document_create({
        name,
        kb_id: knowledgeId,
      });
      if (data.code === 0) {
        if (page === 1) {
          queryClient.invalidateQueries({ queryKey: ["fetchDocumentList"] });
        } else {
          setPaginationParams(); // fetch document list
        }

        message.success("创建于");
      }
      return data.code;
    },
  });

  return { createDocument: mutateAsync, loading, data };
};

export const useUploadNextDocument = () => {
  const queryClient = useQueryClient();
  const { knowledgeId } = useGetKnowledgeSearchParams();
  const { message } = App.useApp();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ["uploadDocument"],
    mutationFn: async (fileList: UploadFile[]) => {
      const formData = new FormData();
      formData.append("kb_id", knowledgeId);
      fileList.forEach((file: any) => {
        formData.append("file", file);
      });

      try {
        const ret = await kbService.document_upload(formData);
        const code = get(ret, "data.code");

        if (code === 0 || code === 500) {
          queryClient.invalidateQueries({ queryKey: ["fetchDocumentList"] });
        }
        return ret?.data;
      } catch (error) {
        console.warn(error);
        return {
          code: 500,
          message: error + "",
        };
      }
    },
  });

  return { uploadDocument: mutateAsync, loading, data };
};

export const useRunNextDocument = () => {
  const queryClient = useQueryClient();
  const { message } = App.useApp();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ["runDocumentByIds"],
    mutationFn: async ({
      documentIds,
      run,
      shouldDelete,
    }: {
      documentIds: string[];
      run: number;
      shouldDelete: boolean;
    }) => {
      queryClient.invalidateQueries({
        queryKey: ["fetchDocumentList"],
      });

      const ret = await kbService.document_run({
        doc_ids: documentIds,
        run,
        delete: shouldDelete,
      });
      const code = get(ret, "data.code");
      if (code === 0) {
        queryClient.invalidateQueries({ queryKey: ["fetchDocumentList"] });
        message.success("操作成功");
      }

      return code;
    },
  });

  return { runDocumentByIds: mutateAsync, loading, data };
};

export const useSetNextDocumentStatus = () => {
  const queryClient = useQueryClient();
  const { message } = App.useApp();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ["updateDocumentStatus"],
    mutationFn: async ({
      status,
      documentId,
    }: {
      status: boolean;
      documentId: string;
    }) => {
      const { data } = await kbService.document_change_status({
        doc_id: documentId,
        status: Number(status),
      });
      if (data.code === 0) {
        message.success("更新成功");
        queryClient.invalidateQueries({ queryKey: ["fetchDocumentList"] });
      }
      return data;
    },
  });

  return { setDocumentStatus: mutateAsync, data, loading };
};

export const useRemoveNextDocument = () => {
  const { message } = App.useApp();
  const queryClient = useQueryClient();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ["removeDocument"],
    mutationFn: async (documentIds: string | string[]) => {
      const { data } = await kbService.document_rm({ doc_id: documentIds });
      if (data.code === 0) {
        message.success("删除成功");
        queryClient.invalidateQueries({ queryKey: ["fetchDocumentList"] });
      }
      return data.code;
    },
  });

  return { data, loading, removeDocument: mutateAsync };
};

export const useSaveNextDocumentName = () => {
  const queryClient = useQueryClient();
  const { message } = App.useApp();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ["saveDocumentName"],
    mutationFn: async ({
      name,
      documentId,
    }: {
      name: string;
      documentId: string;
    }) => {
      const { data } = await kbService.document_rename({
        doc_id: documentId,
        name: name,
      });
      if (data.code === 0) {
        message.success("重命名成功");
        queryClient.invalidateQueries({ queryKey: ["fetchDocumentList"] });
      }
      return data.code;
    },
  });

  return { loading, saveName: mutateAsync, data };
};

export const useSetNextDocumentParser = () => {
  const queryClient = useQueryClient();
  const { message } = App.useApp();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ["setDocumentParser"],
    mutationFn: async ({
      parserId,
      documentId,
      parserConfig,
    }: {
      parserId: string;
      documentId: string;
      parserConfig: IChangeParserConfigRequestBody;
    }) => {
      const { data } = await kbService.document_change_parser({
        parser_id: parserId,
        doc_id: documentId,
        parser_config: parserConfig,
      });
      if (data.code === 0) {
        queryClient.invalidateQueries({ queryKey: ["fetchDocumentList"] });

        message.success("更新成功");
      }
      return data.code;
    },
  });

  return { setDocumentParser: mutateAsync, data, loading };
};

export const useSetDocumentMeta = () => {
  const queryClient = useQueryClient();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ["setDocumentMeta"],
    mutationFn: async (params: IDocumentMetaRequestBody) => {
      const { message } = App.useApp();
      try {
        const { data } = await kbService.setMeta({
          meta: params.meta,
          doc_id: params.documentId,
        });

        if (data?.code === 0) {
          queryClient.invalidateQueries({ queryKey: ["fetchDocumentList"] });

          message.success("更新成功");
        }
        return data?.code;
      } catch (error) {
        message.error("error");
      }
    },
  });

  return { setDocumentMeta: mutateAsync, data, loading };
};
