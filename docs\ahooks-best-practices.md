# ahooks 最佳实践指南

## 简介

ahooks 是一个高质量且可靠的 React Hooks 库，由阿里巴巴开源，提供了丰富的 Hook 集合来简化 React 开发。本指南将帮助您在 AgentFoundry-Ziko 项目中正确使用 ahooks。

## 核心特性

### 1. 完整的 SSR 支持
- 完全解决了 SSR 场景下的 "DOM/BOM 缺失" 和 "useLayoutEffect 警告" 问题
- 可在 Next.js 项目中安全使用

### 2. 稳定的函数引用
- 所有 ahooks 输出的函数引用都是稳定的
- 类似于 React 的 setState，引用不会发生变化

### 3. TypeScript 支持
- 使用 TypeScript 编写，提供完整的类型定义
- 确保类型安全和更好的开发体验

## 安装与配置

### 安装
```bash
npm install ahooks
```

### 基本导入
```typescript
import { useRequest, useLocalStorageState, useDebounce } from 'ahooks';
```

## 常用 Hooks 使用指南

### 1. useRequest - 网络请求

最常用的 Hook，用于处理异步请求：

```typescript
import { useRequest } from 'ahooks';

// 基本用法
function UserProfile() {
  const { data, loading, error } = useRequest('/api/user');
  
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return <div>Hello, {data?.name}!</div>;
}

// 手动触发请求
function CreateUser() {
  const { run: createUser, loading } = useRequest(
    (userData) => fetch('/api/users', {
      method: 'POST',
      body: JSON.stringify(userData)
    }),
    {
      manual: true,
      onSuccess: (result) => {
        console.log('用户创建成功:', result);
      }
    }
  );
  
  return (
    <button onClick={() => createUser({ name: 'John' })} disabled={loading}>
      {loading ? '创建中...' : '创建用户'}
    </button>
  );
}
```

### 2. useLocalStorageState - 本地存储状态

同步组件状态与 localStorage：

```typescript
import { useLocalStorageState } from 'ahooks';

function Settings() {
  const [theme, setTheme] = useLocalStorageState('theme', {
    defaultValue: 'light'
  });
  
  return (
    <div>
      <p>当前主题: {theme}</p>
      <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>
        切换主题
      </button>
    </div>
  );
}
```

### 3. useDebounce - 防抖

用于搜索输入等需要防抖的场景：

```typescript
import { useDebounce } from 'ahooks';
import { useState } from 'react';

function SearchInput() {
  const [keyword, setKeyword] = useState('');
  const debouncedKeyword = useDebounce(keyword, { wait: 500 });
  
  // 只有在停止输入 500ms 后才会触发搜索
  const { data } = useRequest(
    () => fetch(`/api/search?q=${debouncedKeyword}`),
    {
      ready: !!debouncedKeyword,
      refreshDeps: [debouncedKeyword]
    }
  );
  
  return (
    <div>
      <input
        value={keyword}
        onChange={(e) => setKeyword(e.target.value)}
        placeholder="搜索..."
      />
      {data && <SearchResults results={data} />}
    </div>
  );
}
```

### 4. useThrottle - 节流

用于限制函数执行频率：

```typescript
import { useThrottle } from 'ahooks';
import { useState } from 'react';

function ScrollHandler() {
  const [scrollY, setScrollY] = useState(0);
  const throttledScrollY = useThrottle(scrollY, { wait: 100 });
  
  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  return <div>节流后的滚动位置: {throttledScrollY}px</div>;
}
```

### 5. useBoolean - 布尔值状态管理

简化布尔值状态的管理：

```typescript
import { useBoolean } from 'ahooks';

function Modal() {
  const [isOpen, { setTrue: open, setFalse: close, toggle }] = useBoolean(false);
  
  return (
    <div>
      <button onClick={open}>打开弹窗</button>
      <button onClick={toggle}>切换状态</button>
      {isOpen && (
        <div className="modal">
          <p>这是一个弹窗</p>
          <button onClick={close}>关闭</button>
        </div>
      )}
    </div>
  );
}
```

### 6. useToggle - 切换状态

在多个值之间切换：

```typescript
import { useToggle } from 'ahooks';

function StatusToggle() {
  const [state, { toggle, set }] = useToggle('pending', 'success', 'error');
  
  return (
    <div>
      <p>当前状态: {state}</p>
      <button onClick={toggle}>切换状态</button>
      <button onClick={() => set('pending')}>设置为待处理</button>
      <button onClick={() => set('success')}>设置为成功</button>
      <button onClick={() => set('error')}>设置为错误</button>
    </div>
  );
}
```

## 最佳实践原则

### 1. 遵循 React Hooks 规则
- 只在最顶层使用 Hook，不要在循环、条件或嵌套函数中调用
- 只在 React 函数组件中调用 Hook
- 启用 `eslint-plugin-react-hooks` 插件

### 2. 合理使用 useRequest
```typescript
// ✅ 好的实践
const { data, loading, error } = useRequest(fetchUserData, {
  onSuccess: (data) => {
    // 处理成功逻辑
  },
  onError: (error) => {
    // 处理错误逻辑
  }
});

// ❌ 避免的实践
const { data } = useRequest(fetchUserData);
// 没有错误处理，可能导致用户体验不佳
```

### 3. 性能优化建议
```typescript
// 使用 ready 参数避免不必要的请求
const { data } = useRequest(fetchUserData, {
  ready: !!userId, // 只有当 userId 存在时才发起请求
  refreshDeps: [userId]
});

// 使用 debounce 优化搜索
const debouncedSearchTerm = useDebounce(searchTerm, { wait: 300 });
```

### 4. 组件通信模式
```typescript
// 简单的父子组件通信：使用 props
function Parent() {
  const [count, setCount] = useState(0);
  return <Child count={count} onIncrement={() => setCount(c => c + 1)} />;
}

// 复杂的跨层级通信：使用 Context
const ThemeContext = createContext();

function App() {
  const [theme, setTheme] = useLocalStorageState('theme', {
    defaultValue: 'light'
  });
  
  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      <Components />
    </ThemeContext.Provider>
  );
}
```

### 5. 类型安全
```typescript
// 为 useRequest 提供类型定义
interface User {
  id: string;
  name: string;
  email: string;
}

const { data, loading } = useRequest<User>(() => 
  fetch('/api/user').then(res => res.json())
);

// data 现在有完整的类型提示
```

## 错误处理

### 1. useRequest 错误处理
```typescript
const { data, loading, error } = useRequest(fetchData, {
  onError: (error) => {
    console.error('请求失败:', error);
    // 显示错误提示
    toast.error('数据加载失败，请重试');
  }
});
```

### 2. 全局错误处理
```typescript
// 在 App 组件中设置全局错误处理
function App() {
  useRequest.config({
    onError: (error) => {
      // 全局错误处理逻辑
      console.error('全局错误:', error);
    }
  });
  
  return <YourApp />;
}
```

## 常见问题解答

### Q: 在 Next.js 中使用 ahooks 需要注意什么？
A: ahooks 3.0 完全支持 SSR，可以在 Next.js 中安全使用。确保在客户端组件中使用需要 DOM 的 hooks。

### Q: 如何在 TypeScript 中使用 ahooks？
A: ahooks 原生支持 TypeScript，大多数 hooks 都有完整的类型定义。对于 useRequest，可以通过泛型指定返回数据类型。

### Q: useRequest 的 refreshDeps 是什么？
A: refreshDeps 是依赖数组，当数组中的值发生变化时，会重新发起请求。类似于 useEffect 的依赖数组。

### Q: 如何处理并发请求？
A: useRequest 自动处理并发请求，后发起的请求会取消前面的请求。也可以手动使用 cancel 方法取消请求。

## 进阶用法

### 自定义 Hook 结合 ahooks
```typescript
// 创建自定义 Hook
function useUserProfile(userId: string) {
  const { data, loading, error } = useRequest(
    () => fetch(`/api/users/${userId}`).then(res => res.json()),
    {
      ready: !!userId,
      refreshDeps: [userId]
    }
  );
  
  const [isEditing, { setTrue: startEdit, setFalse: cancelEdit }] = useBoolean(false);
  
  return {
    user: data,
    loading,
    error,
    isEditing,
    startEdit,
    cancelEdit
  };
}
```

### 结合状态管理
```typescript
// 结合 Context 使用
const UserContext = createContext();

function UserProvider({ children }) {
  const [currentUser, setCurrentUser] = useLocalStorageState('currentUser');
  
  const { data, loading } = useRequest(
    () => fetch('/api/me').then(res => res.json()),
    {
      onSuccess: setCurrentUser
    }
  );
  
  return (
    <UserContext.Provider value={{ currentUser, loading }}>
      {children}
    </UserContext.Provider>
  );
}
```

## 总结

ahooks 是一个功能强大且易于使用的 React Hooks 库。在 AgentFoundry-Ziko 项目中：

1. 优先使用 ahooks 提供的 hooks 来简化开发
2. 遵循 React Hooks 的最佳实践
3. 合理使用类型定义提升代码质量
4. 注意性能优化，避免不必要的重新渲染
5. 建立良好的错误处理机制

通过遵循这些实践，您可以构建高质量、可维护的 React 应用。