import React from "react";

const tagColorMap: { [key: string]: string } = {
  公开: "bg-[#4285F429] text-[#4285F4]",
  个人: "bg-[#34A85329] text-[#34A853]",
  组: "bg-[#FF790029] text-[#FF7900]",
};

interface KnowledgeListRowProps {
  index: number;
  kb: {
    id?: string;
    name: string;
    avatar?: string;
    labels?: string[];
    // 其他字段可按需扩展
  };
  onRemove?: () => void; // 新增
}

export default function KnowledgeListRow({
  index,
  kb,
  onRemove,
}: KnowledgeListRowProps) {
  return (
    <div className="flex items-center bg-white rounded-[8px] border border-[#0000000F] px-3 py-2 ">
      <span className="text-[#00000066] w-8 text-start mr-10 font-medium text-[13px] font-normal">
        {index + 1}
      </span>
      <div className="flex items-center gap-2">
        <img src="/kb.svg" alt="icon" />
        <span className="text-[16px] font-medium text-[#222] mr-6 min-w-[80px]">
          {kb.name}
        </span>
      </div>
      <div className="flex flex-wrap gap-[6px] flex-1 justify-end">
        {kb.labels?.map((label) => (
          <span
            key={label}
            className={`px-[6px] py-[2px] text-[12px] font-nromal rounded-[4px] border border-[#0000000F] ${
              tagColorMap[label]
                ? `border border-transparent ${tagColorMap[label]}`
                : "bg-[#FFFFFF] text-[#000000B8]"
            }`}
          >
            {label}
          </span>
        ))}
        {/* 清除按钮 */}
        {onRemove && (
          <button
            className="flex items-center justify-center ml-10 cursor-pointer text-[#00000066]"
            onClick={onRemove}
            tabIndex={-1}
          >
            x
          </button>
        )}
      </div>
    </div>
  );
}
