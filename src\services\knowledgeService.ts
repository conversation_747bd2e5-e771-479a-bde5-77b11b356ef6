/**
 * 知识库API服务
 * 纯函数式API调用，配合ahooks的useRequest使用
 * 集成项目的错误处理机制
 */

import { apiClient } from '@/lib/http/errorInterceptor';
import { knowledgeEndpoints } from '@/services/apiEndpoints';
import {
  KnowledgeListParams,
  KnowledgeListResponse,
  KnowledgeCreateParams, 
  KnowledgeCreateResponse,
  KnowledgeUpdateParams,
  KnowledgeUpdateResponse,
  KnowledgeDeleteResponse,
  KnowledgeStatsResponse,
  RetrievalParams,
  RetrievalResponse,
  PermissionSetParams,
  PermissionSetResponse
} from '@/types/knowledge-api';
import {
  IKnowledge,
  KnowledgeStats,
  RetrievalResult
} from '@/types/knowledge';
import { RunningStatus } from '@/types/knowledge-enums';

// ==================== 知识库基础CRUD ====================

/**
 * 获取知识库列表
 */
export async function getKnowledgeList(params: KnowledgeListParams = {}): Promise<KnowledgeListResponse> {
  const response = await apiClient.post<KnowledgeListResponse>(knowledgeEndpoints.list, {
  }, {
    params: {
      page: params.page || 1,
      page_size: params.pageSize || 10,
      keywords: params.keywords,
      orderby: params.sortBy || 'create_time',
      desc: params.sortOrder,
      role: 'user',
      tags: params.labels
    }
  });
  
  return response.data;
}

/**
 * 创建知识库
 */
export async function createKnowledge(params: KnowledgeCreateParams): Promise<KnowledgeCreateResponse> {
  const response = await apiClient.post<KnowledgeCreateResponse>(
    knowledgeEndpoints.create,
    {
      name: params.name,
      description: params.description,
      permission: params.permission || 'public',
      chunk_method: 'naive',
      language: 'zh',
      embedding_model: 'default',
      tags: []
    }
  );
  
  return response.data;
}

/**
 * 获取知识库详情
 */
export async function getKnowledgeDetail(knowledgeId: string): Promise<{ data: IKnowledge }> {
  const response = await apiClient.get<{ data: IKnowledge }>(
    knowledgeEndpoints.detail,
    { params: { kb_id: knowledgeId } }
  );
  
  return response.data;
}

/**
 * 更新知识库
 */
export async function updateKnowledge(
  knowledgeId: string, 
  params: KnowledgeUpdateParams
): Promise<KnowledgeUpdateResponse> {
  const response = await apiClient.post<KnowledgeUpdateResponse>(
    knowledgeEndpoints.update,
    {
      kb_id: knowledgeId,
      parser_id: 'native',
      name: params.name,
      description: params.description,
      permission: params.permission,
      tags: params.tags || null
    }
  );
  
  return response.data;
}

/**
 * 删除知识库
 */
export async function deleteKnowledge(knowledgeId: string): Promise<KnowledgeDeleteResponse> {
  const response = await apiClient.post<KnowledgeDeleteResponse>(
    knowledgeEndpoints.delete,
    { kb_id: knowledgeId }
  );
  
  return response.data;
}

// ==================== 知识库统计和信息 ====================

/**
 * 获取知识库统计信息
 */
export async function getKnowledgeStats(knowledgeId: string): Promise<KnowledgeStatsResponse> {
  const response = await apiClient.get<KnowledgeStatsResponse>(
    knowledgeEndpoints.stats.replace(':id', knowledgeId)
  );
  
  return response.data;
}

// ==================== 知识库检索功能 ====================

/**
 * 执行知识检索
 */
export async function performRetrieval(params: RetrievalParams): Promise<RetrievalResponse> {
  const response = await apiClient.post<RetrievalResponse>(
    knowledgeEndpoints.retrieval,
    {
      kb_id: params.kb_id,
      query: params.query,
      similarity_threshold: params.similarity_threshold,
      vector_similarity_weight: params.vector_similarity_weight,
      top_k: params.top_k,
      doc_ids: params.doc_ids,
      chunk_ids: params.chunk_ids
    }
  );
  
  return response.data;
}

/**
 * 测试检索功能（不记录历史）
 */
export async function testRetrieval(params: RetrievalParams): Promise<RetrievalResponse> {
  const response = await apiClient.post<RetrievalResponse>(
    knowledgeEndpoints.testRetrieval,
    {
      kb_id: params.kb_id,
      query: params.query,
      similarity_threshold: params.similarity_threshold,
      vector_similarity_weight: params.vector_similarity_weight,
      top_k: params.top_k,
      doc_ids: params.doc_ids,
      chunk_ids: params.chunk_ids
    }
  );
  
  return response.data;
}

// ==================== 权限管理 ====================

/**
 * 设置知识库权限
 */
export async function setKnowledgePermissions(
  knowledgeId: string,
  params: PermissionSetParams
): Promise<PermissionSetResponse> {
  const response = await apiClient.post<PermissionSetResponse>(
    knowledgeEndpoints.permissions.replace(':id', knowledgeId),
    {
      permission: params.permission,
      user_ids: params.user_ids,
      group_ids: params.group_ids
    }
  );
  
  return response.data;
}

// ==================== Mock数据（开发阶段） ====================

/**
 * 生成Mock数据用于开发测试
 */
export function createMockKnowledge(override: Partial<IKnowledge> = {}): IKnowledge {
  return {
    id: `kb_${Date.now()}`,
    name: 'Mock知识库',
    description: '这是一个用于开发测试的模拟知识库',
    avatar: undefined,
    chunk_num: 150,
    doc_num: 12,
    token_num: 25000,
    size: 1048576,
    permission: 'public',
    status: 'active',
    parser_config: {
      chunk_token_num: 512,
      delimiter: '\\n',
      auto_keywords: 1,
      auto_questions: 1
    },
    parser_id: 'default',
    embd_id: 'default',
    similarity_threshold: 0.8,
    vector_similarity_weight: 0.3,
    operator_permission: 1,
    create_date: new Date().toISOString(),
    create_time: Date.now().toString(),
    update_date: new Date().toISOString(),
    update_time: Date.now().toString(),
    created_by: 'current-user',
    tenant_id: 'default-tenant',
    nickname: 'Test User',
    ...override
  };
}

/**
 * 生成Mock检索结果
 */
export function createMockRetrievalResults(count: number = 5): RetrievalResult[] {
  return Array.from({ length: count }, (_, index) => ({
    chunk_id: `chunk_${index + 1}`,
    doc_id: `doc_${Math.floor(index / 2) + 1}`,
    doc_name: `文档${Math.floor(index / 2) + 1}.pdf`,
    content: `这是第${index + 1}个检索结果的内容，包含了相关的知识信息...`,
    similarity: 0.95 - index * 0.1,
    keywords: [`关键词${index + 1}`, `关键词${index + 2}`],
    highlights: [{
      start: 10,
      end: 20,
      text: '重要信息'
    }]
  }));
}

/**
 * 生成Mock统计数据
 */
export function createMockStats(): KnowledgeStats {
  return {
    total_documents: 15,
    total_chunks: 245,
    total_tokens: 125800,
    total_size: 20485760,
    document_types: {
      pdf: 8,
      docx: 5,
      txt: 2,
      doc: 0,
      md: 0,
      html: 0,
      json: 0
    },
    processing_status: {
      [RunningStatus.UNSTART]: 0,
      [RunningStatus.RUNNING]: 1,
      [RunningStatus.CANCEL]: 0,
      [RunningStatus.DONE]: 13,
      [RunningStatus.FAIL]: 1
    },
    recent_activity: []
  };
}