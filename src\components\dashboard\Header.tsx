"use client";

import React from "react";
import { usePathname } from "next/navigation";

interface HeaderProps {
  onToggle: () => void;
}

// 面包屑配置
const breadcrumbConfig: Record<string, string> = {
  "/dashboard": "控制面板",
  "/chat": "聊天",
  "/files": "文件",
  "/knowledge": "知识库",
  "/ai-staff": "AI员工",
  "/history": "历史会话",
  "/ai-staff/create": "创建员工",
};

// 动态路由面包屑名称获取
function getBreadcrumbName(path: string) {
  if (path.startsWith("/ai-staff/edit/")) return "编辑员工";
  if (path.startsWith("/ai-staff/preview/")) return "预览员工";
  return breadcrumbConfig[path] || "未知页面";
}

export default function Header({ onToggle }: HeaderProps) {
  const pathname = usePathname();
  const currentPageTitle = getBreadcrumbName(pathname);

  return (
    <header className="flex items-center justify-between h-[56px] px-6 bg-[#FAFAFA] border-b border-border-light">
      {/* 左侧：移动端菜单按钮 + 面包屑 */}
      <div className="flex items-center space-x-2">
        {/* 菜单切换按钮 */}
        <button
          onClick={onToggle}
          className="hover:bg-layer-2 rounded-lg transition-colors cursor-pointer"
        >
          <img src="/sidebar-control.svg" alt="" />
        </button>

        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2">
          <span className="text-text-muted text-sm">首页</span>
          <span className="text-text-muted text-sm">/</span>
          <span className="text-text-primary text-sm font-medium">
            {currentPageTitle}
          </span>
        </nav>
      </div>
    </header>
  );
}
