@layer theme, base, antd, components, utilities;
@import "tailwindcss";

:root {
  /* =================
   * 基于设计稿的 Light 主题颜色系统
   * ================= */
  
  /* 基础文本和背景 */
  --text-primary: #000000;
  --text-secondary: #666666;
  --text-muted: #999999;
  --bg-base: #ffffff;
  
  /* 设计稿中的背景色层次 (基于黑色透明度) */
  --bg-layer-1: rgba(0, 0, 0, 0.01);  /* #000000 1% */
  --bg-layer-2: rgba(0, 0, 0, 0.02);  /* #000000 2% */
  --bg-layer-3: rgba(0, 0, 0, 0.04);  /* #000000 4% */
  --bg-layer-4: rgba(0, 0, 0, 0.08);  /* #000000 8% */
  
  /* 设计稿中的基础色板 (100% 饱和度) */
  --color-purple: #5938E4;     /* 紫色 */
  --color-blue: #4285F4;       /* 蓝色 */
  --color-cyan: #57CBFF;       /* 青色 */
  --color-green: #34A853;      /* 绿色 */
  --color-yellow: #FBBC04;     /* 黄色 */
  --color-orange: #FF7900;     /* 橙色 */
  --color-red: #E74639;        /* 红色 */
  --color-pink: #FF7DB0;       /* 粉色 */
  
  /* 设计稿中的浅色变体 (88% 透明度) */
  --color-purple-light: rgba(89, 56, 228, 0.88);
  --color-blue-light: rgba(66, 133, 244, 0.88);
  --color-cyan-light: rgba(87, 203, 255, 0.88);
  --color-green-light: rgba(52, 168, 83, 0.88);
  --color-yellow-light: rgba(251, 188, 4, 0.88);
  --color-orange-light: rgba(255, 121, 0, 0.88);
  --color-red-light: rgba(231, 70, 57, 0.88);
  --color-pink-light: rgba(255, 125, 176, 0.88);
  
  /* 语义化功能色 (基于设计稿色板) */
  --color-brand: var(--color-purple);
  --color-primary: var(--color-blue);
  --color-success: var(--color-green);
  --color-warning: var(--color-yellow);
  --color-error: var(--color-red);
  --color-info: var(--color-cyan);
  
  /* 边框颜色 (基于背景层次) */
  --border-light: var(--bg-layer-2);
  --border-medium: var(--bg-layer-3);

  /* 页面切换动画配置 */
  --page-transition-duration: 300ms;
  --page-transition-easing: cubic-bezier(0.4, 0, 0.2, 1);
  --border-strong: var(--bg-layer-4);
}

@theme inline {
  /* 字体配置 */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  
  /* 基础颜色 */
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-muted: var(--text-muted);
  --color-bg-base: var(--bg-base);
  
  /* 背景色层次 - 可用作 bg-layer-1, bg-layer-2 等 */
  --color-layer-1: var(--bg-layer-1);
  --color-layer-2: var(--bg-layer-2);
  --color-layer-3: var(--bg-layer-3);
  --color-layer-4: var(--bg-layer-4);
  
  /* 设计稿色板 - 可用作 bg-purple, text-blue 等 */
  --color-purple: var(--color-purple);
  --color-blue: var(--color-blue);
  --color-cyan: var(--color-cyan);
  --color-green: var(--color-green);
  --color-yellow: var(--color-yellow);
  --color-orange: var(--color-orange);
  --color-red: var(--color-red);
  --color-pink: var(--color-pink);
  
  /* 浅色变体 - 可用作 bg-purple-light, text-blue-light 等 */
  --color-purple-light: var(--color-purple-light);
  --color-blue-light: var(--color-blue-light);
  --color-cyan-light: var(--color-cyan-light);
  --color-green-light: var(--color-green-light);
  --color-yellow-light: var(--color-yellow-light);
  --color-orange-light: var(--color-orange-light);
  --color-red-light: var(--color-red-light);
  --color-pink-light: var(--color-pink-light);
  
  /* 语义化功能色 - 可用作 bg-brand, text-primary, border-success 等 */
  --color-brand: var(--color-brand);
  --color-primary: var(--color-primary);
  --color-success: var(--color-success);
  --color-warning: var(--color-warning);
  --color-error: var(--color-error);
  --color-info: var(--color-info);
  
  /* 边框颜色 - 可用作 border-light, border-medium 等 */
  --color-border-light: var(--border-light);
  --color-border-medium: var(--border-medium);
  --color-border-strong: var(--border-strong);
}

body {
  background: var(--bg-base);
  color: var(--text-primary);
  font-family: Arial, Helvetica, sans-serif;
}

文件管理器自定义样式
.breadcrumb-item-button {
  @apply cursor-pointer text-blue-500 p-0 h-auto;
}

.filter {
  @apply flex my-2.5 justify-between items-center pb-2 mt-0;
}

.file-toolbar-delete-button {
  @apply h-10 text-sm bg-white border border-red-200 text-red-500 px-6 rounded-lg shadow-none transition-all duration-200 ease-in-out;
}

.file-toolbar-delete-button:hover:not(:disabled) {
  @apply bg-red-50 border-red-200 text-red-500;
}

.file-toolbar-delete-button:disabled {
  @apply bg-red-50 border-red-100 text-red-500 cursor-not-allowed;
}

.file-toolbar-cancel-button {
  @apply h-10 text-sm bg-white border border-gray-200 text-gray-600 px-6 rounded-lg shadow-none transition-all duration-200 ease-in-out;
}

.file-toolbar-cancel-button:hover {
  @apply bg-gray-50 border-gray-200 text-gray-600;
}

.file-toolbar-back-button {
  @apply h-10 text-sm bg-white border border-gray-200 text-gray-700 px-4 rounded-lg shadow-none transition-all duration-200 ease-in-out;
}

.file-toolbar-back-button:hover {
  @apply bg-gray-50 border-gray-200 text-gray-700;
}

.root-create-folder-button,
.file-toolbar-upload-button {
  @apply h-10 text-sm bg-blue-600 text-white px-6 rounded-lg shadow-none transition-all duration-200 ease-in-out;
}

.root-create-folder-button:hover,
.file-toolbar-upload-button:hover {
  @apply bg-blue-700 border-blue-600;
}

.file-toolbar-create-folder-button {
  @apply h-10 text-sm bg-white border border-gray-200 text-gray-700 px-6 rounded-lg shadow-none transition-all duration-200 ease-in-out;
}

.file-toolbar-create-folder-button:hover {
  @apply bg-gray-50 border-gray-200 text-gray-700;
}

.file-toolbar-batch-button {
  @apply h-10 text-sm bg-white border border-gray-200 text-gray-700 px-6 rounded-lg shadow-none transition-all duration-200 ease-in-out;
}

.file-toolbar-batch-button:hover {
  @apply bg-gray-50 border-gray-200 text-gray-700;
}

.file-toolbar-search-input {
  @apply w-60 h-10 rounded-lg border border-gray-200 px-4 text-sm text-gray-700 bg-white transition-all duration-200 ease-in-out;
}

.file-toolbar-search-input::placeholder {
  @apply text-gray-400;
}

/* 文件卡片样式 */
.file-card {
  @apply h-60 rounded-lg border border-gray-100 transition-all duration-300 ease-in-out cursor-pointer relative overflow-hidden bg-white p-5;
}

.file-card:hover {
  @apply shadow-lg;
}

.file-card:hover .card-footer .default-info {
  @apply opacity-0;
}

.file-card:hover .card-footer .hover-info {
  @apply opacity-100 pointer-events-auto;
}

.file-card:hover .card-action-button {
  @apply opacity-100;
}

.card-content {
  @apply flex flex-col h-full justify-between;
}

.card-header {
  @apply flex items-center gap-3 mb-2;
}

.icon-wrapper {
  @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

.card-title {
  @apply text-base font-semibold text-gray-700 flex-1 m-0;
}

.card-body {
  @apply flex-1 flex flex-col gap-3;
}

.card-description .description-text {
  @apply text-sm text-gray-500 m-0 leading-5;
}

.card-tags {
  @apply flex flex-wrap gap-1.5;
}

.file-type-tag {
  @apply px-1.5 py-0.5 border-none rounded font-normal text-xs;
}

.file-type-tag.system {
  @apply bg-purple-100 text-purple-600;
}

.file-type-tag.user {
  @apply bg-yellow-100 text-yellow-600;
}

.file-type-tag:not(.system):not(.user) {
  @apply bg-purple-100 text-purple-600;
}

.custom-tag {
  @apply px-1.5 py-0.5 border border-gray-200 rounded font-normal text-xs text-gray-500 bg-white;
}

.kb-tag {
  @apply px-1.5 py-0.5 border border-gray-200 rounded font-normal text-xs text-gray-500 bg-gray-50;
}

.more-tag {
  @apply px-1.5 py-0.5 border border-gray-200 rounded font-normal text-xs text-gray-400 bg-gray-50;
}

.card-footer {
  @apply mt-4 pt-3 flex justify-between items-center relative;
}

.default-info {
  @apply opacity-100 transition-opacity duration-300 ease-in-out;
}

.default-info .card-time {
  @apply text-xs text-gray-400 m-0;
}

.hover-info {
  @apply absolute bottom-0 left-0 w-full opacity-0 transition-opacity duration-300 ease-in-out pointer-events-none;
}

.hover-info .hover-time {
  @apply text-xs text-gray-400 m-0;
}

.card-action-button {
  @apply flex items-center justify-center p-1 bg-white rounded-lg border border-gray-100 opacity-0 transition-all duration-300 h-8 w-8;
}

.card-action-button:hover {
  @apply bg-gray-50;
}

.card-action-button img {
  @apply w-4 h-4;
}

/* 模态框样式 */
.custom-footer {
  @apply flex justify-end gap-3;
}

.cancel-button {
  @apply h-10 text-sm bg-white border border-gray-200 text-gray-600 px-6 rounded-lg shadow-none transition-all duration-200 ease-in-out;
}

.cancel-button:hover {
  @apply bg-gray-50 border-gray-200 text-gray-600;
}

.confirm-button {
  @apply h-10 text-sm bg-blue-600 text-white px-6 rounded-lg shadow-none transition-all duration-200 ease-in-out;
}

.confirm-button:hover {
  @apply bg-blue-700 border-blue-600;
}

.rename-modal {
  @apply rounded-lg;
}

.form-container {
  @apply flex gap-4;
}

.icon-section {
  @apply flex-shrink-0;
}

.file-icon {
  @apply w-12 h-12 bg-gray-100 rounded-lg;
}

.divider {
  @apply w-px bg-gray-200;
}

.form-section {
  @apply flex-1;
}

.label-with-required {
  @apply flex items-center gap-1;
}

.required-star {
  @apply text-red-500;
}

/* 文件上传模态框样式 */
.uploader {
  @apply w-full;
}

.uploadModal {
  @apply rounded-lg;
}

.folder-create-modal {
  @apply rounded-lg;
}

.top-section {
  @apply flex gap-4;
}

.folder-icon {
  @apply w-12 h-12 bg-blue-100 rounded-lg;
}

.name-form-section {
  @apply flex-1;
}

.bottom-section {
  @apply mt-4;
}

.tag-form-item {
  @apply mb-4;
}

.tag-header {
  @apply flex justify-between items-start mb-2;
}

.tag-label {
  @apply text-sm font-medium text-gray-700;
}

.tag-hint {
  @apply text-xs text-gray-500;
}

.add-tag-btn {
  @apply h-8 text-sm bg-white border border-gray-200 text-gray-600 px-3 rounded transition-all duration-200 ease-in-out;
}

.add-tag-btn:hover {
  @apply bg-gray-50 border-gray-200 text-gray-600;
}

.selected-item-list {
  @apply space-y-2;
}

.selected-item-row {
  @apply flex items-center gap-2;
}

.delete-icon {
  @apply cursor-pointer text-gray-400 hover:text-red-500 transition-colors duration-200;
}

/* 文件上传模态框样式 */
.uploader {
  @apply w-full;
}

.uploadModal {
  @apply rounded-lg;
}

.customFooter {
  @apply flex justify-end gap-3;
}

.cancelButton {
  @apply h-10 text-sm bg-white border border-gray-200 text-gray-600 px-6 rounded-lg shadow-none transition-all duration-200 ease-in-out;
}

.cancelButton:hover {
  @apply bg-gray-50 border-gray-200 text-gray-600;
}

.confirmButton {
  @apply h-10 text-sm bg-blue-600 text-white px-6 rounded-lg shadow-none transition-all duration-200 ease-in-out;
}

.confirmButton:hover {
  @apply bg-blue-700 border-blue-600;
}

/* 页面切换动画 */
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity var(--page-transition-duration) var(--page-transition-easing),
              transform var(--page-transition-duration) var(--page-transition-easing);
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity var(--page-transition-duration) var(--page-transition-easing),
              transform var(--page-transition-duration) var(--page-transition-easing);
}

/* 全局页面容器动画 */
.page-container {
  animation: page-fade-in var(--page-transition-duration) var(--page-transition-easing);
}

@keyframes page-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
