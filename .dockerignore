# Docker 构建忽略文件

# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Next.js
.next/
.swc/
out/

# 开发环境文件
.env.local
.env.development
.env.test

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
.gitattributes

# 测试和覆盖率
coverage/
.nyc_output
*.lcov

# 日志文件
logs
*.log

# 临时文件
tmp/
temp/

# 构建产物（本地）
dist/
build/

# TypeScript
*.tsbuildinfo

# ESLint
.eslintcache

# 文档和说明
README.md
docs/
*.md

# 开发脚本
scripts/

# Docker 相关
Dockerfile*
docker-compose*
.dockerignore

# 其他开发工具
.husky/
.lint-staged
.prettierrc*
.editorconfig

# 测试文件
**/*.test.*
**/*.spec.*
__tests__/
__mocks__/