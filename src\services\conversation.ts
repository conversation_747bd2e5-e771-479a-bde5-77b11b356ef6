/**
 * 会话API服务层
 * 基于 axios 和现有 API 结构，提供统一的会话相关API接口
 */

import { AxiosResponse } from 'axios';
import {
  ConversationItem,
  ConversationMessage,
  GetConversationsParams,
  CreateConversationParams,
  SendMessageParams,
  UpdateConversationTitleParams,
  PaginatedResponse,
  ConversationError} from '@/types/conversation';
import { transformApiMessageList } from '@/utils/data-transform';
import { createApiClient } from '@/lib/http/errorInterceptor';

// ============== API 基础配置 ==============

/** 创建带有全局错误处理的 API 客户端 */
const api = createApiClient();

// ============== 错误处理工具 ==============



// ============== API 接口实现 ==============

/**
 * 获取会话列表
 * 🔧 兼容性处理：支持有分页和无分页两种API响应格式
 */
export async function getConversations(
  params: GetConversationsParams
): Promise<PaginatedResponse<ConversationItem> | ConversationItem[]> {
  try {
    const response = await api.post(
      '/api/conversations',
      {
        pager: params.pager,
        user_id: params.user_id,
        channel: params.channel,
      }
    );
    
    const responseData = response.data;
    
    // 🔧 检测响应格式并进行兼容性处理
    if (responseData && typeof responseData === 'object') {
      // 如果有 pager 字段，说明是标准分页响应
      if (responseData.pager && responseData.data) {
        console.log('📄 使用标准分页响应格式');
        return responseData as PaginatedResponse<ConversationItem>;
      }
      
      // 如果是直接的数组或有 data 字段但没有 pager
      if (Array.isArray(responseData)) {
        console.log('📋 检测到数组格式响应，转换为兼容格式');
        return responseData as ConversationItem[];
      }
      
      // 如果有 data 字段但没有 pager，可能是 { data: [...] } 格式
      if (responseData.data && Array.isArray(responseData.data)) {
        console.log('📋 检测到 data 数组格式响应，转换为兼容格式');
        return responseData.data as ConversationItem[];
      }
      
      // 🔧 新增：如果是 { items: [...], count: number } 格式
      if (responseData.items && Array.isArray(responseData.items)) {
        console.log('📋 检测到 items 数组格式响应，转换为兼容格式');
        return responseData.items as ConversationItem[];
      }
    }
    
    // 如果都不匹配，返回空数组
    console.warn('⚠️ 无法识别的响应格式:', responseData);
    return [];
  } catch (error) {
    throw error;
  }
}

/**
 * 创建新会话
 */
export async function createConversation(
  params: CreateConversationParams
): Promise<ConversationItem> {
  try {
    const response: AxiosResponse<ConversationItem> = await api.post(
      `/api/conversation/${params.agentId}`,
      {
        states: [
          {
            key: 'channel',
            value: 'employee'
          }
        ]
      }
    );
    
    return response.data;
  } catch (error) {
    throw error;
  }
}

/**
 * 获取会话消息列表
 * 🔧 兼容性处理：转换 API 响应格式为前端期望格式
 */
export async function getConversationMessages(
  conversationId: string
): Promise<ConversationMessage[]> {
  try {
    const response = await api.get(
      `/api/conversation/${conversationId}/dialogs`
    );
    
    const responseData = response.data;
    
    // 🔧 检测和转换响应格式
    if (Array.isArray(responseData)) {
      console.log('📋 检测到消息数组格式响应，进行转换');
      return transformApiMessageList(responseData);
    }
    
    // 如果不是数组，可能是其他格式
    if (responseData && responseData.data && Array.isArray(responseData.data)) {
      console.log('📋 检测到包装的消息数组格式响应，进行转换');
      return transformApiMessageList(responseData.data);
    }
    
    // 如果都不匹配，返回空数组
    console.warn('⚠️ 无法识别的消息响应格式:', responseData);
    return [];
  } catch (error) {
    throw error;
  }
}

/**
 * 发送消息
 */
export async function sendMessage(
  params: SendMessageParams
): Promise<ConversationMessage> {
  try {
    const response: AxiosResponse<ConversationMessage> = await api.post(
      `/api/conversation/${params.agentId}/${params.conversationId}`,
      {
        text: params.text,
        states: params.states || [],
        postback: params.postback
      }
    );
    
    return response.data;
  } catch (error) {
    throw error;
  }
}

/**
 * 更新会话标题别名
 */
export async function updateConversationTitle(
  params: UpdateConversationTitleParams
): Promise<void> {
  try {
    await api.put(
      `/api/conversation/${params.conversationId}/update-title-alias`,
      {
        newTitleAlias: params.newTitleAlias
      }
    );
  } catch (error) {
    throw error;
  }
}

/**
 * 删除会话
 */
export async function deleteConversation(conversationId: string): Promise<void> {
  try {
    await api.delete(`/api/conversation/${conversationId}`);
  } catch (error) {
    throw error;
  }
}

// ============== 工具函数 ==============

/**
 * 获取默认分页参数
 */
export function getDefaultPagerParams(): {
  page: number;
  size: number;
  count: number;
  sort: 'updated_time';
  order: 'desc';
} {
  return {
    page: 1,
    size: 20,
    count: 0,
    sort: 'updated_time',
    order: 'desc'
  };
}

/**
 * 构建获取会话列表的默认参数
 */
export function buildGetConversationsParams(
  userId: string,
  page: number = 1,
  size: number = 20
): GetConversationsParams {
  return {
    user_id: userId,
    pager: {
      page,
      size,
      sort: 'updated_time',
      order: 'desc',
      offset: 0,
      returnTotal: false
    },
    channel: 'employee',
    status: null,
    taskId: null,
    states: [],
    tags: []
  };
}

/**
 * 构建创建会话的默认参数
 */
export function buildCreateConversationParams(
  agentId: string,
): CreateConversationParams {
  return {
    agentId
  };
}

/**
 * 构建发送消息的参数
 */
export function buildSendMessageParams(
  agentId: string,
  conversationId: string,
  text: string,
  states?: string[],
  postback?: any
): SendMessageParams {
  return {
    agentId,
    conversationId,
    text,
    states: states || [],
    postback
  };
}

// ============== 类型守卫 ==============

/**
 * 检查是否为会话错误
 */
export function isConversationError(error: any): error is ConversationError {
  return (
    error &&
    typeof error === 'object' &&
    typeof error.code === 'string' &&
    typeof error.message === 'string' &&
    typeof error.timestamp === 'number'
  );
}

/**
 * 检查是否为网络相关错误
 */
export function isNetworkError(error: ConversationError): boolean {
  return ['NETWORK_ERROR', 'TIMEOUT_ERROR'].includes(error.code);
}

/**
 * 检查是否为认证相关错误
 */
export function isAuthError(error: ConversationError): boolean {
  return ['AUTH_ERROR', 'PERMISSION_ERROR'].includes(error.code);
}

/**
 * 检查是否为服务器错误
 */
export function isServerError(error: ConversationError): boolean {
  return error.code === 'SERVER_ERROR';
}

/**
 * 检查是否为可重试的错误
 */
export function isRetryableError(error: ConversationError): boolean {
  return isNetworkError(error) || isServerError(error);
}