import { createApiClient } from '@/lib/http/errorInterceptor';
const api = createApiClient();

export async function getAgentDetail(agentId: string) {
    const response = await api.get(`/api/agent/${agentId}`);
    return response.data;
}

export async function createAgent(data: {
    name?: string;
    description?: string;
    knowledge_bases?: { name: string; description?: string; type?: string }[];
    instruction?: string;
}) {
    const response = await api.post("/api/agent", data);
    return response.data;
}

// 更新智能体
export async function updateAgent(agentId: string, data: {
    name?: string;
    description?: string;
    knowledge_bases?: { name: string; description?: string; type?: string }[];
    instruction?: string;
}) {
    const response = await api.put(`/api/agent/${agentId}`, data);
    return response.data;
} 