// SignalR Client 实现
// 直接使用@microsoft/signalr，无需Mock

import { HttpTransportType, HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr';
import { 
  SignalRConfig, 
  AGUIEvent, 
  EventListener, 
  EventFilter, 
  EventType, 
  PerformanceMetrics 
} from './types';

export class SignalRClient {
  private config: SignalRConfig;
  private connection: HubConnection | null = null;
  private eventListeners: Map<string, EventListener[]> = new Map();
  private performanceMetrics: PerformanceMetrics;
  private aguiEventListener?: (event: AGUIEvent) => void;

  constructor(config: SignalRConfig) {
    this.config = config;
    this.performanceMetrics = {
      totalEvents: 0,
      averageResponseTime: 0,
      toolCallsCount: 0,
      errorsCount: 0,
      connectionUptime: 0
    };
  }

  async connect(): Promise<void> {
    this.connection = new HubConnectionBuilder()
      .withUrl(this.config.hubUrl,
          {
          transport: HttpTransportType.WebSockets,
          skipNegotiation: true,
        })
      .withAutomaticReconnect()
      .configureLogging(LogLevel.Information)
      .build();

    // 设置事件监听
    this.connection.on('AGUIEvent', (event: AGUIEvent) => {
      this.aguiEventListener?.(event);
    });

    await this.connection.start();
    console.log('SignalR连接建立成功');
  }

  async disconnect(): Promise<void> {
    if (!this.connection) {
      console.log('🔌 SignalR连接已处于断开状态，无需重复断开');
      return;
    }

    const connectionState = this.connection.state;
    console.log(`🔌 开始断开SignalR连接 (当前状态: ${connectionState})`);

    try {
      if (connectionState !== 'Disconnected') {
        await this.connection.stop();
        console.log('✅ SignalR连接已成功断开');
      }
    } catch (error) {
      console.warn('⚠️ 断开SignalR连接时发生错误:', error);
    } finally {
      // 清理连接引用和事件监听器
      this.connection = null;
      this.eventListeners.clear();
      console.log('🧹 SignalR连接资源已清理');
    }
  }

  isConnected(): boolean {
    // 简化实现，基于连接对象是否存在
    return this.connection !== null;
  }

  setAGUIEventListener(listener: (event: AGUIEvent) => void): void {
    this.aguiEventListener = listener;
  }

  addEventListener<T extends AGUIEvent>(
    eventType: EventType | EventType[],
    listener: EventListener<T>,
  ): () => void {
    const types = Array.isArray(eventType) ? eventType : [eventType];
    
    types.forEach(type => {
      if (!this.eventListeners.has(type)) {
        this.eventListeners.set(type, []);
      }
      this.eventListeners.get(type)!.push(listener as EventListener);
    });

    // 返回取消监听函数
    return () => {
      types.forEach(type => {
        const listeners = this.eventListeners.get(type);
        if (listeners) {
          const index = listeners.indexOf(listener as EventListener);
          if (index > -1) {
            listeners.splice(index, 1);
          }
        }
      });
    };
  }

  async invoke(methodName: string, ...args: any[]): Promise<any> {
    if (!this.connection) {
      throw new Error('SignalR连接未建立');
    }
    return await this.connection.invoke(methodName, ...args);
  }

  on(eventName: string, callback: (...args: any[]) => void): void {
    if (this.connection) {
      console.log('🔔 注册事件监听器', eventName);
      this.connection.on(eventName, callback);
    }
  }

  onreconnecting(callback: () => void): void {
    if (this.connection) {
      this.connection.onreconnecting(callback);
    }
  }

  onreconnected(callback: () => void): void {
    if (this.connection) {
      this.connection.onreconnected(callback);
    }
  }

  onclose(callback: () => void): void {
    if (this.connection) {
      this.connection.onclose(callback);
    }
  }

  async start(): Promise<void> {
    if (this.connection) {
      await this.connection.start();
    }
  }

  async stop(): Promise<void> {
    if (this.connection) {
      await this.connection.stop();
    }
  }

  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  private handleEvent(event: AGUIEvent): void {
    console.log('🔔 收到AGUIEvent事件', event);
    
    this.performanceMetrics.totalEvents++;
    
    const listeners = this.eventListeners.get(event.type) || [];
    listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in event listener:', error);
        this.performanceMetrics.errorsCount++;
      }
    });
  }
}

// 工厂函数
export function createSignalRClient(config: SignalRConfig): SignalRClient {
  return new SignalRClient(config);
}