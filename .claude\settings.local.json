{"permissions": {"allow": ["Bash(npm config set:*)", "Bash(npx create-next-app:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:github.com)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run build:*)", "Bash(grep:*)", "Bash(git add:*)", "Bash(cp:*)", "Bash(find:*)", "WebFetch(domain:tailwindcss.com)", "Bash(ls:*)", "Bash(git commit:*)", "Bash(npm run type-check:*)", "Bash(npm run:*)", "Bash(npx tsc:*)", "WebFetch(domain:docs.copilotkit.ai)", "Bash(npx eslint:*)", "Bash(rg:*)", "mcp__ide__getDiagnostics", "Bash(pnpm add:*)"], "deny": []}}