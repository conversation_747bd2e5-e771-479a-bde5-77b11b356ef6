'use client';

import { useEffect, useRef, useCallback, useMemo, useState } from 'react';
import { useAGUIContext } from '@/lib/agui/AGUIProvider';
import { chatEngine } from '@/lib/chat-engine/chat-engine';
import { ChatEngineConfig, ChatStateData } from '@/lib/chat-engine/types';
import { chatEngineAdapter, AdapterState } from '@/store/chat-engine-adapter';

/**
 * 统一的ChatEngine Hook
 * 整合AGUIProvider和ChatEngine，提供完整的聊天功能
 */
export interface UseChatEngineOptions {
  conversationId: string;
  agentId: string;
  enableLogging?: boolean;
  autoConnect?: boolean;
}

export interface UseChatEngineReturn {
  // 状态数据
  state: ChatStateData;
  
  // 连接状态
  isConnected: boolean;
  connectionState: string;
  
  // 消息操作
  sendMessage: (content: string) => Promise<void>;
  
  // 连接管理
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  
  // 会话管理
  switchConversation: (conversationId: string) => Promise<void>;
  loadMessages: (conversationId: string) => Promise<void>;
}

export function useChatEngine(options: UseChatEngineOptions): UseChatEngineReturn {
  const {
    conversationId,
    agentId,
    enableLogging = false,
  } = options;

  // 使用AGUIProvider上下文
  const aguiContext = useAGUIContext();
  
  // 使用ChatEngineAdapter获取状态
  const [adapterState, setAdapterState] = useState<AdapterState>(chatEngineAdapter.getState());
  
  // 监听adapter状态变化
  useEffect(() => {
    const unsubscribe = chatEngineAdapter.subscribe((newState) => {
      setAdapterState(newState);
    });
    
    return unsubscribe;
  }, []);
  
  // 从adapter状态中获取ChatEngine状态
  const state: ChatStateData = adapterState.engineState;
  
  const isInitializedRef = useRef(false);

  // 初始化ChatEngine
  useEffect(() => {
    const initializeChatEngine = async () => {
      if (isInitializedRef.current) return;

      try {
        const config: ChatEngineConfig = {
          conversationId,
          agentId,
          enableLogging
        };

        await chatEngine.initialize(config);
        isInitializedRef.current = true;
        
        if (enableLogging) {
          console.log('✅ [useChatEngine] ChatEngine初始化完成', config);
        }
      } catch (error) {
        console.error('[useChatEngine] ChatEngine初始化失败:', error);
      }
    };

    initializeChatEngine();
  }, [conversationId, agentId, enableLogging]);

  // 更新AGUIProvider配置
  useEffect(() => {
    aguiContext.updateConfig({
      conversationId,
      agentId
    });
  }, [conversationId, agentId, aguiContext]);

  // 发送消息（使用HTTP API）
  const sendMessage = useCallback(async (content: string) => {
    try {
      // 通过ChatEngine的MessageService发送（使用HTTP API）
      const messageService = chatEngine.getMessageService();
      await messageService.sendMessage(conversationId, content);
      
      if (enableLogging) {
        console.log('📤 [useChatEngine] 消息发送成功');
      }
    } catch (error) {
      console.error('[useChatEngine] 消息发送失败:', error);
      throw error;
    }
  }, [conversationId, enableLogging]);

  // 连接管理
  const connect = useCallback(async () => {
    try {
      await aguiContext.connect();
      if (enableLogging) {
        console.log('🔗 [useChatEngine] 连接成功');
      }
    } catch (error) {
      console.error('[useChatEngine] 连接失败:', error);
      throw error;
    }
  }, [aguiContext, enableLogging]);

  const disconnect = useCallback(async () => {
    try {
      await aguiContext.disconnect();
      if (enableLogging) {
        console.log('🔌 [useChatEngine] 断开连接');
      }
    } catch (error) {
      console.error('[useChatEngine] 断开连接失败:', error);
      throw error;
    }
  }, [aguiContext, enableLogging]);

  // 会话切换
  const switchConversation = useCallback(async (newConversationId: string) => {
    try {
      // 更新ChatEngine配置
      await chatEngine.updateConfig({
        conversationId: newConversationId,
        agentId
      });
      
      // 更新AGUIProvider配置
      aguiContext.updateConfig({
        conversationId: newConversationId,
        agentId
      });
      
      if (enableLogging) {
        console.log('🔄 [useChatEngine] 会话切换完成:', newConversationId);
      }
    } catch (error) {
      console.error('[useChatEngine] 会话切换失败:', error);
      throw error;
    }
  }, [agentId, aguiContext, enableLogging]);

  // 加载消息
  const loadMessages = useCallback(async (conversationId: string) => {
    try {
      const messageService = chatEngine.getMessageService();
      await messageService.loadMessages(conversationId);
      
      if (enableLogging) {
        console.log('📥 [useChatEngine] 消息加载完成');
      }
    } catch (error) {
      console.error('[useChatEngine] 消息加载失败:', error);
      throw error;
    }
  }, [enableLogging]);

  return useMemo(() => ({
    // 状态数据（来自ChatEngine）
    state,
    
    // 连接状态（来自AGUIProvider）
    isConnected: aguiContext.isConnected,
    connectionState: aguiContext.connectionState,
    
    // 消息操作
    sendMessage,
    
    // 连接管理
    connect,
    disconnect,
    
    // 会话管理
    switchConversation,
    loadMessages,
  }), [
    state,
    aguiContext.isConnected,
    aguiContext.connectionState,
    sendMessage,
    connect,
    disconnect,
    switchConversation,
    loadMessages
  ]);
}