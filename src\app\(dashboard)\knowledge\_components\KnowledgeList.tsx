import {
  DeleteOutlined,
  ExclamationCircleFilled,
  PlusOutlined,
  SearchOutlined
} from "@ant-design/icons";
import {
  Button,
  Col,
  Divider,
  Input,
  Layout,
  Modal,
  Row,
  Select,
  Skeleton,
  Spin,
  Tag,
  Typography
} from "antd";
import {
  useCallback,
  useMemo,
  useRef
} from "react";
// import IconScreen from "@/assets/application/screen.svg";
import { formatDate } from "@/utils/date";
import { useRouter } from "next/navigation";
import {
  useDeleteKnowledge,
  useFetchGetKbTags,
  useInfiniteFetchKnowledgeList,
} from "../_hooks/knowledge-hooks";
import KnowledgeCard, { Action } from "./KnowledgeCard";
// import IconEmptyModelConfig from "@/assets/svg/IconEmptyModelConfig.svg";
import InfiniteScroll from "react-infinite-scroll-component";
import EmptyData from "../_components/emptyData";
import { useSetNextDocumentStatus } from "../_hooks/document-hooks";
import KnowledgeModal from "./KnowledgeModal";

const Option = Select.Option;
const { Text } = Typography;
const { Header, Content } = Layout;

const initActionButtons: Action[] = [
  {
    label: "编辑",
    className: "text-[#333333]",
    type: "edit",
    icon: <></>,
  },
  {
    label: "删除",
    className: "text-[#333333]",
    type: "del",
    icon: <DeleteOutlined />,
  },
] as const;

const { confirm } = Modal;

// 知识库列表
export default function KnowledgeList() {
  const router = useRouter();

  const {
    fetchNextPage,
    data,
    hasNextPage,
    searchString,
    handleInputChange,
    loading,
    refetch,
    tags,
    setTags,
  } = useInfiniteFetchKnowledgeList();

  const { kbTags } = useFetchGetKbTags();

  const { setDocumentStatus } = useSetNextDocumentStatus();

  const nextList = useMemo(() => {
    const list =
      data?.pages?.flatMap((x) => (Array.isArray(x.kbs) ? x.kbs : [])) ?? [];
    return list;
  }, [data?.pages]);

  const total = useMemo(() => {
    return data?.pages.at(-1).total ?? 0;
  }, [data?.pages]);

  const knowledgeModalRef = useRef<{
    open: (form?: any) => void;
  }>(null);

  const handleAction = (row: any, type: Action["type"]) => {
    switch (type) {
      // 启动
      case "enable":
        break;
      // 禁用
      case "disable":
        break;
      case "edit":
        knowledgeModalRef.current?.open(row);
        break;
      case "del":
        confirmDelete(row);
        break;
    }
  };

  const { deleteKnowledge } = useDeleteKnowledge();

  const confirmDelete = (data: any) => {
    confirm({
      title: "删除知识库",
      icon: <ExclamationCircleFilled />,
      content: `“${data.name}”内文件将被删除无法恢复`,
      onOk() {
        deleteKnowledge(data.id);
      },
      onCancel() {},
    });
  };

  const getActionButtons = useCallback(
    (row: any) => {
      if (row.status === "1") {
        const disableActionButtons: Action[] = [
          {
            label: "禁用",
            className: "text-[#d54941]",
            type: "disable",
            icon: <></>,
          },
        ];
        return initActionButtons.concat(disableActionButtons);
      }
      const startActionButtons: Action[] = [
        {
          label: "启动",
          className: "text-[#2ba471]",
          type: "enable",
          icon: <></>,
        },
      ];
      return initActionButtons.concat(startActionButtons);
    },
    [initActionButtons]
  );

  const handleSwitch = (row: any, value: boolean) => {
    console.log(row, value);
  };

  const handleToDataset = (row: any) => {
    router.push(`/knowledge/dataset?id=${row.id}`);
  };

  return (
    <>
      <Header
        className="flex justify-between items-center"
        style={{ background: "unset", padding: "0px" }}
      >
        <Row justify="center">
          <Col flex="auto">
            <Button
              icon={<PlusOutlined />}
              color="default"
              variant="solid"
              type="primary"
              onClick={() => knowledgeModalRef.current?.open()}
            >
              创建库
            </Button>
          </Col>
          <Col>
            <Divider type="vertical" />
          </Col>

          <Col flex="200px">
            <Input
              prefix={<SearchOutlined />}
              placeholder="AI搜索..."
              allowClear
              onChange={(e) => handleInputChange(e)}
              className="bg-[#f2f2f2]"
            />
          </Col>
        </Row>
        <Row justify="center" gutter={8}>
          <Col flex="200px">
            <Select
              className="w-full"
              placeholder="标签"
              mode="multiple"
              allowClear={true}
              value={tags}
              onChange={(value) => setTags(value)}
            >
              {kbTags?.map((tag, tagIndex) => (
                <Option key={tagIndex} value={tag}>
                  {tag}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>
      </Header>
      <Content
        className="!flex-1 !basis-0 overflow-auto w-[calc(100%+18px)] p-[9px] ml-[-9px]"
        id="scrollableDiv"
      >
        <Spin
          wrapperClassName="block h-full relative [&_.ant-spin-container]:h-full"
          spinning={loading}
        >
          <InfiniteScroll
            dataLength={nextList?.length ?? 0}
            next={fetchNextPage}
            hasMore={hasNextPage}
            loader={
              <Skeleton
                avatar={{ shape: "circle" }}
                paragraph={{
                  rows: 2,
                }}
              />
            }
            endMessage={!!total && <Divider>没有更多数据了 🤐</Divider>}
            scrollableTarget="scrollableDiv"
            scrollThreshold="200px"
            style={{
              overflow: "unset",
            }}
          >
            <div className="grid grid-cols-4 gap-4">
              {nextList?.length > 0 ? (
                nextList.map((item, index) => {
                  return (
                    <KnowledgeCard
                      key={`${item?.name}-${index}`}
                      tag={
                        <>
                          {item.permission === "0" ? (
                            <Tag className="rounded-[4px]" color="warning">
                              公开
                            </Tag>
                          ) : (
                            <Tag className="rounded-[4px]" color="processing">
                              部分成员可访问
                            </Tag>
                          )}
                          {(item?.tags as string[]).map((tag, tagIndex) => (
                            <Tag
                              key={tagIndex}
                              className="rounded-[4px] bg-white text-[#5c5c5c] border-[#ebebeb]"
                            >
                              {tag}
                            </Tag>
                          ))}
                        </>
                      }
                      footerTag={
                        <div
                          className={`size-[8px] rounded-[3px] border ${
                            item.status === "1"
                              ? "border-[#309b4c] bg-[#34a853]"
                              : "border-[#d54135] bg-[#e74639]"
                          }`}
                        ></div>
                      }
                      actionButtons={getActionButtons(item)}
                      description={item.description}
                      title={item.name}
                      createTime={formatDate(
                        item.update_time,
                        "YYYY-MM-DD HH:mm:ss"
                      )}
                      handleSwitch={(value) => handleSwitch(item, value)}
                      handleAction={(type) => handleAction(item, type)}
                      onClick={() => handleToDataset(item)}
                    />
                  );
                })
              ) : (
                <EmptyData />
              )}
            </div>
          </InfiniteScroll>
        </Spin>
      </Content>
      <KnowledgeModal ref={knowledgeModalRef} />
    </>
  );
}
