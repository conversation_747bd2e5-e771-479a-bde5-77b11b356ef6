'use client';

import React from 'react';
import { useChatState, useChatMessages } from '@/store/chatStore';
import { ChatState } from '@/lib/chat-engine/types';

/**
 * 聊天状态显示组件
 * 显示连接状态、消息计数等信息
 */
export default function ChatStatus() {
  const { isConnected, currentState, isStreaming, isSending } = useChatState();
  const { messages } = useChatMessages();
  
  // 获取状态文本
  const getStatusText = (state: ChatState, isConnected: boolean): string => {
    if (!isConnected) {
      return '连接断开';
    }

    switch (state) {
      case ChatState.IDLE:
        return '已连接';
      case ChatState.TYPING:
        return '正在输入';
      case ChatState.SENDING:
        return '发送中';
      case ChatState.RECEIVING:
        return 'AI正在思考';
      case ChatState.STREAMING:
        return 'AI正在回复';
      case ChatState.ERROR:
        return '出现错误';
      case ChatState.DISCONNECTED:
        return '连接断开';
      default:
        return '未知状态';
    }
  };
  
  const statusText = getStatusText(currentState, isConnected);
  const messageCount = messages.length;

  return (
    <div className="flex items-center justify-between text-xs text-text-muted mt-2">
      <div className="flex items-center space-x-4">
        <span>与 AI助手 的对话</span>
        
        {/* 流式消息状态 */}
        {isStreaming && (
          <span className="flex items-center space-x-1 text-success">
            <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
            <span>AI正在思考和回复...</span>
          </span>
        )}
        
        {/* 发送状态 */}
        {isSending && (
          <span className="flex items-center space-x-1 text-brand">
            <div className="w-2 h-2 bg-brand rounded-full animate-pulse"></div>
            <span>消息发送中...</span>
          </span>
        )}
      </div>
      
      <div className="flex items-center space-x-2">
        {/* 连接状态 */}
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${
            isConnected ? 'bg-success' : 'bg-layer-2'
          }`}></div>
          <span>{statusText}</span>
        </div>
        
        {/* 消息计数 */}
        <span>{messageCount} 条消息</span>
      </div>
    </div>
  );
}