.fileManagerWrapper {
  // width: 100%;
  // padding: 32px;
}

.filter {
  // height: 32px;
  display: flex;
  margin: 0px 0 20px 0;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #f5f5f5;
}

.deleteIconWrapper {
  width: 22px;
  text-align: center;
}

.linkButton {
  padding: 0;
}

.breadcrumbItemButton {
  cursor: pointer;
  color: #1677ff;
  padding: 0;
  height: auto;
}

// 文件工具栏上传按钮专属样式
.rootCreateFolderButton,
.fileToolbarUploadButton {
  height: 40px;
  font-size: 14px;
  background-color: #4455f2;
  color: #ffffff;
  padding: 8px 24px;
  border-radius: 8px;
  box-shadow: none;
  transition: all 0.2s ease-in-out !important;

  &:hover {
    background-color: #3f4edf !important;
    border-color: #4455f2 !important;
  }
}

// 文件工具栏创建文件组按钮专属样式
.fileToolbarCreateFolderButton {
  height: 40px;
  font-size: 14px;
  background-color: #ffffff;
  border: 1px solid #ebebeb;
  color: #333333;
  padding: 8px 24px;
  border-radius: 8px;
  box-shadow: none;
  transition: all 0.2s ease-in-out !important;

  &:hover {
    background-color: #fafafa !important;
    border-color: #f5f5f5 !important;
    color: #333333 !important;
  }
}

// 文件工具栏返回按钮专属样式
.fileToolbarBackButton {
  height: 40px;
  background-color: #ffffff;
  border: none;
  color: #333333;
  padding: 0;
  border-radius: 8px;
  box-shadow: none;
  transition: all 0.2s ease-in-out !important;
  gap: 24px;
  font-weight: 600;
  font-size: 20px;
  line-height: 32px;
  display: flex;
  align-items: center;

  :global(.ant-btn-icon) {
    width: 32px;
    height: 32px;
  }

  &:hover {
    color: #333333 !important;
  }
}

.fileToolbarBatchButton {
  height: 40px;
  font-size: 14px;
  background-color: #ffffff;
  border: 1px solid #ebebeb;
  color: #333333;
  padding: 8px 24px;
  border-radius: 8px;
  box-shadow: none;
  transition: all 0.2s ease-in-out !important;

  &:hover {
    background-color: #fafafa !important;
    border-color: #f5f5f5 !important;
    color: #333333 !important;
  }
}

.fileToolbarDeleteButton {
  height: 40px;
  font-size: 14px;
  background-color: #ffffff;
  border: 1px solid #fce8e7;
  color: #e74639;
  padding: 8px 24px;
  border-radius: 8px;
  box-shadow: none;
  transition: all 0.2s ease-in-out !important;

  &:hover:not(:disabled) {
    background-color: #fcf1f0 !important;
    border-color: #fce8e7 !important;
    color: #e74639 !important;
  }

  &:disabled {
    background-color: #fef8f8 !important;
    border-color: #fcf1f0 !important;
    color: #e74639 !important;
    cursor: not-allowed !important;
  }
}

.fileToolbarCancelButton {
  height: 40px;
  font-size: 14px;
  background-color: #ffffff;
  border: 1px solid #f0f0f0;
  color: #474747;
  padding: 8px 24px;
  border-radius: 8px;
  box-shadow: none;
  transition: all 0.2s ease-in-out !important;

  &:hover {
    background-color: #fafafa !important;
    border-color: #f0f0f0 !important;
    color: #474747 !important;
  }
}

.fileToolbarSearchInput {
  width: 240px;
  height: 40px;
  border-radius: 8px;
  border: 1px solid #ebebeb;
  padding: 0 16px;
  font-size: 14px;
  color: #333333;
  background-color: #ffffff;
  transition: all 0.2s ease-in-out !important;

  &::placeholder {
    color: #adadad;
  }
}

// 文件表格样式
.fileTable {
  :global(.ant-table) {
    background: transparent;
    border-collapse: separate !important;
    border-spacing: 0 4px !important;
  }
  
  // 直接覆盖 Ant Design 的表格样式
  :global(.css-dev-only-do-not-override-7t2xvq.ant-table-wrapper table),
  :global(.ant-table-wrapper table),
  :global(.ant-table table) {
    border-spacing: 0 4px !important;
  }
  
  // 使用更高优先级的选择器
  :global(.ant-table-wrapper .ant-table table) {
    border-spacing: 0 4px !important;
  }

  :global(.ant-table-container) {
    border: none;
  }

  :global(.ant-table-thead) {
    display: none !important;
  }

  :global(.ant-table-tbody) {
    >tr {
      background: #ffffff !important;
      transition: all 0.3s ease;
      border-radius: 8px !important;
      overflow: hidden !important;
      outline: 1px solid #f0f0f0 !important;
      outline-offset: -1px !important;

      &:hover {
        background-color: #f7f7f7 !important;
      }

      >td {
        border: none !important;
        padding: 10px 12px !important;
        vertical-align: middle;
        background: transparent !important;
        position: relative !important;
      }

      // 第一个单元格（序号列）
      >td:first-child {
        border-top-left-radius: 8px !important;
        border-bottom-left-radius: 8px !important;
        text-align: left !important;
        font-weight: 500 !important;
        color: #999999 !important;
      }
      
      // 文件类型列样式
      >td:nth-child(3) {
        text-align: center !important;
        padding: 8px 12px !important;
        .fileTypeTag {
          padding: 4px 8px !important;
          border-radius: 4px !important;
          font-size: 12px !important;
          font-weight: 500 !important;
          color: #939393 !important;
          background-color: #f5f5f5 !important;
        }
      }

      // 文件大小列样式
      >td:nth-child(4),
      >td:nth-child(5) {
        text-align: center !important;
        color: #999999 !important;
      }

      // 最后一个单元格（操作列）
      >td:last-child {
        border-top-right-radius: 8px !important;
        border-bottom-right-radius: 8px !important;
      }
    }

    :global(.ant-table-measure-row) {
      display: none !important;
    }
  }

  :glo:where(.css-dev-only-do-not-override-7t2xvq).ant-checkbox-checked .ant-checkbox-inner {
    background-color: #1890ff !important;
    border-color: #1890ff !important;
  }

  :global(:where(.css-dev-only-do-not-override-7t2xvq).ant-checkbox-checked .ant-checkbox-inner){
    background-color:#4455f2 !important;
    border-color: #4455f2;
  }

  :global(:where(.css-dev-only-do-not-override-7t2xvq).ant-checkbox:not(.ant-checkbox-disabled):hover .ant-checkbox-inner) {
    border-color: #4455f2 !important;
  }

  // :global(.ant-table-tbody > tr > td:first-child) {
  //   border-top-left-radius: 8px !important;
  //   border-bottom-left-radius: 8px !important;
  // }

  // :global(.ant-table-tbody > tr > td:last-child) {
  //   border-top-right-radius: 8px !important;
  //   border-bottom-right-radius: 8px !important;
  // }

  // // 移除表格默认边框
  // :global(.ant-table-thead) {
  //   display: none;
  // }

  // :global(.ant-table-tbody > tr > td) {
  //   border-bottom: none !important;
  // }

  // :global(.ant-table-tbody > tr:hover > td) {
  //   background: transparent !important;
  // }
}