import { DocumentParserType } from '../../_constants/knowledge';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Flex, Form, Input, InputNumber, Slider, Switch } from 'antd';
import random from 'lodash/random';

export const excludedParseMethods = [
  DocumentParserType.Table,
  DocumentParserType.Resume,
  DocumentParserType.One,
  DocumentParserType.Picture,
  DocumentParserType.KnowledgeGraph,
  DocumentParserType.Qa,
  DocumentParserType.Tag,
];

export const showRaptorParseConfiguration = (
  parserId: DocumentParserType | undefined
) => {
  return !excludedParseMethods.some((x) => x === parserId);
};

export const excludedTagParseMethods = [
  DocumentParserType.Table,
  DocumentParserType.KnowledgeGraph,
  DocumentParserType.Tag,
];

export const showTagItems = (parserId: DocumentParserType) => {
  return !excludedTagParseMethods.includes(parserId);
};

// The three types "table", "resume" and "one" do not display this configuration.
const ParseConfiguration = () => {
  const form = Form.useFormInstance();

  const handleGenerate = () => {
    form.setFieldValue(
      ['parser_config', 'raptor', 'random_seed'],
      random(10000)
    );
  };

  return (
    <>
      <Form.Item
        name={['parser_config', 'raptor', 'use_raptor']}
        label={'使用召回增强 RAPTOR 策略'}
        initialValue={false}
        valuePropName="checked"
        tooltip={('为多跳问答任务启用 RAPTOR，详情请见 : https://ragflow.io/docs/dev/enable_raptor。')}
      >
        <Switch />
      </Form.Item>
      <Form.Item
        shouldUpdate={(prevValues, curValues) =>
          prevValues.parser_config.raptor.use_raptor !==
          curValues.parser_config.raptor.use_raptor
        }
      >
        {({ getFieldValue }) => {
          const useRaptor = getFieldValue([
            'parser_config',
            'raptor',
            'use_raptor',
          ]);

          return (
            useRaptor && (
              <>
                <Form.Item
                  name={['parser_config', 'raptor', 'prompt']}
                  label={'提示词'}
                  initialValue={
                    '请总结以下段落。 小心数字，不要编造。 段落如下'
                  }
                  tooltip={
                    '系统提示为大模型提供任务描述、规定回复方式，以及设置其他各种要求。系统提示通常与 key （变量）合用，通过变量设置大模型的输入数据。你可以通过斜杠或者 (x) 按钮显示可用的 key。'
                  }
                  rules={[
                    {
                      required: true,
                      message: '提示词是必填项',
                    },
                  ]}
                >
                  <Input.TextArea rows={8} />
                </Form.Item>
                <Form.Item
                  label={('最大token数')}
                  tooltip={'用于设定每个被总结的文本块的最大 token 数。'}
                >
                  <Flex gap={20} align="center">
                    <Flex flex={1}>
                      <Form.Item
                        name={['parser_config', 'raptor', 'max_token']}
                        noStyle
                        initialValue={256}
                        rules={[
                          {
                            required: true,
                            message: '最大token数是必填项',
                          },
                        ]}
                      >
                        <Slider max={2048} style={{ width: '100%' }} />
                      </Form.Item>
                    </Flex>
                    <Form.Item
                      name={['parser_config', 'raptor', 'max_token']}
                      noStyle
                      rules={[
                        {
                          required: true,
                          message: '最大token数是必填项',
                        },
                      ]}
                    >
                      <InputNumber max={2048} min={0} />
                    </Form.Item>
                  </Flex>
                </Form.Item>
                <Form.Item
                  label={'阈值'}
                  tooltip={
                    '在 RAPTOR 中，数据块会根据它们的语义相似性进行聚类。阈值设定了数据块被分到同一组所需的最小相似度。阈值越高，每个聚类中的数据块越少；阈值越低，则每个聚类中的数据块越多。'
                  }
                >
                  <Flex gap={20} align="center">
                    <Flex flex={1}>
                      <Form.Item
                        name={['parser_config', 'raptor', 'threshold']}
                        noStyle
                        initialValue={0.1}
                        rules={[
                          {
                            required: true,
                            message: '阈值是必填项',
                          },
                        ]}
                      >
                        <Slider
                          min={0}
                          max={1}
                          style={{ width: '100%' }}
                          step={0.01}
                        />
                      </Form.Item>
                    </Flex>
                    <Form.Item
                      name={['parser_config', 'raptor', 'threshold']}
                      noStyle
                      rules={[
                        {
                          required: true,
                          message: '阈值是必填项',
                        },
                      ]}
                    >
                      <InputNumber max={1} min={0} step={0.01} />
                    </Form.Item>
                  </Flex>
                </Form.Item>
                <Form.Item
                  label={'最大聚类数'}
                  tooltip={'最多可创建的聚类数。'}
                >
                  <Flex gap={20} align="center">
                    <Flex flex={1}>
                      <Form.Item
                        name={['parser_config', 'raptor', 'max_cluster']}
                        noStyle
                        initialValue={64}
                        rules={[
                          {
                            required: true,
                            message: '最大聚类数是必填项',
                          },
                        ]}
                      >
                        <Slider min={1} max={1024} style={{ width: '100%' }} />
                      </Form.Item>
                    </Flex>
                    <Form.Item
                      name={['parser_config', 'raptor', 'max_cluster']}
                      noStyle
                      rules={[
                        {
                          required: true,
                          message: '最大聚类数是必填项',
                        },
                      ]}
                    >
                      <InputNumber max={1024} min={1} />
                    </Form.Item>
                  </Flex>
                </Form.Item>
                <Form.Item label={'随机种子'}>
                  <Flex gap={20} align="center">
                    <Flex flex={1}>
                      <Form.Item
                        name={['parser_config', 'raptor', 'random_seed']}
                        noStyle
                        initialValue={0}
                        rules={[
                          {
                            required: true,
                            message: '随机种子是必填项',
                          },
                        ]}
                      >
                        <InputNumber style={{ width: '100%' }} />
                      </Form.Item>
                    </Flex>
                    <Form.Item noStyle>
                      <Button type="primary" onClick={handleGenerate}>
                        <PlusOutlined />
                      </Button>
                    </Form.Item>
                  </Flex>
                </Form.Item>
              </>
            )
          );
        }}
      </Form.Item>
    </>
  );
};

export default ParseConfiguration;
