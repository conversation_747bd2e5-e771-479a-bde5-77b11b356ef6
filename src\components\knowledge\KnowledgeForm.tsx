'use client';

import React, { useState, useEffect } from 'react';
import { useBoolean, useToggle } from 'ahooks';
import { KnowledgeCreateParams, KnowledgeUpdateParams } from '@/types/knowledge-api';
import { IKnowledge } from '@/types/knowledge';

interface KnowledgeFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: KnowledgeCreateParams | KnowledgeUpdateParams) => Promise<void>;
  initialData?: IKnowledge;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

export default function KnowledgeForm({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  isLoading = false,
  mode
}: KnowledgeFormProps) {
  // 表单状态管理
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    permission: 'public' as 'public' | 'partial'
  });

  const [showAdvanced, { toggle: toggleAdvanced }] = useToggle(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 初始化表单数据
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && initialData) {
        setFormData({
          name: initialData.name,
          description: initialData.description,
          permission: initialData.permission as 'public' | 'partial'
        });        
      } else {
        // 重置为初始状态
        setFormData({
          name: '',
          description: '',
          permission: 'public' as 'public' | 'partial'
        });
      }
      setErrors({});
    }
  }, [isOpen, mode, initialData]);

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
 
    if (!formData.name?.trim()) {
      newErrors.name = '知识库名称不能为空';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = '知识库名称至少需要2个字符';
    } else if (formData.name.trim().length > 50) {
      newErrors.name = '知识库名称不能超过50个字符';
    }

    if (formData.description?.length > 200) {
      newErrors.description = '描述不能超过200个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const submitData = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        permission: formData.permission
      };

      await onSubmit(submitData);
      onClose();
    } catch (error) {
      // 错误处理在上层hook中已经处理
      console.error('表单提交失败:', error);
    }
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // 模态框背景点击关闭
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-[#00000080] flex items-center justify-center z-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-white bg-layer-1 rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-text-primary">
            {mode === 'create' ? '创建知识库' : '编辑知识库'}
          </h2>
          <button
            onClick={onClose}
            className="text-text-secondary hover:text-text-primary transition-colors"
          >
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 知识库名称 */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              知识库名称 <span className="text-error">*</span>
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="请输入知识库名称"
              className={`w-full px-3 py-2 border rounded-lg bg-bg-base text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary ${
                errors.name ? 'border-error' : 'border-border-light'
              }`}
            />
            {errors.name && (
              <p className="text-error text-sm mt-1">{errors.name}</p>
            )}
          </div>

          {/* 知识库描述 */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              描述
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="请输入知识库描述（可选）"
              rows={3}
              className={`w-full px-3 py-2 border rounded-lg bg-bg-base text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary resize-none ${
                errors.description ? 'border-error' : 'border-border-light'
              }`}
            />
            {errors.description && (
              <p className="text-error text-sm mt-1">{errors.description}</p>
            )}
            <p className="text-text-muted text-xs mt-1">
              {formData.description ? formData.description.length : 0}/200
            </p>
          </div>

          {/* 权限设置 */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              访问权限
            </label>
            <select
              value={formData.permission}
              onChange={(e) => handleInputChange('permission', e.target.value)}
              className="w-full px-3 py-2 border border-border-light rounded-lg bg-bg-base text-text-primary focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="public">公开 - 所有用户可访问</option>
              <option value="partial">部分开放 - 授权用户可访问</option>
            </select>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-border-light">
            <button
              type="button"
              onClick={onClose}
              disabled={isLoading}
              className="px-4 py-2 text-text-secondary hover:text-text-primary transition-colors disabled:opacity-50"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="bg-brand text-bg-base px-6 py-2 rounded-lg font-medium hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '处理中...' : mode === 'create' ? '创建' : '保存'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}