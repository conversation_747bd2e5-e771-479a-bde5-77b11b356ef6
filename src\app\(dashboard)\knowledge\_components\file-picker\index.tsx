import React, { useState, useCallback, useMemo } from "react";
import { Modal, Button, Space, Spin, message } from "antd";
import { useQuery } from "@tanstack/react-query";
import TreePicker, { TreeNode as TreePickerNode } from "@/components/Tree";
import fileManagerService from "@/services/file-manager-service";
import TxtSvg from "../../svg/txt.svg";
import PdfSvg from "../../svg/pdf.svg";
import FolderSvg from "../../svg/folder.svg";
import FolderOutlinedSvg from "../../svg/folder-outlined.svg";

// 文件/文件夹类型定义
interface FileItem {
  id: string;
  name: string;
  type: "file" | "folder";
  parent_id?: string;
  size?: number;
  created_at?: string;
  updated_at?: string;
}

// 树节点数据类型
interface TreeNode extends TreePickerNode {
  key: string;
  title: string;
  icon?: React.ReactNode;
  isLeaf?: boolean;
  children?: TreeNode[];
  fileData?: FileItem;
}

// 组件属性类型
interface FilePickerProps {
  /** 是否显示弹窗 */
  visible: boolean;
  /** 关闭弹窗回调 */
  onCancel: () => void;
  /** 确认选择回调 */
  onConfirm: (selectedFiles: string[]) => void;
  /** 是否允许多选 */
  multiple?: boolean;
  /** 是否只允许选择文件夹 */
  folderOnly?: boolean;
  /** 是否只允许选择文件 */
  fileOnly?: boolean;
  /** 弹窗标题 */
  title?: string;
  /** 弹窗宽度 */
  width?: number;
}

/**
 * 文件选择组件弹窗
 * 提供树形结构的文件/文件夹选择功能
 */
const FilePicker: React.FC<FilePickerProps> = ({
  visible,
  onCancel,
  onConfirm,
  multiple = false,
  folderOnly = false,
  fileOnly = false,
  title = "选择文件",
  width = 600,
}) => {
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([""]);
  const [searchValue, setSearchValue] = useState("");
  const [loadedKeys, setLoadedKeys] = useState<string[]>([""]);
  const [treeDataState, setTreeDataState] = useState<TreeNode[]>([]);

  /**
   * 获取文件列表数据
   */
  const fetchFileList = useCallback(
    async (parentId: string = "", keywords: string = "") => {
      console.log("📡 获取文件列表:", { parent_id: parentId, keywords });
      const { data } = await fileManagerService.listFile({
        parent_id: parentId,
        keywords,
        page_size: 1000, // 获取所有文件用于树形展示
        page: 1,
      });

      console.log("✅ 文件列表响应:", data);
      return data?.data || { files: [], total: 0 };
    },
    []
  );

  /**
   * 根文件列表查询
   */
  const {
    data: rootData,
    isLoading: rootLoading,
    refetch,
  } = useQuery({
    queryKey: ["filePicker", "root", searchValue, visible],
    queryFn: () => fetchFileList("", searchValue),
    enabled: visible,
    refetchOnMount: true,
    staleTime: 0, // 数据立即过期，确保每次都重新获取
  });

  /**
   * 将文件数据转换为树节点
   */
  const convertToTreeNode = useCallback(
    (file: FileItem, isRoot?: boolean): TreeNode => {
      const isFolder = file.type === "folder";

      return {
        key: file.id,
        title: file.name,
        icon:
          isRoot && isFolder ? (
            <FolderOutlinedSvg className="size-[14px]" />
          ) : isFolder ? (
            <FolderSvg className="size-[14px]" />
          ) : (
            getExtensionSvg(file.type)
          ),
        isLeaf: !isFolder,
        fileData: file,
        // 文件夹节点不设置 children，让懒加载生效
        children: undefined,
      };
    },
    []
  );

  const getExtensionSvg = (type: string) => {
    if (type === "txt") {
      return <TxtSvg className="size-[14px]" />;
    } else if (type === "pdf") {
      return <PdfSvg className="size-[14px]" />;
    }
  };

  /**
   * 构建基础树形数据（不包含状态更新）
   */
  const baseTreeData = useMemo(() => {
    if (!rootData?.files) return [];

    return rootData.files
      .filter((file: FileItem) => {
        // 根据配置过滤文件类型
        if (folderOnly && file.type !== "folder") return false;
        if (fileOnly && file.type !== "file") return false;
        return true;
      })
      .map((file: FileItem) => convertToTreeNode(file, true));
  }, [rootData, folderOnly, fileOnly, convertToTreeNode]);

  /**
   * 处理树形数据的合并和更新
   */
  React.useEffect(() => {
    if (!baseTreeData.length) {
      if (searchValue) {
        setTreeDataState([]);
      }
      return;
    }

    // 如果是搜索模式，直接设置新数据
    if (searchValue) {
      setTreeDataState(baseTreeData);
      return;
    }

    // 合并已加载的子节点数据
    const mergeTreeData = (
      newNodes: TreeNode[],
      existingNodes: TreeNode[]
    ): TreeNode[] => {
      return newNodes.map((newNode) => {
        const existingNode = existingNodes.find(
          (node) => node.key === newNode.key
        );
        if (existingNode && existingNode.children) {
          return {
            ...newNode,
            children: existingNode.children,
          };
        }
        return newNode;
      });
    };

    const mergedData = mergeTreeData(baseTreeData, treeDataState);
    setTreeDataState(mergedData);
  }, [baseTreeData, searchValue]);

  /**
   * 最终的树形数据
   */
  const treeData = useMemo(() => {
    return treeDataState;
  }, [treeDataState]);

  /**
   * 动态加载子节点
   */
  const onLoadData = useCallback(
    async (node: TreeNode) => {
      const { key } = node;

      if (loadedKeys.includes(key)) {
        return;
      }

      try {
        const childData = await fetchFileList(key, searchValue);

        // 更新树节点的子节点
        const updateTreeData = (nodes: TreeNode[]): TreeNode[] => {
          return nodes.map((item) => {
            if (item.key === key) {
              return {
                ...item,
                children: childData.files
                  .filter((file: FileItem) => {
                    if (folderOnly && file.type !== "folder") return false;
                    if (fileOnly && file.type !== "file") return false;
                    return true;
                  })
                  .map((file: FileItem) => convertToTreeNode(file)),
              };
            }
            if (item.children) {
              return {
                ...item,
                children: updateTreeData(item.children),
              };
            }
            return item;
          });
        };

        // 更新树形数据状态
        setTreeDataState((prevTreeData) => updateTreeData(prevTreeData));
        setLoadedKeys((prev) => [...prev, key]);
      } catch (error) {
        console.error("加载子节点失败:", error);
        message.error("加载文件夹内容失败");
      }
    },
    [
      fetchFileList,
      searchValue,
      folderOnly,
      fileOnly,
      convertToTreeNode,
      loadedKeys,
    ]
  );

  /**
   * 处理节点选择
   */
  const onSelect = useCallback(
    (
      checkedKeys:
        | React.Key[]
        | { checked: React.Key[]; halfChecked: React.Key[] },
      info: any
    ) => {
      let keys: React.Key[];

      // 处理 checkStrictly 模式下的返回值
      if (Array.isArray(checkedKeys)) {
        keys = checkedKeys;
      } else {
        keys = checkedKeys.checked;
      }

      if (multiple) {
        setSelectedKeys(keys as string[]);
      } else {
        setSelectedKeys(keys.slice(-1) as string[]);
      }
    },
    [multiple]
  );

  /**
   * 处理节点展开
   */
  const onExpand = useCallback((keys: React.Key[]) => {
    setExpandedKeys(keys as string[]);
  }, []);

  /**
   * 处理搜索
   */
  const onSearch = useCallback((value: string) => {
    setSearchValue(value);
    setExpandedKeys([""]); // 重置展开状态
    setLoadedKeys([""]); // 重置加载状态
    setTreeDataState([]); // 重置树形数据状态
  }, []);

  /**
   * 获取选中的文件数据
   */
  const getSelectedFiles = useCallback(() => {
    const findFileInTree = (
      nodes: TreeNode[],
      targetKey: string
    ): FileItem | null => {
      for (const node of nodes) {
        if (node.key === targetKey && node.fileData) {
          return node.fileData;
        }
        if (node.children) {
          const found = findFileInTree(node.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    return selectedKeys
      .map((key) => findFileInTree(treeData, key))
      .filter((file): file is FileItem => file !== null);
  }, [selectedKeys, treeData]);

  /**
   * 确认选择
   */
  const handleConfirm = useCallback(() => {
    const selectedFiles = getSelectedFiles();

    if (selectedFiles.length === 0) {
      message.warning("请选择文件");
      return;
    }

    onConfirm(selectedFiles.map((v) => v.id));
  }, [getSelectedFiles, onConfirm]);

  /**
   * 重置状态
   */
  const resetState = useCallback(() => {
    setSelectedKeys([]);
    setExpandedKeys([""]);
    setSearchValue("");
    setLoadedKeys([""]);
    setTreeDataState([]);
  }, []);

  // 弹窗关闭时重置状态
  const handleCancel = useCallback(() => {
    resetState();
    onCancel();
  }, [resetState, onCancel]);

  /**
   * 监听弹窗打开状态，重新获取数据
   */
  React.useEffect(() => {
    if (visible && refetch) {
      refetch();
    }
  }, [visible, refetch]);

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={handleCancel}
      width={width}
      centered
      footer={
        <Space>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" onClick={handleConfirm}>
            添加
          </Button>
        </Space>
      }
    >
      {/* <div style={{ marginBottom: 16 }}>
        <Search
          placeholder="搜索文件或文件夹"
          allowClear
          enterButton={<SearchOutlined />}
          onSearch={onSearch}
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
        />
      </div> */}

      <div
        style={{
          height: 400,
          overflow: "auto",
          borderTop: "1px solid #0000000F",
          borderBottom: "1px solid #0000000F",
          padding: 8,
        }}
      >
        <Spin spinning={rootLoading}>
          <TreePicker
            treeData={treeData}
            checkedKeys={selectedKeys}
            expandedKeys={expandedKeys}
            loadData={onLoadData}
            onCheck={onSelect}
            onExpand={onExpand}
            checkable
            checkStrictly={!multiple}
            showIcon
            nodeHeight={32}
            indent={20}
            style={{
              width: "100%",
              height: "100%",
            }}
            titleRender={(nodeData) => {
              return nodeData.title;
            }}
          />
        </Spin>
      </div>

      {/* {selectedKeys.length > 0 && (
        <div style={{ marginTop: 12, padding: 8, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
          <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>已选择:</div>
          {getSelectedFiles().map(file => (
            <div key={file.id} style={{ fontSize: 12, color: '#333' }}>
              {file.type === 'folder' ? <FolderOutlined /> : <FileOutlined />} {file.name}
            </div>
          ))}
        </div>
      )} */}
    </Modal>
  );
};

export default FilePicker;
export type { FilePickerProps, FileItem };
