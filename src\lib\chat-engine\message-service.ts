'use client';

import { 
  MessageService, 
  ChatMessage, 
  EventBus
} from './types';
import { 
  conversationApi
} from '@/services/apiManager';

/**
 * 消息服务实现
 * 负责消息的发送、接收和管理，完全独立于UI
 * 发送消息通过HTTP API，接收响应通过AGUI通道
 */
export class MessageServiceImpl implements MessageService {
  private eventBus: EventBus;
  private agentId: string;
  private enableLogging = false;

  constructor(
    eventBus: EventBus, 
    agentId: string,
    options?: { enableLogging?: boolean }
  ) {
    this.eventBus = eventBus;
    this.agentId = agentId;
    this.enableLogging = options?.enableLogging ?? false;
  }

  /**
   * 发送消息
   * 通过HTTP API发送消息，AGUI通道只用于接收响应
   */
  async sendMessage(conversationId: string, content: string, tools?: any[]): Promise<void> {
    if (!conversationId || !content.trim()) {
      throw new Error('会话ID和消息内容不能为空');
    }

    if (this.enableLogging) {
      console.log(`[MessageService] Sending message to conversation ${conversationId}:`, {
        content: content.substring(0, 100) + (content.length > 100 ? '...' : ''),
        toolsCount: tools?.length || 0
      });
    }

    try {
      // 发射消息发送开始事件
      this.eventBus.emit({
        type: 'MESSAGE_SENDING',
        payload: {
          conversationId,
          content: content.trim(),
          tools: tools || []
        },
        timestamp: Date.now(),
        source: 'MessageService'
      });

      // 通过HTTP API发送消息（保持原有方式）
      await conversationApi.sendMessage(conversationId, content.trim(), this.agentId);

      // 发射消息发送成功事件
      this.eventBus.emit({
        type: 'MESSAGE_SENT',
        payload: {
          message: {
            id: `user_${Date.now()}`,
            conversationId,
            role: 'user' as const,
            content: content.trim(),
            timestamp: Date.now(),
            status: 'sent' as const
          }
        },
        timestamp: Date.now(),
        source: 'MessageService'
      });

      if (this.enableLogging) {
        console.log(`[MessageService] Message sent successfully via HTTP API`);
      }

    } catch (error) {
      if (this.enableLogging) {
        console.error(`[MessageService] Failed to send message:`, error);
      }

      // 发射错误事件
      this.eventBus.emit({
        type: 'ERROR',
        payload: {
          error: error instanceof Error ? error.message : '发送消息失败',
          code: 'SEND_MESSAGE_FAILED',
          conversationId
        },
        timestamp: Date.now(),
        source: 'MessageService'
      });

      throw error;
    }
  }

  /**
   * 加载消息历史
   */
  async loadMessages(conversationId: string): Promise<ChatMessage[]> {
    console.log('📂 [MessageService] loadMessages被调用', {
      conversationId,
      stackTrace: new Error().stack?.split('\n').slice(1, 5).join('\n')
    });
    
    if (!conversationId) {
      throw new Error('会话ID不能为空');
    }

    if (this.enableLogging) {
      console.log(`[MessageService] Loading messages for conversation ${conversationId}`);
    }

    try {
      // 发射消息加载开始事件
      this.eventBus.emit({
        type: 'MESSAGES_LOADING',
        payload: {
          conversationId
        },
        timestamp: Date.now(),
        source: 'MessageService'
      });

      // 调用API获取消息
      const apiMessages = await conversationApi.getConversationMessages(conversationId);
      
      // 转换为内部消息格式
      const messages: ChatMessage[] = apiMessages.map((msg: any) => ({
        id: msg.message_id,
        conversationId: msg.conversation_id,
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
        timestamp: new Date(msg.created_at).getTime(),
        isStreaming: false,
        status: 'delivered' as const
      }));

      // 发射消息加载成功事件
      console.log('📤 [MessageService] 发射MESSAGES_LOADED事件', {
        conversationId,
        messagesCount: messages.length,
        stackTrace: new Error().stack?.split('\n').slice(1, 5).join('\n')
      });
      
      this.eventBus.emit({
        type: 'MESSAGES_LOADED',
        payload: {
          conversationId,
          messages
        },
        timestamp: Date.now(),
        source: 'MessageService'
      });

      if (this.enableLogging) {
        console.log(`[MessageService] Loaded ${messages.length} messages`);
      }

      return messages;

    } catch (error) {
      if (this.enableLogging) {
        console.error(`[MessageService] Failed to load messages:`, error);
      }

      // 发射错误事件
      this.eventBus.emit({
        type: 'ERROR',
        payload: {
          error: error instanceof Error ? error.message : '加载消息失败',
          code: 'LOAD_MESSAGES_FAILED',
          conversationId
        },
        timestamp: Date.now(),
        source: 'MessageService'
      });

      throw error;
    }
  }

  /**
   * 重试发送消息
   */
  async retryMessage(messageId: string): Promise<void> {
    if (!messageId) {
      throw new Error('消息ID不能为空');
    }

    if (this.enableLogging) {
      console.log(`[MessageService] Retrying message ${messageId}`);
    }

    try {
      // 发射重试开始事件
      this.eventBus.emit({
        type: 'MESSAGE_RETRY_START',
        payload: {
          messageId
        },
        timestamp: Date.now(),
        source: 'MessageService'
      });

      // 这里可以根据需要实现重试逻辑
      // 目前先发射重试成功事件
      this.eventBus.emit({
        type: 'MESSAGE_RETRY_SUCCESS',
        payload: {
          messageId
        },
        timestamp: Date.now(),
        source: 'MessageService'
      });

      if (this.enableLogging) {
        console.log(`[MessageService] Message retry successful`);
      }

    } catch (error) {
      if (this.enableLogging) {
        console.error(`[MessageService] Failed to retry message:`, error);
      }

      // 发射重试失败事件
      this.eventBus.emit({
        type: 'ERROR',
        payload: {
          error: error instanceof Error ? error.message : '重试消息失败',
          code: 'RETRY_MESSAGE_FAILED',
          messageId
        },
        timestamp: Date.now(),
        source: 'MessageService'
      });

      throw error;
    }
  }

  /**
   * 处理流式消息
   */
  handleStreamingMessage(messageId: string, content: string, isComplete: boolean): void {
    if (this.enableLogging) {
      console.log(`[MessageService] Handling streaming message ${messageId}:`, {
        content: content.substring(0, 50) + '...',
        isComplete
      });
    }

    if (isComplete) {
      // 流式消息完成
      this.eventBus.emit({
        type: 'STREAMING_END',
        payload: {
          messageId,
          finalContent: content
        },
        timestamp: Date.now(),
        source: 'MessageService'
      });
    } else {
      // 流式消息内容更新
      this.eventBus.emit({
        type: 'STREAMING_CONTENT',
        payload: {
          messageId,
          content,
          delta: '' // 这里可以计算增量内容
        },
        timestamp: Date.now(),
        source: 'MessageService'
      });
    }
  }

  /**
   * 开始流式消息
   */
  startStreamingMessage(messageId: string, conversationId: string): void {
    if (this.enableLogging) {
      console.log(`[MessageService] Starting streaming message ${messageId}`);
    }

    this.eventBus.emit({
      type: 'STREAMING_START',
      payload: {
        messageId,
        conversationId
      },
      timestamp: Date.now(),
      source: 'MessageService'
    });
  }

  /**
   * 更新Agent ID
   */
  updateAgentId(agentId: string): void {
    this.agentId = agentId;
    
    if (this.enableLogging) {
      console.log(`[MessageService] Updated agent ID to: ${agentId}`);
    }
  }

  /**
   * 启用/禁用日志
   */
  setLogging(enabled: boolean): void {
    this.enableLogging = enabled;
  }

  /**
   * 获取调试信息
   */
  getDebugInfo(): {
    agentId: string;
    enableLogging: boolean;
  } {
    return {
      agentId: this.agentId,
      enableLogging: this.enableLogging
    };
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    if (this.enableLogging) {
      console.log('[MessageService] Destroyed');
    }
  }
}