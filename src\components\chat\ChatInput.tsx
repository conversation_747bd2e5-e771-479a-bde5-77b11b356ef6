'use client';

import React, { useState, useCallback, useRef } from 'react';
import { useChatInput } from '@/store/chatStore';

/**
 * 完全解耦的聊天输入组件
 * 只负责UI渲染和基本交互，通过事件总线与其他组件通信
 */
export default function ChatInput() {
  // 本地UI状态（完全独立）
  const [localValue, setLocalValue] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 聊天引擎状态（只读）
  const { canSend, isSending, onChange, onSend } = useChatInput();

  // 自动调整textarea高度
  const autoResize = useCallback((textarea: HTMLTextAreaElement) => {
    // 临时重置高度以获取准确的scrollHeight
    textarea.style.height = 'auto';
    // 根据scrollHeight设置实际需要的高度
    const newHeight = Math.min(Math.max(textarea.scrollHeight, 24), 120); // 最小24px，最大120px（5行）
    textarea.style.height = `${newHeight}px`;
  }, []);

  // 处理输入变化
  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setLocalValue(value);
    
    // 自动调整textarea高度
    autoResize(e.target);
    
    // 通知聊天引擎用户正在输入
    onChange(value);
  }, [onChange, autoResize]);

  // 处理发送
  const handleSend = useCallback(() => {
    const canSendNow = canSend(localValue);
    if (!canSendNow || !localValue.trim()) {
      console.log('Cannot send:', { canSendNow, hasContent: !!localValue.trim() });
      return;
    }
    
    console.log('Sending message:', localValue.trim());
    
    // 发送消息
    onSend(localValue.trim());
    
    // 立即清空本地输入框
    setLocalValue('');
    
    // 重置textarea高度
    if (textareaRef.current) {
      textareaRef.current.style.height = '24px';
    }
    
    // 重置焦点
    textareaRef.current?.focus();
  }, [canSend, localValue, onSend]);

  // 处理键盘事件 (使用onKeyDown替代已弃用的onKeyPress)
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  }, [handleSend]);

  // 计算当前是否可以发送
  const canSendNow = canSend(localValue);

  return (
    <div className="max-w-[960px] mx-auto">
      <div className="bg-bg-base rounded-xl shadow-sm">
        <div className="flex flex-row items-center p-3">
          {/* 输入框区域 */}

            <textarea
              ref={textareaRef}
              value={localValue}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              placeholder="输入消息... (Enter发送，Shift+Enter换行)"
              className="flex-1 w-full text-base leading-6 text-text-primary placeholder-text-muted bg-transparent border-none outline-none resize-none transition-all duration-150 ease-out"
              rows={1}
              disabled={isSending}
              style={{ 
                minHeight: '24px', 
                lineHeight: '24px',
                maxHeight: '120px'
              }}
            />

          
          {/* 操作按钮 */}
          <div className="flex items-center justify-between ml-3 ">
            {/* 右侧发送按钮 */}
            <button
              onClick={handleSend}
              disabled={!canSendNow || !localValue.trim() || isSending}
              className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${
                canSendNow && localValue.trim() && !isSending
                  ? 'bg-layer-4 hover:bg-layer-4 text-text-primary'
                  : 'bg-layer-2 text-text-muted cursor-not-allowed'
              }`}
            >
              <img src={`/assets/history/${isSending ? 'sending' : 'send'}.svg`} alt="sending" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}