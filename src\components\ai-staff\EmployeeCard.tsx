import { Employee } from "@/types/employee";
import { formatDateTime } from "@/utils/data-transform";
import React, { useState } from "react";
import EmployeeContextMenu from "./EmployeeContextMenu";

// 这是一个简化的类型，实际情况可能更复杂
interface EmployeeCardProps {
  employee: Employee;
  onStart?: () => void;
  onEdit?: (employee: Employee) => void;
  onDelete?: (employee: Employee) => void;
  onToggleStatus?: (employee: Employee) => void;
  isEditing?: boolean;
  isDeleting?: boolean;
  /**
   * 自定义footer右侧内容（如知识库选择弹窗中的添加按钮）
   */
  footerRight?: React.ReactNode;
  /**
   * 自定义卡片最外层className（如弹窗内自定义悬浮样式）
   */
  className?: string;
  /**
   * 是否隐藏未悬浮时右下角圆点（如知识库选择弹窗）
   */
  hideStatusDot?: boolean;
  /**
   * 极简模式：只显示名字、描述和右上角按钮
   */
  minimal?: boolean;
}

const tagColorMap: { [key: string]: string } = {
  公开: "bg-[#4285F429] text-[#4285F4]",
  个人: "bg-[#34A85329] text-#34A853",
  组: "bg-[#FF790029 text-[#FF7900]",
};

export default function EmployeeCard({
  employee,
  onStart,
  onEdit,
  onDelete,
  onToggleStatus,
  isEditing,
  isDeleting,
  footerRight,
  className,
  hideStatusDot,
  minimal,
}: EmployeeCardProps) {
  // 添加上下文菜单状态
  const [menuVisible, setMenuVisible] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });

  // 处理more图标点击事件
  const handleMoreClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    const rect = e.currentTarget.getBoundingClientRect();
    setMenuPosition({ x: rect.right, y: rect.top });
    setMenuVisible(true);
  };

  // 关闭菜单
  const handleCloseMenu = () => {
    setMenuVisible(false);
  };

  // 处理编辑按钮点击
  const handleEdit = () => {
    if (onEdit) {
      onEdit(employee);
    }
    setMenuVisible(false);
  };

  // 处理删除按钮点击
  const handleDelete = () => {
    if (onDelete) {
      onDelete(employee);
    }
    setMenuVisible(false);
  };

  // 处理启用/禁用按钮点击
  const handleToggleStatus = () => {
    if (onToggleStatus) {
      onToggleStatus(employee);
    }
    setMenuVisible(false);
  };

  if (minimal) {
    return (
      <div
        className={`group bg-white rounded-lg border border-[#0000000F] hover:shadow-lg transition-all duration-200 flex flex-col cursor-pointer p-3 relative ${
          className ?? ""
        }`}
        onClick={onStart}
        style={{ minHeight: 150 }}
      > 
        <div className="flex justify-between items-start mb-2">
          <span className="font-medium text-[#000000E0] text-[16px]">
            {employee.name}
          </span>
          <img src="/assets/ai-employees/agent.svg" alt="agent" />
        </div>
        <span className="text-[#000000B8] text-[13px] font-normal line-clamp-2">
          {employee.description}
        </span>
        <div className="absolute bottom-3 right-3">{footerRight}</div>
      </div>
    );
  }

  return (
    <div
      className={`min-h-[150px] group bg-white rounded-lg border border-[#0000000F] hover:shadow-lg transition-all duration-200 flex flex-col cursor-pointer p-3 ${
        className ?? ""
      }`}
      onClick={onStart}
    >
      <div className="flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-start mb-3">
          <span className="font-medium text-[#000000E0] text-[16px]">
            {employee.name}
          </span>
          <img src="/assets/ai-employees/AI.svg" alt="AI" />
        </div>
        {/* Description */}
        <span className="text-[#000000B8] text-[13px] font-normal line-clamp-2">
          {employee.description}
        </span>
        {/* Tags */}
        <div className="mt-[6px] flex flex-wrap gap-[6px]">
          {employee.tags?.map((tag) => (
            <div
              key={tag}
              className={`px-[6px] py-[2px] text-[12px] font-nromal rounded-[4px] ${
                tagColorMap[tag]
                  ? `border border-transparent ${tagColorMap[tag]}`
                  : "border border-[#0000000F] text-[#000000B8] bg-white"
              }`}
            >
              {tag}
            </div>
          ))}
        </div>
      </div>

      {/* Footer */}
      <div>
        {/* Default Footer（主列表） */}
        <div className="group-hover:hidden flex justify-between items-end">
          <div className="text-[12px] text-[#00000066] font-normal flex items-center">
            {employee.name} <span className="mx-1">|</span>{" "}
            {!employee.disabled ? "启用" : "未启用"}
          </div>
          {hideStatusDot ? (
            <div className="w-[37px] h-[37px]" />
          ) : (
            <div className="w-[32px] h-[32px] flex items-center justify-center">
              <div
                className={`w-[8px] h-[8px] rounded-[3px] border border-[#00000014] ${
                  !employee.disabled ? "bg-[#34A853]" : "bg-[#E74639]"
                }`}
              ></div>
            </div>
          )}
        </div>
        {/* Hover Footer（自定义右侧） */}
        <div className="hidden group-hover:flex justify-between items-end">
          <span className="text-[12px] text-[#00000066] font-normal">
            创建于{formatDateTime(employee.createdTime)}
          </span>
          {footerRight === null ? (
            <div className="w-[37px] h-[37px]" />
          ) : typeof footerRight !== "undefined" ? (
            footerRight
          ) : (
            <img
              src="/assets/ai-employees/more.svg"
              alt="more"
              className="hover:bg-[#0000000A] cursor-pointer rounded-[8px]"
              onClick={handleMoreClick}
            />
          )}
        </div>
      </div>

      {/* 上下文菜单 */}
      <EmployeeContextMenu
        isVisible={menuVisible}
        position={menuPosition}
        onClose={handleCloseMenu}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onToggleStatus={handleToggleStatus}
        isEditing={isEditing}
        isDeleting={isDeleting}
        isDisabled={employee.disabled}
      />
    </div>
  );
}
