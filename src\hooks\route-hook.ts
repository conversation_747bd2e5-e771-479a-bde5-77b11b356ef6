import {
  <PERSON>Rout<PERSON>Key,
  KnowledgeSearchParams,
} from '@/constants/knowledge';
import { useCallback, useMemo } from 'react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';

export enum SegmentIndex {
  Second = '2',
  Third = '3',
}

// Next.js 的 useSearchParams 实现
const useSearchParamsHook = () => {
  const searchParams = useSearchParams();
  
  const setSearchParams = useCallback((params: URLSearchParams) => {
    // Next.js 中需要手动构建 URL 并导航
    console.warn('setSearchParams 在 Next.js 中需要手动构建 URL');
  }, []);

  return [searchParams, setSearchParams] as const;
};

// Next.js 的 useNavigate 实现
const useNavigate = () => {
  const router = useRouter();
  
  return useCallback((path: string, options?: { state?: any }) => {
    router.push(path);
  }, [router]);
};

export const useSegmentedPathName = (index: SegmentIndex) => {
  const pathname = usePathname();

  const pathArray = pathname.split('/');
  return pathArray[index] || '';
};

export const useSecondPathName = () => {
  return useSegmentedPathName(SegmentIndex.Second);
};

export const useThirdPathName = () => {
  return useSegmentedPathName(SegmentIndex.Third);
};

export const useGetKnowledgeSearchParams = () => {
  const currentQueryParameters = useSearchParams();

  return {
    documentId:
      currentQueryParameters.get(KnowledgeSearchParams.DocumentId) || '',
    knowledgeId:
      currentQueryParameters.get(KnowledgeSearchParams.KnowledgeId) || '',
  };
};

export const useNavigateWithFromState = () => {
  const navigate = useNavigate();
  return useCallback(
    (path: string) => {
      navigate(path, { state: { from: path } });
    },
    [navigate],
  );
};

export const useNavigateToDataset = () => {
  const navigate = useNavigate();
  const { knowledgeId } = useGetKnowledgeSearchParams();

  return useCallback(() => {
    navigate(`/knowledge/${KnowledgeRouteKey.Dataset}?id=${knowledgeId}`);
  }, [knowledgeId, navigate]);
};

export const useGetPaginationParams = () => {
  const currentQueryParameters = useSearchParams();

  return {
    page: currentQueryParameters.get('page') || 1,
    size: currentQueryParameters.get('size') || 10,
  };
};

export const useSetPaginationParams = () => {
  const queryParameters = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const setPaginationParams = useCallback(
    (page: number = 1, pageSize?: number) => {
      const newParams = new URLSearchParams(queryParameters.toString());
      newParams.set('page', page.toString());
      if (pageSize) {
        newParams.set('size', pageSize.toString());
      }
      router.push(`${pathname}?${newParams.toString()}`);
    },
    [router, pathname, queryParameters],
  );

  return {
    setPaginationParams,
    page: Number(queryParameters.get('page')) || 1,
    size: Number(queryParameters.get('size')) || 10,
  };
};
