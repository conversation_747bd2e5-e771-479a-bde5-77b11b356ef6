import { useTranslate } from '@/hooks/common-hooks';
import { useGetFolderId } from '@/hooks/file-manager-hooks';
import { Form, Input, Modal, Button } from 'antd';
import { useEffect, useState } from 'react';
import { IModalManagerChildrenProps } from '@/components/file-manager/common/modal-manager';
// import FileIcon from '@/assets/knowledge/FileIcon.svg';
// import IconCloseTag from '@/assets/close.svg';
import { Space, Typography } from 'antd';
const { Text } = Typography;

interface IProps extends Omit<IModalManagerChildrenProps, 'showModal'> {
  loading: boolean;
  initialName?: string;
  initialDescription?: string;
  initialTags?: string[];
  onOk: (params: { new_name: string; description?: string; tags?: string[] }) => void;
  showModal?(): void;
}

const RenameModal = ({
  visible,
  hideModal,
  loading,
  initialName = '',
  initialDescription = '',
  initialTags = [],
  onOk,
}: IProps) => {
  const [form] = Form.useForm();
  const { t } = useTranslate('common');
  const [formValues, setFormValues] = useState<{ new_name?: string; description?: string; tags?: string[] }>({});
  const [tags, setTags] = useState<string[]>(initialTags);
  const currentFolderId = useGetFolderId();
  const isRootDirectory = !currentFolderId;

  type FieldType = {
    new_name?: string;
    description?: string;
    tags?: string[];
  };

  const handleOk = async () => {
    const ret = await form.validateFields();
    return onOk({
      new_name: ret.new_name,
      description: isRootDirectory ? ret.description : '',
      tags: isRootDirectory ? tags.filter(tag => tag.trim() !== '') : [],
    });
  };

  // 监听表单值变化
  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    setFormValues(allValues);
  };

  // 处理Modal关闭
  const handleCancel = () => {
    form.resetFields();
    setFormValues({});
    hideModal();
  };

  // 判断确认按钮是否应该禁用
  const isConfirmDisabled = !formValues.new_name?.trim() || loading;

  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        new_name: initialName,
        description: initialDescription,
      });
      setFormValues({
        new_name: initialName,
        description: initialDescription,
      });
      setTags(initialTags);
    }
  }, [initialName, initialDescription, initialTags, form, visible]);

  const showTagInput = () => {
    if (tags.length >= 3) {
      return;
    }
    setTags([...tags, '']);
  };
  const handleTagChange = (newTags: string[]) => {
    setTags(newTags);
  };

  // 自定义footer
  const customFooter = (
    <div className="flex justify-end gap-3 mt-6">
      <Button
        style={{
          height: 40,
          padding: '8px 24px',
          borderRadius: 8,
          fontSize: 14,
          fontWeight: 500,
          transition: 'all 0.2s ease-in-out',
          boxShadow: 'none',
          border: '1px solid #e8e8e8',
          backgroundColor: '#ffffff',
          color: '#666666',
        }}
        onMouseOver={e => {
          e.currentTarget.style.borderColor = '#d9d9d9';
          e.currentTarget.style.backgroundColor = '#fafafa';
          e.currentTarget.style.color = '#333333';
        }}
        onMouseOut={e => {
          e.currentTarget.style.borderColor = '#e8e8e8';
          e.currentTarget.style.backgroundColor = '#ffffff';
          e.currentTarget.style.color = '#666666';
        }}
        onFocus={e => {
          e.currentTarget.style.borderColor = '#d9d9d9';
          e.currentTarget.style.backgroundColor = '#fafafa';
          e.currentTarget.style.color = '#333333';
        }}
        onBlur={e => {
          e.currentTarget.style.borderColor = '#e8e8e8';
          e.currentTarget.style.backgroundColor = '#ffffff';
          e.currentTarget.style.color = '#666666';
        }}
        onClick={handleCancel}
      >
        取消
      </Button>
      <Button
        type="primary"
        style={{
          height: 40,
          padding: '8px 24px',
          borderRadius: 8,
          fontSize: 14,
          fontWeight: 500,
          transition: 'all 0.2s ease-in-out',
          boxShadow: 'none',
          border: 'none',
          backgroundColor: loading || isConfirmDisabled ? '#c3c8fa' : '#1f1f1f',
          color: '#ffffff',
          cursor: loading || isConfirmDisabled ? 'not-allowed' : undefined,
        }}
        onMouseOver={e => {
          const btn = e.currentTarget as HTMLButtonElement;
          if (!btn.disabled) {
            btn.style.backgroundColor = '#1f1f1f';
            btn.style.color = '#ffffff';
          }
        }}
        onMouseOut={e => {
          const btn = e.currentTarget as HTMLButtonElement;
          if (!btn.disabled) {
            btn.style.backgroundColor = '#1f1f1f';
            btn.style.color = '#ffffff';
          }
        }}
        onFocus={e => {
          const btn = e.currentTarget as HTMLButtonElement;
          if (!btn.disabled) {
            btn.style.backgroundColor = '#1f1f1f';
            btn.style.color = '#ffffff';
          }
        }}
        onBlur={e => {
          const btn = e.currentTarget as HTMLButtonElement;
          if (!btn.disabled) {
            btn.style.backgroundColor = '#1f1f1f';
            btn.style.color = '#ffffff';
          }
        }}
        loading={loading}
        disabled={isConfirmDisabled}
        onClick={handleOk}
      >
        保存
      </Button>
    </div>
  );

  return (
    <Modal
      title={<span style={{ fontSize: 18, fontWeight: 700, color: '#1f1f1f' }}>编辑</span>}
      open={visible}
      onCancel={handleCancel}
      footer={customFooter}
      styles={{ content: { borderRadius: 12 } }}
    >
      <div className="flex flex-col gap-6">
        <Form
          name="fileEditForm"
          layout="vertical"
          autoComplete="off"
          form={form}
          onValuesChange={handleFormValuesChange}
        >
          <div className="flex items-start gap-6">
            <div className="flex-1">
              <Form.Item<FieldType>
                label={
                  <span className="relative inline-block" style={{color: '#474747',fontSize: '14px',fontWeight: '600'}}>
                    名称
                    <span className="text-[#1f1f1f] absolute top-0.5 -right-3 text-base leading-none">*</span>
                  </span>
                }
                name="new_name"
                required={false}
                rules={[{ required: true, message: t('namePlaceholder') }]}
              >
                <Input 
                  placeholder="请输入"
                  className="rounded-lg border border-gray-200 px-3 py-2 text-sm focus:border-blue-600 focus:shadow-none hover:border-gray-200"
                  style={{ border: '1px solid #ebebeb', backgroundColor: '#fafafa', borderRadius: 8, padding: '8px 12px' }}
                />
              </Form.Item>
            </div>
          </div>
          
          {isRootDirectory && (
            <div>
              <Form.Item<FieldType>
                label={
                  <span className="relative inline-block" style={{color: '#474747',fontSize: '14px',fontWeight: '600'}}>
                    描述
                  </span>
                }
                name="description"
                required={false}
              >
                <Input.TextArea
                  placeholder="请输入"
                  rows={4}
                  maxLength={100}
                  className="rounded-lg border border-gray-200 px-3 py-2 text-sm min-h-22 resize-none focus:border-blue-600 focus:shadow-none hover:border-gray-200"
                  style={{border: '1px solid #ebebeb',backgroundColor: '#fafafa',borderRadius: '8px',padding: '8px 12px' }}
                />
              </Form.Item>
              
              <Form.Item<FieldType>>
                <div className="flex justify-between items-center mb-2">
                  <Space direction="vertical" size={4}>
                    <Text className="text-gray-700 font-semibold text-sm">标签</Text>
                    <Text className="font-normal text-xs text-gray-400">标签至多添加3个</Text>
                  </Space>
                  <Button
                    className="h-10 px-6 bg-white border border-gray-200 rounded-lg flex items-center justify-center font-medium text-sm text-gray-700 hover:bg-gray-50 hover:border-gray-200 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors duration-200"
                    onClick={showTagInput}
                    disabled={tags.length >= 3}
                  >
                    添加
                  </Button>
                </div>
                {tags.length > 0 && (
                  <div className="flex flex-col gap-2 p-2 bg-gray-50 border border-gray-100 rounded-lg">
                    {tags.map((tag, index) => (
                      <div key={`tag-${index}`} className="w-full">
                        <Input
                          autoFocus={tag === ''}
                          value={tag}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (value && value.length > 20) {
                              return;
                            }
                            const newTags = [...tags];
                            newTags[index] = value;
                            handleTagChange(newTags);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              e.stopPropagation();
                            }
                          }}
                          placeholder={`标签 ${index + 1}`}
                          className="border-none rounded-lg bg-white px-3 py-2 text-sm focus:shadow-none hover:bg-white"
                          suffix={
                            <div
                              className="cursor-pointer w-6 h-6 flex items-center justify-center"
                              onClick={() => {
                                const newTags = [...tags];
                                newTags.splice(index, 1);
                                handleTagChange(newTags);
                              }}
                            />
                          }
                        />
                      </div>
                    ))}
                  </div>
                )}
              </Form.Item>
            </div>
          )}
        </Form>
      </div>
    </Modal>
  );
};

export default RenameModal;

