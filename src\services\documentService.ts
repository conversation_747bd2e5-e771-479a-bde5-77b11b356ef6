/**
 * 文档管理API服务
 * 纯函数式API调用，配合ahooks的useRequest使用
 * 处理文档上传、解析、知识块管理等功能
 */

import { apiClient } from '@/lib/http/errorInterceptor';
import { documentEndpoints } from '@/services/apiEndpoints';
import {
  DocumentListParams,
  DocumentListResponse,
  UploadFileParams,
  DocumentUploadResponse,
  DocumentDeleteResponse,
  DocumentProcessResponse,
  ChunkListParams,
  ChunkListResponse,
  ChunkUpdateParams,
  ChunkUpdateResponse,
  ChunkDeleteResponse,
  ChunkBatchOperationParams,
  ChunkBatchOperationResponse
} from '@/types/knowledge-api';
import {
  IDocument,
  IChunk,
  RunningStatus
} from '@/types/knowledge';

// ==================== 文档管理 ====================

/**
 * 获取文档列表
 */
export async function getDocumentList(
  knowledgeId: string,
  params: DocumentListParams = {}
): Promise<DocumentListResponse> {
  const response = await apiClient.post<DocumentListResponse>(
    documentEndpoints.list,
    {
      run_status: params.status ? [params.status] : undefined,
      types: params.type ? [params.type] : undefined
    },
    {
      params: {
        kb_id: knowledgeId,
        keywords: params.keywords,
        page: params.page || 1,
        page_size: params.pageSize || 20,
        orderby: params.sortBy || 'create_time',
        desc: params.sortOrder === 'desc' ? 'true' : 'false'
      }
    }
  );
  
  return response.data;
}

/**
 * 上传文档到知识库
 */
export async function uploadDocuments(
  knowledgeId: string,
  params: UploadFileParams
): Promise<DocumentUploadResponse> {
  const formData = new FormData();
  
  // 根据API文档，一次只能上传一个文件
  const file = params.files[0];
  if (!file) {
    throw new Error('没有选择要上传的文件');
  }
  
  formData.append('file', file);
  formData.append('kb_id', knowledgeId);
  formData.append('chunk_size', '1024');
  formData.append('chunk_overlap', '50');
  
  if (params.parser_id) {
    formData.append('parser_id', params.parser_id);
  }
  
  const response = await apiClient.post<DocumentUploadResponse>(
    documentEndpoints.uploadAndParse, // 使用上传并解析的端点
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      // 支持上传进度回调
      onUploadProgress: (progressEvent) => {
        if (params.onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          params.onProgress(progress);
        }
      }
    }
  );
  
  return response.data;
}

/**
 * 删除文档
 */
export async function deleteDocument(
  knowledgeId: string,
  documentId: string
): Promise<DocumentDeleteResponse> {
  const response = await apiClient.delete<DocumentDeleteResponse>(
    documentEndpoints.delete.replace(':kbId', knowledgeId).replace(':docId', documentId)
  );
  
  return response.data;
}

/**
 * 重新解析文档
 */
export async function reprocessDocument(
  knowledgeId: string,
  documentId: string,
  parserConfig?: any
): Promise<DocumentProcessResponse> {
  const response = await apiClient.post<DocumentProcessResponse>(
    documentEndpoints.reprocess.replace(':kbId', knowledgeId).replace(':docId', documentId),
    {
      parser_config: parserConfig
    }
  );
  
  return response.data;
}

/**
 * 停止文档解析
 */
export async function stopDocumentProcess(
  knowledgeId: string,
  documentId: string
): Promise<{ success: boolean }> {
  const response = await apiClient.post<{ success: boolean }>(
    documentEndpoints.stopProcess.replace(':kbId', knowledgeId).replace(':docId', documentId)
  );
  
  return response.data;
}

// ==================== 知识块管理 ====================

/**
 * 获取知识块列表
 */
export async function getChunkList(
  knowledgeId: string,
  params: ChunkListParams = {}
): Promise<ChunkListResponse> {
  const response = await apiClient.get<ChunkListResponse>(
    documentEndpoints.chunks.replace(':kbId', knowledgeId),
    {
      params: {
        doc_id: params.doc_id,
        page: params.page || 1,
        page_size: params.pageSize || 20,
        keywords: params.keywords,
        available: params.available,
        sort_by: params.sortBy || 'create_time',
        sort_order: params.sortOrder || 'desc'
      }
    }
  );
  
  return response.data;
}

/**
 * 更新知识块
 */
export async function updateChunk(
  knowledgeId: string,
  chunkId: string,
  params: ChunkUpdateParams
): Promise<ChunkUpdateResponse> {
  const response = await apiClient.put<ChunkUpdateResponse>(
    documentEndpoints.updateChunk.replace(':kbId', knowledgeId).replace(':chunkId', chunkId),
    {
      content_with_weight: params.content,
      available_int: params.available ? 1 : 0,
      important_kwd: params.keywords,
      question_kwd: params.questions
    }
  );
  
  return response.data;
}

/**
 * 删除知识块
 */
export async function deleteChunk(
  knowledgeId: string,
  chunkId: string
): Promise<ChunkDeleteResponse> {
  const response = await apiClient.delete<ChunkDeleteResponse>(
    documentEndpoints.deleteChunk.replace(':kbId', knowledgeId).replace(':chunkId', chunkId)
  );
  
  return response.data;
}

/**
 * 批量操作知识块
 */
export async function batchOperateChunks(
  knowledgeId: string,
  params: ChunkBatchOperationParams
): Promise<ChunkBatchOperationResponse> {
  const response = await apiClient.post<ChunkBatchOperationResponse>(
    documentEndpoints.batchChunks.replace(':kbId', knowledgeId),
    {
      action: params.action,
      chunk_ids: params.chunk_ids,
      reason: params.reason
    }
  );
  
  return response.data;
}

// ==================== Mock数据（开发阶段） ====================

/**
 * 生成Mock文档数据
 */
export function createMockDocument(override: Partial<IDocument> = {}): IDocument {
  return {
    id: `doc_${Date.now()}`,
    kb_id: 'kb_default',
    name: 'Mock文档.pdf',
    location: '/uploads/mock-document.pdf',
    size: 1048576,
    type: 'pdf',
    source_type: 'upload',
    thumbnail: undefined,
    progress: 100,
    progress_msg: '解析完成',
    run: RunningStatus.DONE,
    status: 'enabled',
    chunk_num: 25,
    token_num: 5000,
    parser_config: {
      chunk_token_num: 512,
      delimiter: '\\n',
      auto_keywords: 1,
      auto_questions: 1
    },
    parser_id: 'default',
    process_begin_at: new Date().toISOString(),
    process_duation: 30,
    create_date: new Date().toISOString(),
    create_time: Date.now(),
    update_date: new Date().toISOString(),
    update_time: Date.now(),
    created_by: 'current-user',
    ...override
  };
}

/**
 * 生成Mock知识块数据
 */
export function createMockChunk(override: Partial<IChunk> = {}): IChunk {
  return {
    chunk_id: `chunk_${Date.now()}`,
    doc_id: 'doc_default',
    doc_name: 'Mock文档.pdf',
    content_with_weight: '这是一个模拟的知识块内容，包含了重要的信息...',
    available_int: 1,
    image_id: undefined,
    positions: [[100, 200, 300, 400]],
    important_kwd: ['重要', '关键词'],
    question_kwd: ['什么是', '如何'],
    tag_kwd: ['标签1', '标签2'],
    tag_feas: {
      'feature1': 0.8,
      'feature2': 0.6
    },
    ...override
  };
}

/**
 * 生成Mock文档列表
 */
export function createMockDocumentList(count: number = 10): IDocument[] {
  return Array.from({ length: count }, (_, index) => 
    createMockDocument({
      id: `doc_${index + 1}`,
      name: `文档${index + 1}.pdf`,
      type: index % 3 === 0 ? 'pdf' : index % 3 === 1 ? 'docx' : 'txt',
      chunk_num: Math.floor(Math.random() * 50) + 10,
      token_num: Math.floor(Math.random() * 10000) + 1000,
      size: Math.floor(Math.random() * 5000000) + 100000,
      run: index < 8 ? RunningStatus.DONE : 
           index === 8 ? RunningStatus.RUNNING : RunningStatus.FAIL
    })
  );
}

/**
 * 生成Mock知识块列表
 */
export function createMockChunkList(count: number = 20): IChunk[] {
  return Array.from({ length: count }, (_, index) =>
    createMockChunk({
      chunk_id: `chunk_${index + 1}`,
      doc_id: `doc_${Math.floor(index / 4) + 1}`,
      doc_name: `文档${Math.floor(index / 4) + 1}.pdf`,
      content_with_weight: `这是第${index + 1}个知识块的内容，包含了相关的信息和知识点...`,
      available_int: Math.random() > 0.2 ? 1 : 0,
      important_kwd: [`关键词${index + 1}`, `重要词${index + 1}`],
      question_kwd: [`问题${index + 1}`, `疑问${index + 1}`]
    })
  );
}