/**
 * 知识库枚举和常量定义
 * 定义知识库系统中使用的所有枚举值、常量和配置选项
 * 确保数据一致性和类型安全
 */

// ==================== 基础枚举 ====================

// 运行状态枚举
export enum RunningStatus {
  UNSTART = '0', // 未开始
  RUNNING = '1', // 运行中
  CANCEL = '2',  // 已取消
  DONE = '3',    // 已完成
  FAIL = '4'     // 失败
}

// 知识库权限枚举
export enum KnowledgePermission {
  PUBLIC = 'public',  // 公开访问
  PARTIAL = 'partial' // 部分成员访问
}

// 知识库状态枚举
export enum KnowledgeStatus {
  ACTIVE = 'active',      // 活跃
  INACTIVE = 'inactive',  // 未激活
  UPDATING = 'updating'   // 更新中
}

// 文档类型枚举
export enum DocumentType {
  PDF = 'pdf',
  DOC = 'doc',
  DOCX = 'docx',
  TXT = 'txt',
  MD = 'md',
  HTML = 'html',
  JSON = 'json'
}

// 任务类型枚举
export enum TaskType {
  UPLOAD = 'upload',     // 文档上传
  PROCESS = 'process',   // 文档处理
  EXPORT = 'export',     // 数据导出
  IMPORT = 'import',     // 数据导入
  DELETE = 'delete',     // 删除操作
  REINDEX = 'reindex'    // 重新索引
}

// 操作类型枚举
export enum OperationType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  UPLOAD = 'upload',
  DOWNLOAD = 'download',
  ENABLE = 'enable',
  DISABLE = 'disable',
  SHARE = 'share',
  UNSHARE = 'unshare'
}

// 排序字段枚举
export enum SortField {
  NAME = 'name',
  CREATE_TIME = 'create_time',
  UPDATE_TIME = 'update_time',
  DOC_NUM = 'doc_num',
  CHUNK_NUM = 'chunk_num',
  SIZE = 'size',
  PROGRESS = 'progress',
  SIMILARITY = 'similarity'
}

// 排序方向枚举
export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc'
}

// 导出格式枚举
export enum ExportFormat {
  JSON = 'json',
  CSV = 'csv',
  TXT = 'txt'
}

// 合并策略枚举
export enum MergeStrategy {
  APPEND = 'append',           // 追加
  REPLACE = 'replace',         // 替换
  SKIP_EXISTING = 'skip_existing' // 跳过已存在
}

// ==================== 状态映射和显示 ====================

// 运行状态显示映射
export const RunningStatusDisplay: Record<RunningStatus, string> = {
  [RunningStatus.UNSTART]: '未开始',
  [RunningStatus.RUNNING]: '运行中',
  [RunningStatus.CANCEL]: '已取消',
  [RunningStatus.DONE]: '已完成',
  [RunningStatus.FAIL]: '失败'
};

// 运行状态颜色映射
export const RunningStatusColor: Record<RunningStatus, string> = {
  [RunningStatus.UNSTART]: 'default',
  [RunningStatus.RUNNING]: 'processing',
  [RunningStatus.CANCEL]: 'warning',
  [RunningStatus.DONE]: 'success',
  [RunningStatus.FAIL]: 'error'
};

// 知识库状态显示映射
export const KnowledgeStatusDisplay: Record<KnowledgeStatus, string> = {
  [KnowledgeStatus.ACTIVE]: '活跃',
  [KnowledgeStatus.INACTIVE]: '未激活',
  [KnowledgeStatus.UPDATING]: '更新中'
};

// 知识库状态颜色映射
export const KnowledgeStatusColor: Record<KnowledgeStatus, string> = {
  [KnowledgeStatus.ACTIVE]: 'success',
  [KnowledgeStatus.INACTIVE]: 'default',
  [KnowledgeStatus.UPDATING]: 'processing'
};

// 权限显示映射
export const PermissionDisplay: Record<KnowledgePermission, string> = {
  [KnowledgePermission.PUBLIC]: '公开',
  [KnowledgePermission.PARTIAL]: '部分成员'
};

// 文档类型显示映射
export const DocumentTypeDisplay: Record<DocumentType, string> = {
  [DocumentType.PDF]: 'PDF文档',
  [DocumentType.DOC]: 'Word文档',
  [DocumentType.DOCX]: 'Word文档',
  [DocumentType.TXT]: '文本文件',
  [DocumentType.MD]: 'Markdown文档',
  [DocumentType.HTML]: 'HTML文档',
  [DocumentType.JSON]: 'JSON文件'
};

// 文档类型图标映射
export const DocumentTypeIcon: Record<DocumentType, string> = {
  [DocumentType.PDF]: 'file-pdf',
  [DocumentType.DOC]: 'file-word',
  [DocumentType.DOCX]: 'file-word',
  [DocumentType.TXT]: 'file-text',
  [DocumentType.MD]: 'file-markdown',
  [DocumentType.HTML]: 'file-code',
  [DocumentType.JSON]: 'file-code'
};

// ==================== 默认配置常量 ====================

// 默认分页配置
export const DEFAULT_PAGINATION = {
  PAGE: 1,
  PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100
} as const;

// 默认知识库配置
export const DEFAULT_KNOWLEDGE_CONFIG = {
  PERMISSION: KnowledgePermission.PUBLIC,
  STATUS: KnowledgeStatus.ACTIVE,
  SIMILARITY_THRESHOLD: 0.7,
  VECTOR_SIMILARITY_WEIGHT: 0.5,
  CHUNK_TOKEN_NUM: 1024,
  LAYOUT_RECOGNIZE: true,
  AUTO_KEYWORDS: 5,
  AUTO_QUESTIONS: 3
} as const;

// 默认检索配置
export const DEFAULT_RETRIEVAL_CONFIG = {
  SIMILARITY_THRESHOLD: 0.7,
  VECTOR_SIMILARITY_WEIGHT: 0.5,
  TOP_K: 10,
  RERANK: false
} as const;

// 文件上传配置
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  MAX_FILES: 10,
  ALLOWED_EXTENSIONS: ['.pdf', '.doc', '.docx', '.txt', '.md', '.html', '.json'],
  CHUNK_SIZE: 1024 * 1024 // 1MB chunk for upload
} as const;

// API配置常量
export const API_CONFIG = {
  TIMEOUT: 30000,        // 30秒超时
  RETRY_TIMES: 3,        // 重试3次
  CACHE_TTL: 5 * 60 * 1000, // 5分钟缓存
  UPLOAD_TIMEOUT: 10 * 60 * 1000 // 上传10分钟超时
} as const;

// ==================== 业务规则常量 ====================

// 字段长度限制
export const FIELD_LIMITS = {
  KNOWLEDGE_NAME_MAX: 50,
  KNOWLEDGE_DESCRIPTION_MAX: 200,
  DOCUMENT_NAME_MAX: 100,
  CHUNK_CONTENT_MAX: 8192,
  KEYWORD_MAX_COUNT: 20,
  QUESTION_MAX_COUNT: 10,
  TAG_MAX_COUNT: 15
} as const;

// 数值范围限制
export const VALUE_LIMITS = {
  CHUNK_TOKEN_MIN: 256,
  CHUNK_TOKEN_MAX: 8192,
  SIMILARITY_THRESHOLD_MIN: 0.1,
  SIMILARITY_THRESHOLD_MAX: 1.0,
  VECTOR_WEIGHT_MIN: 0.0,
  VECTOR_WEIGHT_MAX: 1.0,
  TOP_K_MIN: 1,
  TOP_K_MAX: 50
} as const;

// 缓存键前缀
export const CACHE_KEYS = {
  KNOWLEDGE_LIST: 'knowledge:list',
  KNOWLEDGE_DETAIL: 'knowledge:detail',
  DOCUMENT_LIST: 'document:list',
  DOCUMENT_DETAIL: 'document:detail',
  CHUNK_LIST: 'chunk:list',
  CHUNK_DETAIL: 'chunk:detail',
  RETRIEVAL_RESULT: 'retrieval:result',
  USER_PERMISSIONS: 'user:permissions'
} as const;

// WebSocket事件类型
export const WS_EVENT_TYPES = {
  TASK_UPDATE: 'task_update',
  PROCESSING_PROGRESS: 'processing_progress',
  ERROR: 'error',
  NOTIFICATION: 'notification',
  PROCESSING_COMPLETE: 'processing_complete'
} as const;

// ==================== 错误代码常量 ====================

// 业务错误代码
export const ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  INVALID_PARAMS: 'INVALID_PARAMS',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  
  // 知识库相关
  KNOWLEDGE_NOT_FOUND: 'KNOWLEDGE_NOT_FOUND',
  KNOWLEDGE_NAME_EXISTS: 'KNOWLEDGE_NAME_EXISTS',
  KNOWLEDGE_DELETE_FAILED: 'KNOWLEDGE_DELETE_FAILED',
  
  // 文档相关
  DOCUMENT_NOT_FOUND: 'DOCUMENT_NOT_FOUND',
  DOCUMENT_UPLOAD_FAILED: 'DOCUMENT_UPLOAD_FAILED',
  DOCUMENT_PROCESS_FAILED: 'DOCUMENT_PROCESS_FAILED',
  UNSUPPORTED_FILE_TYPE: 'UNSUPPORTED_FILE_TYPE',
  FILE_SIZE_EXCEEDED: 'FILE_SIZE_EXCEEDED',
  
  // 知识块相关
  CHUNK_NOT_FOUND: 'CHUNK_NOT_FOUND',
  CHUNK_UPDATE_FAILED: 'CHUNK_UPDATE_FAILED',
  CHUNK_DELETE_FAILED: 'CHUNK_DELETE_FAILED',
  
  // 检索相关
  RETRIEVAL_FAILED: 'RETRIEVAL_FAILED',
  INVALID_QUERY: 'INVALID_QUERY',
  
  // 权限相关
  INSUFFICIENT_PERMISSION: 'INSUFFICIENT_PERMISSION',
  PERMISSION_UPDATE_FAILED: 'PERMISSION_UPDATE_FAILED'
} as const;

// 错误信息映射
export const ERROR_MESSAGES: Record<string, string> = {
  [ERROR_CODES.UNKNOWN_ERROR]: '未知错误，请稍后重试',
  [ERROR_CODES.INVALID_PARAMS]: '参数无效',
  [ERROR_CODES.UNAUTHORIZED]: '未授权访问',
  [ERROR_CODES.FORBIDDEN]: '访问被拒绝',
  [ERROR_CODES.NOT_FOUND]: '资源不存在',
  
  [ERROR_CODES.KNOWLEDGE_NOT_FOUND]: '知识库不存在',
  [ERROR_CODES.KNOWLEDGE_NAME_EXISTS]: '知识库名称已存在',
  [ERROR_CODES.KNOWLEDGE_DELETE_FAILED]: '知识库删除失败',
  
  [ERROR_CODES.DOCUMENT_NOT_FOUND]: '文档不存在',
  [ERROR_CODES.DOCUMENT_UPLOAD_FAILED]: '文档上传失败',
  [ERROR_CODES.DOCUMENT_PROCESS_FAILED]: '文档处理失败',
  [ERROR_CODES.UNSUPPORTED_FILE_TYPE]: '不支持的文件类型',
  [ERROR_CODES.FILE_SIZE_EXCEEDED]: '文件大小超出限制',
  
  [ERROR_CODES.CHUNK_NOT_FOUND]: '知识块不存在',
  [ERROR_CODES.CHUNK_UPDATE_FAILED]: '知识块更新失败',
  [ERROR_CODES.CHUNK_DELETE_FAILED]: '知识块删除失败',
  
  [ERROR_CODES.RETRIEVAL_FAILED]: '检索失败',
  [ERROR_CODES.INVALID_QUERY]: '查询内容无效',
  
  [ERROR_CODES.INSUFFICIENT_PERMISSION]: '权限不足',
  [ERROR_CODES.PERMISSION_UPDATE_FAILED]: '权限更新失败'
};

// ==================== 工具函数类型 ====================

// 用于类型检查的工具函数
export const isValidRunningStatus = (status: string): status is RunningStatus => {
  return Object.values(RunningStatus).includes(status as RunningStatus);
};

export const isValidDocumentType = (type: string): type is DocumentType => {
  return Object.values(DocumentType).includes(type as DocumentType);
};

export const isValidKnowledgeStatus = (status: string): status is KnowledgeStatus => {
  return Object.values(KnowledgeStatus).includes(status as KnowledgeStatus);
};

export const isValidKnowledgePermission = (permission: string): permission is KnowledgePermission => {
  return Object.values(KnowledgePermission).includes(permission as KnowledgePermission);
};

// 获取文件扩展名对应的文档类型
export const getDocumentTypeFromExtension = (extension: string): DocumentType | null => {
  const normalizedExt = extension.toLowerCase().replace('.', '');
  switch (normalizedExt) {
    case 'pdf': return DocumentType.PDF;
    case 'doc': return DocumentType.DOC;
    case 'docx': return DocumentType.DOCX;
    case 'txt': return DocumentType.TXT;
    case 'md': return DocumentType.MD;
    case 'html': return DocumentType.HTML;
    case 'json': return DocumentType.JSON;
    default: return null;
  }
};

// 检查文件是否为支持的类型
export const isSupportedFileType = (filename: string): boolean => {
  const extension = filename.split('.').pop()?.toLowerCase() || '';
  return getDocumentTypeFromExtension(extension) !== null;
};

// 格式化文件大小
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

// 格式化进度百分比
export const formatProgress = (progress: number): string => {
  return `${Math.round(progress)}%`;
};

// 生成缓存键
export const generateCacheKey = (prefix: string, ...params: string[]): string => {
  return `${prefix}:${params.join(':')}`;
};