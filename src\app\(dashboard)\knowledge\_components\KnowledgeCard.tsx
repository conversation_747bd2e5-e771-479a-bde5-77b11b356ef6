import { <PERSON>, Switch, Typo<PERSON>, <PERSON><PERSON>, <PERSON>ton } from "antd";
import IconKnowledge from "../svg/IconKnowledge.svg";
import { MoreOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import type React from "react";
import Image from "next/image";

const { Text } = Typography;

export type Action = {
  label: string;
  className?: string;
  type: "enable" | "del" | "edit" | "disable";
  icon: React.ReactNode;
};

interface KnowledgeCardProps {
  tag: React.ReactNode;
  footerTag: React.ReactNode;
  actionButtons: Action[];
  description: string;
  title: string;
  createTime: string;
  handleAction: (type: Action["type"]) => void;
  handleSwitch: (value: boolean) => void;
  onClick?: () => void;
}

const KnowledgeCard = ({
  tag,
  footerTag,
  handleAction,
  actionButtons,
  description,
  title,
  createTime,
  handleSwitch,
  onClick,
}: KnowledgeCardProps) => {
  const [actionPopoverVisible, setActionPopoverVisible] = useState(false);

  const _handleAction = (
    type: (typeof actionButtons)[number]["type"],
    e: React.MouseEvent<HTMLElement, MouseEvent>
  ) => {
    e.stopPropagation();
    setActionPopoverVisible(false);
    handleAction(type);
  };

  return (
    <Card
      className={`relative rounded-[8px] cursor-pointer group/card [&_.ant-card-body]:p-[12px_16px] ${
        actionPopoverVisible ? "shadow-[0_4px_10px_rgb(var(--gray-2))]" : ""
      }`}
      hoverable
      onClick={onClick}
    >
      <IconKnowledge className="absolute right-[12px] top-[12px]" />
      <div className="flex justify-between items-center">
        <Text className="text-[16px]">{title}</Text>
      </div>
      <p className="my-[8px] text-[#5c5c5c] leading-[24px] min-h-[24px] text-ellipsis overflow-hidden">
        {description}
      </p>
      <div className="flex gap-[8px] mb-[12px]">{tag}</div>
      <div className="flex justify-end items-center relative min-h-[40px]">
        <div
          className={`group-hover/card:opacity-100 flex opacity-0 justify-between transition-opacity items-center flex-1 absolute w-full z-1 ${
            actionPopoverVisible ? "opacity-100" : ""
          }`}
        >
          <Text className="text-[#adadad] text-[12px]">
            创建于：{createTime}
          </Text>
          <Popover
            placement="bottomLeft"
            trigger="hover"
            open={actionPopoverVisible}
            onOpenChange={setActionPopoverVisible}
            content={
              <div className="flex flex-col">
                {actionButtons.map((action) => (
                  <Button
                    key={action.type}
                    type="text"
                    block
                    className={`w-[148px] justify-start ${action.className}`}
                    onClick={(e) => _handleAction(action.type, e)}
                    // icon={action.icon}
                  >
                    {action.label}
                  </Button>
                ))}
              </div>
            }
          >
            <Button
              type="text"
              className="bottom-[]rounded-[8px] border border-[#f5f5f5]"
              icon={<MoreOutlined />}
            ></Button>
          </Popover>
        </div>
        <div
          className={`group-hover/card:opacity-0 ${
            actionPopoverVisible ? "opacity-0" : "opacity-100"
          }`}
        >
          {footerTag}
        </div>
      </div>
    </Card>
  );
};

export default KnowledgeCard;
