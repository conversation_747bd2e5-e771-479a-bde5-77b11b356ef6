import { Typography } from 'antd';
import type React from 'react';
import { useState } from 'react';
import IconKnowledge from '../svg/IconKnowledge.svg';
import KnowledgeContextMenu from './KnowledgeContextMenu';

const { Text } = Typography;

export type Action = {
  label: string;
  className?: string;
  type: 'enable' | 'del' | 'edit' | 'disable';
  icon: React.ReactNode;
};

interface KnowledgeCardProps {
  tag: React.ReactNode;
  footerTag: React.ReactNode;
  actionButtons: Action[];
  description: string;
  title: string;
  createTime: string;
  handleAction: (type: Action['type']) => void;
  handleSwitch: (value: boolean) => void;
  onClick?: () => void;
  doc_num: number;
  status: string;
}

const KnowledgeCard = ({
  tag,
  footerTag,
  handleAction,
  actionButtons,
  description,
  title,
  createTime,
  handleSwitch,
  onClick,
  doc_num,
  status,
}: KnowledgeCardProps) => {
  const [menuVisible, setMenuVisible] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });

  const handleMoreClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    const rect = e.currentTarget.getBoundingClientRect();
    setMenuPosition({ x: rect.right, y: rect.top });
    setMenuVisible(true);
  };

  const handleCloseMenu = () => {
    setMenuVisible(false);
  };

  const handleMenuAction = (type: Action['type']) => {
    handleAction(type);
    setMenuVisible(false);
  };

  return (
    <>
      <div
        className={`min-h-[150px] group bg-white rounded-lg border border-[#0000000F] hover:shadow-lg transition-all duration-200 flex flex-col cursor-pointer p-3 ${
          menuVisible ? 'shadow-lg' : ''
        }`}
        onClick={onClick}
      >
        <div className="flex flex-col">
          {/* Header */}
          <div className="flex justify-between items-start mb-3">
            <span className="font-medium text-[#000000E0] text-[16px]">
              {title}
            </span>
            <IconKnowledge />
          </div>

          {/* Description */}
          <span className="text-[#000000B8] text-[13px] font-normal line-clamp-2 mb-3">
            {description}
          </span>

          {/* Tags */}
          <div className="flex flex-wrap gap-[6px] mb-3">{tag}</div>

          {/* Footer */}
          <div className="mt-auto flex justify-between items-center relative min-h-[24px]">
            {/* 默认状态：显示状态信息 */}
            <div
              className={`absolute inset-0 flex justify-between items-center transition-opacity ${
                menuVisible ? 'opacity-0' : 'opacity-100 group-hover:opacity-0'
              }`}
            >
              <span className="text-[12px] text-[#00000066] font-normal">
                {doc_num}文件 | {status === '1' ? '启用' : '禁用'}
              </span>
              {footerTag}
            </div>

            {/* 悬浮状态：显示创建时间和操作按钮 */}
            <div
              className={`absolute inset-0 flex justify-between items-center transition-all duration-200 ${
                menuVisible
                  ? 'opacity-100 visible'
                  : 'opacity-0 invisible group-hover:opacity-100 group-hover:visible'
              }`}
            >
              <Text className="text-[#adadad] text-[12px]">
                创建于：{createTime}
              </Text>
              <img
                src="/assets/ai-employees/more.svg"
                alt="more"
                className="hover:bg-[#0000000A] cursor-pointer rounded-[8px] p-1"
                onClick={handleMoreClick}
              />
            </div>
          </div>
        </div>
      </div>

      {/* 上下文菜单 */}
      <KnowledgeContextMenu
        isVisible={menuVisible}
        position={menuPosition}
        onClose={handleCloseMenu}
        onAction={handleMenuAction}
        actionButtons={actionButtons}
      />
    </>
  );
};

export default KnowledgeCard;
