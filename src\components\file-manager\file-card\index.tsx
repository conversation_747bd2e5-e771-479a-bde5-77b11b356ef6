import { IFile } from '@/interfaces/database/file-manager';
import { formatDate } from '@/utils/date';
import { Button, Space, Tag, Typography, Popover } from 'antd';
import { formatNumberWithThousandsSeparator } from '@/utils/common-util';
import { MoreOutlined, FileOutlined, FolderOutlined } from '@ant-design/icons';
import { useState } from 'react';

const { Text } = Typography;

interface FileCardProps {
    file: IFile;
    onNavigate: (folderId: string) => void;
    onRename: (file: IFile) => void;
    onDelete: (fileId: string, parentId: string, fileName: string) => void;
}

const FileCard = ({ file, onNavigate, onRename, onDelete }: FileCardProps) => {
    // 新增悬浮状态
    const [hovered, setHovered] = useState(false);
    // 处理标签数据
    const renderTags = () => {
        const tagsArray: string[] = [];
        if (typeof file.tags === 'string') {
            tagsArray.push(...file.tags.split(',').filter(tag => tag.trim()));
        } else if (Array.isArray(file.tags)) {
            tagsArray.push(...file.tags);
        }

        return (
            <div className="flex flex-wrap gap-1.5">
                {/* 文件类型标签 */}
                {/* <span className={`inline-flex items-center px-2 py-0.5 text-xs font-normal rounded border ${
                    file.creator_role === 'admin' ? 'bg-red-50 text-red-700 border-red-200' :
                    file.creator_role === 'client' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                    'bg-gray-50 text-gray-700 border-gray-200'
                }`}>
                    {file.creator_role === 'admin' ? '系统' :
                        file.creator_role === 'client' ? '用户' :
                            file.type === 'folder' ? '文件夹' : '文件'}
                </span> */}

                {/* 自定义标签 */}
                {tagsArray.length > 0 && (
                    <>
                        {tagsArray.slice(0, 3).map((tag, index) => (
                            <span key={index} className="inline-flex items-center px-2 py-0.5 text-xs font-normal bg-white-50 text-gray-700 rounded border border-gray-200">
                                {tag.trim()}
                            </span>
                        ))}
                        {tagsArray.length > 3 && (
                            <span className="inline-flex items-center px-2 py-0.5 text-xs font-normal bg-gray-50 text-gray-500 rounded border border-gray-200">+{tagsArray.length - 3}</span>
                        )}
                    </>
                )}

                {/* 知识库标签 */}
                {file.kbs_info && Array.isArray(file.kbs_info) && file.kbs_info.length > 0 && (
                    <>
                        {file.kbs_info.slice(0, 2).map((kb) => (
                            <span key={kb.kb_id} className="inline-flex items-center px-2 py-0.5 text-xs font-normal bg-purple-50 text-purple-700 rounded border border-purple-200">
                                {kb.kb_name}
                            </span>
                        ))}
                        {file.kbs_info.length > 2 && (
                            <span className="inline-flex items-center px-2 py-0.5 text-xs font-normal bg-gray-50 text-gray-500 rounded border border-gray-200">+{file.kbs_info.length - 2}</span>
                        )}
                    </>
                )}
            </div>
        );
    };

    // 处理卡片点击
    const handleCardClick = () => {
        if (file.type === 'folder') {
            onNavigate(file.id);
        }
    };

    // 处理重命名
    const handleRename = (e: React.MouseEvent) => {
        e.stopPropagation();
        onRename(file);
    };

    // 处理删除
    const handleDelete = (e: React.MouseEvent) => {
        e.stopPropagation();
        onDelete(file.id, file.parent_id, file.name);
    };

    return (
        <div
            className="min-h-[150px] group bg-white rounded-lg border border-[#0000000F] hover:shadow-lg transition-all duration-200 flex flex-col cursor-pointer p-3 relative"
            onClick={handleCardClick}
            onMouseEnter={() => setHovered(true)}
            onMouseLeave={() => setHovered(false)}
        >
            {/* 顶部：名称和右上角icon */}
            <div className="flex justify-between items-start mb-2">
                <span className="font-medium text-[#1f1f1f] text-[16px] truncate max-w-[70%]">{file.name}</span>
                <div className="w-8 h-8 rounded-lg bg-gray-50 flex items-center justify-center border border-gray-200">
                    {file.type === 'folder' ? (
                        <FolderOutlined className="text-xl text-gray-500" />
                    ) : (
                        <FileOutlined className="text-xl text-gray-500" />
                    )}
                </div>
            </div>
            {/* 描述 */}
            <span className="text-[#474747] text-[13px] font-normal line-clamp-2 mb-2 max-w-full block overflow-hidden">{file.description && file.description.trim() ? file.description : '描述'}</span>
            {/* 标签区域 */}
            <div className="flex flex-wrap gap-1.5 mb-2 max-h-[40px] overflow-hidden">
                {renderTags()}
            </div>
            {/* 底部：时间/文件数 */}
            <div className="mt-auto flex justify-between items-end w-full">
                <span className="text-[12px] text-[#999999] truncate max-w-[70%]">
                    {hovered
                        ? (file.type === 'folder'
                            ? `${file.file_count || 0}个文件`
                            : `${formatNumberWithThousandsSeparator((file.size / 1024).toFixed(2))} KB`)
                        : `创建于 ${formatDate(file.create_time)}`}
                </span>
                {/* 操作按钮Popover */}
                <Popover
                    trigger="click"
                    placement="bottomRight"
                    content={
                        <Space direction="vertical" size="small">
                            <Button
                                type="text"
                                size="small"
                                onClick={handleRename}
                            >
                                编辑
                            </Button>
                            <Button
                                type="text"
                                size="small"
                                danger
                                onClick={handleDelete}
                            >
                                删除
                            </Button>
                        </Space>
                    }
                >
                    <Button
                        type="text"
                        size="small"
                        className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 w-6 h-6 rounded-lg flex items-center justify-center text-gray-500 hover:bg-gray-100 hover:text-gray-700 p-0"
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                    >
                        <MoreOutlined />
                    </Button>
                </Popover>
            </div>
        </div>
    );
};

export default FileCard;