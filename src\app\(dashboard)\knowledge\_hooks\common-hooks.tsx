import { useState, useCallback } from "react";
import { App } from "antd";
import { ExclamationCircleFilled } from "@ant-design/icons";
import { ReactNode } from "react";

export const useSetModalState = () => {
  const [visible, setVisible] = useState(false);

  const showModal = useCallback(() => {
    setVisible(true);
  }, []);
  const hideModal = useCallback(() => {
    setVisible(false);
  }, []);

  const switchVisible = useCallback(() => {
    setVisible(!visible);
  }, [visible]);

  return { visible, showModal, hideModal, switchVisible };
};

interface IProps {
  title?: string;
  content?: ReactNode;
  onOk?: (...args: any[]) => any;
  onCancel?: (...args: any[]) => any;
}

export const useShowDeleteConfirm = () => {
  const { modal } = App.useApp();
  const confirm = modal.confirm;
  const showDeleteConfirm = useCallback(
    ({ title, content, onOk, onCancel }: IProps): Promise<number> => {
      return new Promise((resolve, reject) => {
        confirm({
          title: title ?? "确定删除吗?",
          icon: <ExclamationCircleFilled />,
          content,
          okText: "是",
          okType: "danger",
          cancelText: "取消",
          async onOk() {
            try {
              const ret = await onOk?.();
              resolve(ret);
              console.info(ret);
            } catch (error) {
              reject(error);
            }
          },
          onCancel() {
            onCancel?.();
          },
        });
      });
    },
    [confirm]
  );

  return showDeleteConfirm;
};
