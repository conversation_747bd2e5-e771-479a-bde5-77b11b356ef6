# AgentFoundry-Ziko 开发规范

## 三阶段工作流

### 阶段一：分析问题
**声明格式**：`【分析问题】`

**必须做的事**：
- 深入理解需求本质
- 搜索所有相关代码
- 识别问题根因
- 发现架构问题
- 收集必要信息
- 提供1~3个解决方案
- 评估每个方案的优劣

**绝对禁止**：
- ❌ 修改任何代码
- ❌ 急于给出解决方案
- ❌ 跳过搜索和理解步骤

### 阶段二：细化方案
**声明格式**：`【细化方案】`

**前置条件**：用户明确选择了方案

**必须做的事**：
- 列出变更（新增、修改、删除）的文件
- 简要描述每个文件的变化

### 阶段三：执行方案
**声明格式**：`【执行方案】`

**必须做的事**：
- 严格按照选定方案实现
- 修改后运行类型检查（npm run type-check）

**绝对禁止**：
- ❌ 提交代码（除非用户明确要求）
- ❌ 启动开发服务器

## ahooks 优先使用原则

### 核心原则
**当 ahooks 提供了相应功能时，优先使用 ahooks 而非 React 原生 Hook**

### 常用替换对照

| React 原生 | ahooks 替代 | 优势 |
|-----------|------------|------|
| `useState` | `useBoolean` | 专门处理布尔值，提供 `setTrue`、`setFalse`、`toggle` 方法 |
| `useState` | `useLocalStorageState` | 自动同步 localStorage，数据持久化 |
| `useEffect` | `useMount` | 专门处理组件挂载，语义更清晰 |
| `useEffect` | `useRequest` | 完整的请求生命周期管理，支持缓存、重试等 |
| 手动防抖 | `useDebounce` | 内置防抖功能，使用更简单 |
| 手动节流 | `useThrottle` | 内置节流功能，性能更好 |

### 使用示例

```typescript
// ❌ 避免
const [loading, setLoading] = useState(false);
const [data, setData] = useState(null);

useEffect(() => {
  // 手动处理请求逻辑
}, []);

// ✅ 推荐
const { data, loading, error } = useRequest(fetchData);
```

```typescript
// ❌ 避免
const [isOpen, setIsOpen] = useState(false);
const toggleOpen = () => setIsOpen(!isOpen);

// ✅ 推荐
const [isOpen, { toggle, setTrue: open, setFalse: close }] = useBoolean(false);
```

## 主题样式规范

### 强制要求：避免非主题样式
**禁止使用硬编码颜色和非主题样式，必须使用项目的主题变量系统。**

### 禁止的样式写法
```html
❌ 硬编码颜色
<div class="bg-[#fafafb] text-[#333333]">
<div class="bg-gray-50 text-gray-900">

❌ 非主题的 Tailwind 颜色
<button class="bg-blue-500 hover:bg-blue-600">
```

### 正确的样式写法
```html
✅ 使用主题变量
<div class="bg-layer-1 text-text-primary">
<button class="bg-primary hover:bg-blue-600">
<span class="text-error">
<div class="bg-purple border-border-light">
```

### 颜色系统

#### 基础颜色
```css
--text-primary: #000000      /* 主要文本 */
--text-secondary: #666666    /* 次级文本 */
--text-muted: #999999        /* 静音文本 */
--bg-base: #ffffff           /* 基础背景 */
```

#### 背景色层次
```css
--bg-layer-1: rgba(0, 0, 0, 0.01)  /* 最浅背景层 */
--bg-layer-2: rgba(0, 0, 0, 0.02)  /* 浅背景层 */
--bg-layer-3: rgba(0, 0, 0, 0.04)  /* 中等背景层 */
--bg-layer-4: rgba(0, 0, 0, 0.08)  /* 深背景层 */
```

#### 功能色
```css
--color-brand: #5938E4     /* 品牌色 */
--color-primary: #4285F4   /* 主要操作色 */
--color-success: #34A853   /* 成功状态 */
--color-warning: #FBBC04   /* 警告状态 */
--color-error: #E74639     /* 错误状态 */
--color-info: #57CBFF      /* 信息提示 */
```

### 常用组件样式模板

#### 卡片组件
```html
<div class="bg-bg-base border border-border-light rounded-lg shadow-sm p-4">
  <h3 class="text-text-primary font-semibold">卡片标题</h3>
  <p class="text-text-secondary">卡片内容</p>
</div>
```

#### 按钮组件
```html
<!-- 主要按钮 -->
<button class="bg-primary text-white px-4 py-2 rounded-md hover:bg-blue-600">
  主要操作
</button>

<!-- 次要按钮 -->
<button class="bg-layer-2 text-text-primary px-4 py-2 rounded-md hover:bg-layer-3">
  次要操作
</button>
```

#### 输入框组件
```html
<input 
  class="bg-bg-base border border-border-medium rounded-md px-3 py-2 
         text-text-primary placeholder:text-text-muted
         focus:border-primary focus:ring-1 focus:ring-primary"
  placeholder="请输入内容"
/>
```

## 代码规范

### TypeScript 使用
- 优先使用 TypeScript，避免 `any` 类型
- 为所有函数和组件提供类型定义
- 使用接口定义复杂对象类型

### 组件规范
- 函数组件优先
- 使用 `memo` 优化性能
- Props 类型定义清晰

### 导入规范
```typescript
// 推荐的导入方式
import { useRequest, useBoolean } from 'ahooks';
import { useAGUIChat, useAGUIAction } from '@/hooks';
```

## 性能优化

### 1. 使用 ahooks 优化
```typescript
// 防抖优化搜索
const debouncedKeyword = useDebounce(keyword, { wait: 300 });

// 本地存储优化
const [settings, setSettings] = useLocalStorageState('userSettings');
```

### 2. 请求优化
```typescript
// 使用 ready 参数避免不必要的请求
const { data } = useRequest(fetchData, {
  ready: !!userId,
  refreshDeps: [userId]
});
```

### 3. 组件优化
```typescript
// 使用 memo 避免不必要的重渲染
const OptimizedComponent = memo(({ data }) => {
  return <div>{data}</div>;
});
```

## 开发检查清单

### 开发前检查
- [ ] 理解需求，分析问题
- [ ] 搜索相关代码
- [ ] 选择合适的技术方案

### 开发中检查
- [ ] 优先使用 ahooks
- [ ] 使用主题变量
- [ ] 遵循 TypeScript 规范
- [ ] 注意性能优化

### 开发后检查
- [ ] 运行类型检查
- [ ] 测试功能是否正常
- [ ] 检查是否有硬编码颜色
- [ ] 确保代码符合项目规范

## 常见问题

### Q: 什么时候不使用 ahooks？
A: 当 ahooks 没有提供对应功能，或需要非常精细的控制逻辑时。

### Q: 如何处理复杂的主题需求？
A: 优先使用现有的主题变量，如需新增颜色，先在主题系统中定义。

### Q: 性能优化的优先级是什么？
A: 
1. 使用 ahooks 的内置优化
2. 避免不必要的重渲染
3. 优化网络请求
4. 代码分割和懒加载