import { useDebounce } from "ahooks";
import { useHandleSearchChange } from "./logic-hooks";
import {
  useInfiniteQuery,
  useQueryClient,
  useMutation,
  useQuery,
} from "@tanstack/react-query";
import kbService, { listDataset } from "@/services/knowledge-service";
import { message } from "antd";
import { IKnowledge } from "@/interfaces/database/knowledge";
import { useGetKnowledgeSearchParams } from "./route-hook";
import { useState } from "react";
import fileManagerService from '@/services/file-manager-service';
import { IAddFileToKnowledgeRequest } from '@/interfaces/knowledge';

export const useInfiniteFetchKnowledgeList = () => {
  const { searchString, handleInputChange } = useHandleSearchChange();
  const debouncedSearchString = useDebounce(searchString, { wait: 500 });
  const PageSize = 10;
  const [tags, setTags] = useState<string[]>([]);
  const [role, setRole] = useState<"all" | "admin" | "user">("all");
  const {
    data,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
    status,
    refetch,
  } = useInfiniteQuery({
    queryKey: ["infiniteFetchKnowledgeList", debouncedSearchString, tags, role],
    initialPageParam: 1,
    queryFn: async ({ pageParam = 1 }) => {
      const { data } = await listDataset({
        page: pageParam,
        page_size: PageSize,
        keywords: debouncedSearchString,
        tags: tags.join(","),
        // role: role,
      });
      const list = data?.data ?? [];
      return list;
    },
    getNextPageParam: (lastPage, pages) => {
      const currentPage = pages.length;
      if (currentPage * PageSize < lastPage.total) {
        return currentPage + 1;
      }
      return undefined;
    },
  });
  return {
    data,
    loading: isFetching,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
    status,
    handleInputChange,
    searchString,
    refetch,
    setTags,
    setRole,
    tags,
    role,
  };
};

export const useCreateKnowledge = () => {
  const queryClient = useQueryClient();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ["createKnowledge"],
    mutationFn: async (params: { id?: string; name: string }) => {
      const { data = {} } = await kbService.createKb(params);
      if (data.code === 0) {
        message.success(`${params?.id ? "更新成功" : "创建成功"}`);
        queryClient.invalidateQueries({
          queryKey: ["infiniteFetchKnowledgeList"],
        });
      }
      return data;
    },
  });

  return { data, loading, createKnowledge: mutateAsync };
};

export const useUpdateKnowledge = (shouldFetchList = false) => {
  const queryClient = useQueryClient();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ["updateKnowledge"],
    mutationFn: async (params: Record<string, any>) => {
      const { data = {} } = await kbService.updateKb({
        kb_id: params?.kb_id,
        ...params,
      });
      if (data.code === 0) {
        message.success("更新成功");
        // 更新列表
        queryClient.invalidateQueries({
          queryKey: ["infiniteFetchKnowledgeList"],
        });
      }
      return data;
    },
  });

  return { data, loading, saveKnowledgeConfiguration: mutateAsync };
};

export const useDeleteKnowledge = () => {
  const queryClient = useQueryClient();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ["deleteKnowledge"],
    mutationFn: async (id: string) => {
      const { data } = await kbService.rmKb({ kb_id: id });
      if (data.code === 0) {
        message.success("删除成功");
        queryClient.invalidateQueries({
          queryKey: ["infiniteFetchKnowledgeList"],
        });
      }
      return data?.data ?? [];
    },
  });

  return { data, loading, deleteKnowledge: mutateAsync };
};

export const useFetchKnowledgeBaseConfiguration = () => {
  const { knowledgeId } = useGetKnowledgeSearchParams();

  const { data, isFetching: loading } = useQuery<IKnowledge>({
    queryKey: ["fetchKnowledgeDetail"],
    initialData: {} as IKnowledge,
    queryFn: async () => {
      const { data } = await kbService.get_kb_detail({
        kb_id: knowledgeId,
      });
      return data?.data ?? {};
    },
  });

  return { data, loading };
};

export const useFetchTagListByKnowledgeIds = () => {
  const [knowledgeIds, setKnowledgeIds] = useState<string[]>([]);

  const { data, isFetching: loading } = useQuery<Array<[string, number]>>({
    queryKey: ["fetchTagListByKnowledgeIds"],
    enabled: knowledgeIds.length > 0,
    initialData: [],
    queryFn: async () => {
      const { data } = await kbService.listTagByKnowledgeIds({
        kb_ids: knowledgeIds.join(","),
      });
      const list = data?.data || [];
      return list;
    },
  });

  return { list: data, loading, setKnowledgeIds };
};

export const useFetchGetKbTags = () => {
  const { data, isFetching: loading } = useQuery<string[]>({
    queryKey: ["fetchGetKbTags"],
    queryFn: async () => {
      const { data } = await kbService.getKbTags();
      const list = data?.data?.tags || [];
      return list;
    },
  });

  return { kbTags: data, loading };
};


export const useAddFileToKnowledge = () => {
  const queryClient = useQueryClient();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['addFileToKnowledge'],
    mutationFn: async (params: IAddFileToKnowledgeRequest) => {
      const { data } = await fileManagerService.connectFileToKnowledge(params);
      if (data.code === 0) {
        message.success('添加成功');
        queryClient.invalidateQueries({
          queryKey: ['fetchDocumentList'],
        });
      }
      return data.code;
    },
  });

  return { data, loading, addFileToKnowledge: mutateAsync };
};