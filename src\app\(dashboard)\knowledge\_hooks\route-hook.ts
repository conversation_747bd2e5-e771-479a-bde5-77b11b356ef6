import { useSearchParams } from "next/navigation";
import { useCallback, useState } from "react";
import { KnowledgeSearchParams } from "../_constants/knowledge";

export const useSetPaginationParams = () => {
  const searchParams = useSearchParams();
  const page = searchParams.get("page");
  const pageSize = searchParams.get("pageSize");

  const [paginationParams, setPaginationParams] = useState({
    page: Number(page) || 1,
    pageSize: Number(pageSize) || 10,
  });

  const onPaginationParams = useCallback(
    (page = 1, pageSize?: number) => {
      setPaginationParams((prev) => {
        const pag = { ...prev };
        pag.page = page;
        if (pageSize) {
          pag.pageSize = pageSize;
        }
        return pag;
      });
    },
    [paginationParams]
  );

  return {
    setPaginationParams: onPaginationParams,
    page: Number(paginationParams.page) || 1,
    size: Number(paginationParams.pageSize) || 10,
  };
};

export const useGetKnowledgeSearchParams = () => {
  const searchParams = useSearchParams();
  return {
    documentId: searchParams.get(KnowledgeSearchParams.DocumentId) || "",
    knowledgeId: searchParams.get(KnowledgeSearchParams.KnowledgeId) || "",
  };
};
