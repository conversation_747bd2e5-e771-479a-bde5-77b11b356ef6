# ahooks 优先使用指南

## 核心原则

在 AgentFoundry-Ziko 项目中，我们遵循 **ahooks 优先** 的开发原则：

> 🎯 **优先原则**：当 ahooks 提供了相应功能时，优先使用 ahooks 而非 React 原生 Hook

## 为什么选择 ahooks 优先？

### 1. 更好的开发体验
- 更少的样板代码
- 更直观的 API 设计
- 更好的 TypeScript 支持

### 2. 更强的功能性
- 内置常见业务逻辑
- 自动处理边界情况
- 提供更多实用功能

### 3. 更高的可靠性
- 经过大规模项目验证
- 完善的错误处理
- 稳定的函数引用

### 4. 更好的性能
- 优化的重渲染控制
- 内置防抖节流
- 智能的依赖管理

## Hook 替换对照表

### 状态管理 Hook

| React 原生 | ahooks 替代 | 优势 |
|-----------|------------|------|
| `useState` | `useBoolean` | 专门处理布尔值，提供 `setTrue`、`setFalse`、`toggle` 方法 |
| `useState` | `useToggle` | 在多个值之间切换，API 更简洁 |
| `useState` | `useLocalStorageState` | 自动同步 localStorage，数据持久化 |
| `useState` | `useSessionStorageState` | 自动同步 sessionStorage |
| `useState` | `useSetState` | 类似 Class 组件的 setState，支持对象合并 |

### 副作用 Hook

| React 原生 | ahooks 替代 | 优势 |
|-----------|------------|------|
| `useEffect` | `useMount` | 专门处理组件挂载，语义更清晰 |
| `useEffect` | `useUnmount` | 专门处理组件卸载，自动清理 |
| `useEffect` | `useUpdateEffect` | 跳过首次渲染，只在更新时执行 |
| `useEffect` | `useDeepCompareEffect` | 深度比较依赖，避免不必要的执行 |

### 网络请求 Hook

| React 原生 | ahooks 替代 | 优势 |
|-----------|------------|------|
| `useEffect` + `fetch` | `useRequest` | 完整的请求生命周期管理，支持缓存、重试等 |
| 手动管理 loading | `useRequest` | 自动管理 loading、error、data 状态 |

### 性能优化 Hook

| React 原生 | ahooks 替代 | 优势 |
|-----------|------------|------|
| `useCallback` + 防抖逻辑 | `useDebounce` | 内置防抖功能，使用更简单 |
| `useCallback` + 节流逻辑 | `useThrottle` | 内置节流功能，性能更好 |
| `useMemo` + 复杂逻辑 | `useCreation` | 更强的依赖比较，避免不必要计算 |

### DOM 操作 Hook

| React 原生 | ahooks 替代 | 优势 |
|-----------|------------|------|
| `useRef` + DOM 操作 | `useClickAway` | 专门处理点击外部区域 |
| `useRef` + 事件监听 | `useKeyPress` | 专门处理按键事件 |
| `useRef` + 滚动监听 | `useScroll` | 专门处理滚动事件 |

## 使用策略

### 1. 新功能开发
```typescript
// ❌ 避免
import { useState, useEffect } from 'react';

function MyComponent() {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(null);
  
  useEffect(() => {
    // 手动处理请求逻辑
  }, []);
}

// ✅ 推荐
import { useRequest } from 'ahooks';

function MyComponent() {
  const { data, loading, error } = useRequest(fetchData);
}
```

### 2. 状态管理
```typescript
// ❌ 避免
const [isOpen, setIsOpen] = useState(false);
const toggleOpen = () => setIsOpen(!isOpen);
const openModal = () => setIsOpen(true);
const closeModal = () => setIsOpen(false);

// ✅ 推荐
const [isOpen, { toggle, setTrue: openModal, setFalse: closeModal }] = useBoolean(false);
```

### 3. 本地存储
```typescript
// ❌ 避免
const [theme, setTheme] = useState(() => {
  return localStorage.getItem('theme') || 'light';
});

useEffect(() => {
  localStorage.setItem('theme', theme);
}, [theme]);

// ✅ 推荐
const [theme, setTheme] = useLocalStorageState('theme', {
  defaultValue: 'light'
});
```

### 4. 防抖节流
```typescript
// ❌ 避免
const [keyword, setKeyword] = useState('');
const [debouncedKeyword, setDebouncedKeyword] = useState('');

useEffect(() => {
  const timer = setTimeout(() => {
    setDebouncedKeyword(keyword);
  }, 300);
  
  return () => clearTimeout(timer);
}, [keyword]);

// ✅ 推荐
const [keyword, setKeyword] = useState('');
const debouncedKeyword = useDebounce(keyword, { wait: 300 });
```

## 代码审查清单

在代码审查时，请检查以下项目：

### ✅ 必须检查项
- [ ] 是否使用了 React 原生的 `useState` 而不是 ahooks 的替代方案？
- [ ] 是否有手动实现的防抖/节流逻辑可以用 ahooks 替代？
- [ ] 是否有复杂的 `useEffect` 逻辑可以用 ahooks 简化？
- [ ] 是否有手动管理的 localStorage 逻辑？
- [ ] 是否有手动实现的网络请求逻辑？

### ⚠️ 注意事项
- [ ] 确保 ahooks 的使用符合组件的实际需求
- [ ] 检查是否正确处理了 SSR 场景
- [ ] 验证 TypeScript 类型是否正确
- [ ] 确认性能优化是否有效

## 迁移策略

### 阶段一：新功能优先
- 所有新开发的功能必须优先使用 ahooks
- 建立团队共识和开发规范

### 阶段二：渐进迁移
- 逐步替换现有代码中的 React 原生 Hook
- 优先处理高频使用的组件

### 阶段三：全面优化
- 完成所有可替换的 Hook 迁移
- 建立自动化检查规则

## 常见问题

### Q: 什么时候不应该使用 ahooks？
A: 以下情况可以考虑使用 React 原生 Hook：
- ahooks 没有提供对应功能
- 需要非常精细的控制逻辑
- 性能要求极高的场景（需要具体评估）

### Q: 如何处理 ahooks 与现有代码的兼容性？
A: 
- ahooks 与 React 原生 Hook 可以混用
- 逐步迁移，不需要一次性替换所有代码
- 保持向后兼容性

### Q: 如何确保团队成员都遵循 ahooks 优先原则？
A: 
- 建立代码审查规范
- 提供培训和文档
- 配置 ESLint 规则（可选）
- 定期技术分享

## 总结

遵循 ahooks 优先原则将帮助我们：
- 减少代码量和维护成本
- 提高开发效率和代码质量
- 统一团队开发风格
- 充分利用 ahooks 的优势

记住：**当 ahooks 提供了相应功能时，优先选择 ahooks！**