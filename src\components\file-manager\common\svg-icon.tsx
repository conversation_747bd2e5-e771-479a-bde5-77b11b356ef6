import { IconMap } from '@/constants/llm';
import { cn } from '@/lib/utils';
import Icon, { UserOutlined } from '@ant-design/icons';
import { IconComponentProps } from '@ant-design/icons/lib/components/Icon';
import { Avatar } from 'antd';
import { AvatarSize } from 'antd/es/avatar/AvatarContext';

// Webpack require.context 类型定义
declare global {
  interface __WebpackModuleApi {
    RequireContext: {
      keys(): string[];
      (id: string): any;
      <T>(id: string): T;
    };
  }
}

const importAll = (requireContext: any) => {
  const list = requireContext.keys().map((key: string) => {
    const name = key.replace(/\.\/(.*)\.\w+$/, '$1');
    return { name, value: requireContext(key) };
  });
  return list;
};

let routeList: { name: string; value: string }[] = [];

try {
  // @ts-ignore - require.context 是 Webpack 特有的 API
  routeList = importAll(require.context('@/assets/svg', true, /\.svg$/));
} catch (error) {
  console.warn('Failed to load SVG icons:', error);
  routeList = [];
}

interface IProps extends IconComponentProps {
  name: string;
  width: string | number;
  height?: string | number;
  imgClass?: string;
}

const SvgIcon = ({ name, width, height, imgClass, ...restProps }: IProps) => {
  const ListItem = routeList.find((item) => item.name === name);
  
  // 如果找不到图标，显示一个默认的占位符
  if (!ListItem) {
    console.warn(`SVG icon not found: ${name}`);
    return (
      <Icon
        component={() => (
          <div
            style={{ 
              width, 
              height: height || width, 
              backgroundColor: '#f0f0f0',
              border: '1px dashed #ccc',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '10px',
              color: '#999'
            }}
          >
            {name.split('/').pop()}
          </div>
        )}
        {...(restProps as any)}
      />
    );
  }

  return (
    <Icon
      component={() => (
        <img
          src={ListItem.value}
          alt=""
          width={width}
          height={height}
          className={cn(imgClass, 'max-w-full')}
        />
      )}
      {...(restProps as any)}
    />
  );
};

export const LlmIcon = ({
  name,
  height = 48,
  width = 48,
  size = 'large',
  imgClass,
}: {
  name: string;
  height?: number;
  width?: number;
  size?: AvatarSize;
  imgClass?: string;
}) => {
  const icon = IconMap[name as keyof typeof IconMap];

  return icon ? (
    <SvgIcon
      name={`llm/${icon}`}
      width={width}
      height={height}
      imgClass={imgClass}
    ></SvgIcon>
  ) : (
    <Avatar shape="square" size={size} icon={<UserOutlined />} />
  );
};

export default SvgIcon;
