"use client";

import React from "react";
import { useAuthContext } from "@/lib/auth/AuthProvider";

interface LoginModalProps {
  isOpen: boolean;
  onClose?: () => void;
  reason?: string;
}

export default function LoginModal({
  isOpen,
  onClose,
  reason,
}: LoginModalProps) {
  const { login } = useAuthContext();

  if (!isOpen) return null;

  const handleLogin = () => {
    login();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#00000014] select-none">
      <div className="bg-bg-base rounded-2xl w-[500px] h-[300px] relative flex flex-col">
        <img src="/assets/login/login-bg.svg" alt="" />

        <div className="flex-1 flex flex-col justify-center items-start p-6">
          {/* 标题 */}
          <div className="text-start ">
            <h1 className="text-xl font-medium text-[#000000E0] mb-1">
              你好！
            </h1>
            <p className="text-[#00000066] font-normal text-sm">
              {reason || "登录以继续"}
            </p>
          </div>

          {/* 登录按钮 */}
          <button
            onClick={handleLogin}
            className="w-full bg-[#000000E0] text-sm text-[#FFFFFF] py-2 mt-10 rounded-lg font-medium hover:bg-gray-800 transition-colors cursor-pointer"
          >
            登录
          </button>
        </div>
      </div>
    </div>
  );
}
