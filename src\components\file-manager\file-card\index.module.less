// 文件卡片样式
.fileCard {
    height: 240px;
    border-radius: 8px;
    border: 1px solid #f5f5f5;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    background-color: #ffffff;
    padding: 20px 24px;

    &:hover {
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

        .cardFooter {
            .defaultInfo {
                opacity: 0;
            }

            .hoverInfo {
                opacity: 1;
                pointer-events: auto;
            }
        }

        .cardActionButton {
            opacity: 1;
        }
    }
}

.cardContent {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
}

.cardHeader {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.iconWrapper {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cardTitle {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    flex: 1;
    margin: 0;
}

.cardBody {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.cardDescription {
    .descriptionText {
        font-size: 14px;
        color: #5c5c5c;
        margin: 0;
        line-height: 20px;
    }
}

.cardTags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.fileTypeTag {
    padding: 2px 6px;
    border: none;
    border-radius: 4px;
    font-weight: 400;
    font-size: 12px;

    // 系统标签样式（admin角色）
    &.system {
        background-color: #e8e0fc;
        color: #713cee;
    }

    // 用户标签样式（client角色）
    &.user {
        background-color: #fdf2e1;
        color: #cb9039;
    }

    // 默认样式（文件夹/文件）
    &:not(.system):not(.user) {
        background-color: #e8e0fc;
        color: #713cee;
    }
}

.customTag {
    padding: 2px 6px;
    border: 1px solid #ebebeb;
    border-radius: 4px;
    font-weight: 400;
    font-size: 12px;
    color: #5c5c5c;
    background-color: #ffffff;
}

.kbTag {
    padding: 2px 6px;
    border: 1px solid #ebebeb;
    border-radius: 4px;
    font-weight: 400;
    font-size: 12px;
    color: #5c5c5c;
    background-color: #fafafa;
}

.moreTag {
    padding: 2px 6px;
    border: 1px solid #ebebeb;
    border-radius: 4px;
    font-weight: 400;
    font-size: 12px;
    color: #adadad;
    background-color: #fafafa;
}

.cardFooter {
    margin-top: 16px;
    padding-top: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.defaultInfo {
    opacity: 1;
    transition: opacity 0.3s ease;

    .cardTime {
        font-size: 12px;
        color: #adadad;
        margin: 0;
    }
}

.hoverInfo {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;

    .hoverTime {
        font-size: 12px;
        color: #adadad;
        margin: 0;
    }
}

.cardActionButton {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #f5f5f5;
    opacity: 0;
    transition: all 0.3s;
    height: 32px;
    width: 32px;

    &:hover {
        background-color: #fafafa;
    }

    img {
        width: 16px;
        height: 16px;
    }
}