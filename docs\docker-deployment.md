# Docker 部署指南

AgentFoundry-Ziko 项目的 Docker 容器化部署配置和使用说明。

## 🏗️ 架构概述

采用 **Node 官方镜像 + 多阶段构建** 的优化方案：

- **依赖阶段**: 安装系统依赖和 Node 模块
- **构建阶段**: 编译生产代码
- **运行阶段**: 最小化运行环境

## 📋 配置文件说明

### 核心文件

| 文件                 | 用途              |
| -------------------- | ----------------- |
| `Dockerfile`         | 多阶段构建配置    |
| `docker-compose.yml` | 容器编排配置      |
| `.dockerignore`      | 构建忽略文件      |
| `docker/nginx.conf`  | Nginx反向代理配置 |
| `.env.production`    | 生产环境变量示例  |

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装 Docker 和 Docker Compose
# https://docs.docker.com/get-docker/

# 克隆项目
git clone <repository-url>
cd agentfoundry-ziko
```

### 2. 配置环境变量

```bash
# 复制环境变量文件
cp .env.production .env

# 编辑环境变量
vim .env
```

**必需配置**：

```env
NEXT_PUBLIC_API_URL=https://your-api-server.com
NEXT_PUBLIC_V1_API_URL=https://your-v1-api-server.com
NEXT_PUBLIC_ZIKO_API_URL=https://your-ziko-api-server.com
```

### 3. 构建和启动

```bash
# 基础服务启动
docker-compose up -d

# 包含 Nginx 反向代理
docker-compose --profile with-nginx up -d

# 包含 Redis 缓存服务
docker-compose --profile with-redis up -d

# 完整服务栈
docker-compose --profile with-nginx --profile with-redis up -d
```

### 4. 验证部署

```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f agentfoundry-web

# 健康检查
curl http://localhost:3302/api/health
```

## 🔧 高级配置

### 自定义端口

```bash
# 修改 .env 文件
WEB_PORT=8080
NGINX_PORT=80
NGINX_SSL_PORT=443
```

### SSL/HTTPS 配置

1. 放置证书文件到 `docker/ssl/` 目录
2. 修改 `docker/nginx.conf` 中的 SSL 配置
3. 取消注释相关配置行

### 性能优化

**内存配置**：

```yaml
services:
  agentfoundry-web:
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
```

**并发配置**：

```yaml
services:
  agentfoundry-web:
    deploy:
      replicas: 2
```

## 🛠️ 开发与调试

### 开发模式启动

```bash
# 挂载源码进行开发
docker-compose -f docker-compose.dev.yml up
```

### 查看日志

```bash
# 实时日志
docker-compose logs -f

# 特定服务日志
docker-compose logs -f agentfoundry-web
docker-compose logs -f nginx
```

### 进入容器调试

```bash
# 进入应用容器
docker-compose exec agentfoundry-web sh

# 进入 Nginx 容器
docker-compose exec nginx sh
```

## 📊 监控与维护

### 健康检查

应用内置健康检查端点：

- HTTP: `GET /api/health`
- 检查间隔: 30s
- 超时时间: 10s
- 重试次数: 3次

### 日志管理

```bash
# 限制日志大小
services:
  agentfoundry-web:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### 备份与恢复

```bash
# 导出镜像
docker save agentfoundry-ziko_agentfoundry-web > agentfoundry-web.tar

# 导入镜像
docker load < agentfoundry-web.tar
```

## 🔒 安全配置

### 防火墙配置

```bash
# 仅开放必要端口
ufw allow 80/tcp
ufw allow 443/tcp
ufw deny 3000/tcp  # 应用端口不对外暴露
```

### 容器安全

- 使用非 root 用户运行应用
- 最小化系统依赖
- 定期更新基础镜像

## 🚨 故障排除

### 常见问题

**1. Canvas 依赖错误**

```bash
# 检查系统依赖安装
docker-compose exec agentfoundry-web dpkg -l | grep cairo
```

**2. 构建时网络超时**

```bash
# 设置构建超时
docker-compose build --build-arg BUILDKIT_INLINE_CACHE=1
```

**3. 环境变量未生效**

```bash
# 检查环境变量
docker-compose exec agentfoundry-web env | grep NEXT_PUBLIC
```

### 重新构建

```bash
# 清理重建
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

## 📈 生产环境优化

### 资源限制

```yaml
services:
  agentfoundry-web:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

### 自动重启策略

```yaml
services:
  agentfoundry-web:
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
```

## 🔗 相关链接

- [Next.js 部署文档](https://nextjs.org/docs/deployment)
- [Docker 最佳实践](https://docs.docker.com/develop/dev-best-practices/)
- [Nginx 配置指南](https://nginx.org/en/docs/)
