'use client';

import { AuthConfig, UserProfile } from '@/types/auth';
import Keycloak from 'keycloak-js';

export let keycloakInstance: Keycloak | null = null;

export function initializeKeycloak(
  config: AuthConfig,
  callback?: () => void
): Promise<boolean> {
  console.log('🔐 开始初始化Keycloak');

  return new Promise((resolve, reject) => {
    keycloakInstance = new Keycloak({
      url: config.Authority,
      realm: config.Realm,
      clientId: config.ClientId,
    });
    // 检测是否从登录重定向回来
    const urlParams = new URLSearchParams(window.location.search);
    const hasKeycloakParams =
      urlParams.has('code') || urlParams.has('session_state');

    if (hasKeycloakParams) {
      console.log('🔐 检测到Keycloak重定向参数，使用login-required模式');
    }

    // Token 过期处理
    if (!keycloakInstance) reject(new Error('Keycloak实例未初始化'));

    const loadUserProfile = (keycloakInstance: Keycloak) => {
      if (!keycloakInstance.authenticated) {
        console.log('🔐 用户未认证');
        reject(new Error('用户未认证'));
      }

      keycloakInstance.onTokenExpired = () => {
        keycloakInstance
          ?.updateToken(90)
          .then(refreshed => {
            if (refreshed) {
              console.log('🔐 Token successfully refreshed');
              resolve(true);
            } else {
              console.warn(
                '🔐 Token not refreshed, valid for existing duration'
              );
              resolve(false);
            }
          })
          .catch(error => {
            console.error('🔐 Failed to refresh token', error);
            reject(error);
          });
      };

      console.log('🔐 用户已认证，加载用户信息');
      // 获取用户信息
      keycloakInstance
        .loadUserProfile()
        .then(profile => {
          console.log('🔐 用户信息加载成功:', profile);
          localStorage.setItem('userStatus', 'login');

          if (callback) {
            callback();
          }
          resolve(true);
        })
        .catch(error => {
          console.error('🔐 加载用户信息失败:', error);
          reject(error);
        });
    };

    keycloakInstance
      .init({
        onLoad: hasKeycloakParams ? 'login-required' : 'check-sso',
        checkLoginIframe: false,
        scope: 'openid profile email organization',
      })
      .then(authenticated => {
        console.log('🔐 Keycloak初始化完成，认证状态:', authenticated);
        if (!authenticated) {
          reject(new Error('用户认证失败'));
          return;
        }
        keycloakInstance && loadUserProfile(keycloakInstance);
      })
      .catch(error => {
        console.error('🔐 Keycloak初始化错误:', error);
        reject(error);
      });
  });
}

export function triggerLogin(): void {
  console.log('🔐 开始Keycloak登录流程');

  if (keycloakInstance) {
    console.log('🔐 Keycloak实例存在，执行登录');
    const currentUrl = window.location.href.split('?')[0]; // 移除查询参数
    console.log('🔐 重定向URL:', currentUrl);

    keycloakInstance
      .login({
        redirectUri: currentUrl,
      })
      .catch(error => {
        console.error('🔐 Keycloak登录错误:', error);
      });
  } else {
    console.error('🔐 Keycloak实例未初始化');
  }
}

export function triggerLogout(redirectUri?: string): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('user');
    localStorage.removeItem('userStatus');

    if (keycloakInstance) {
      keycloakInstance.logout({ redirectUri }).catch(error => {
        console.error('Keycloak logout error:', error);
      });
    }
  }
}

export function getToken(): string | undefined {
  return keycloakInstance?.token;
}

export function isAuthenticated(): boolean {
  return keycloakInstance?.authenticated ?? false;
}

export function getUserProfile(): UserProfile | null {
  if (keycloakInstance?.profile) {
    return {
      id: keycloakInstance.profile.id,
      username: keycloakInstance.profile.username,
      firstName: keycloakInstance.profile.firstName,
      lastName: keycloakInstance.profile.lastName,
      email: keycloakInstance.profile.email,
      emailVerified: keycloakInstance.profile.emailVerified,
      attributes: keycloakInstance.profile.attributes,
    };
  }
  return null;
}
