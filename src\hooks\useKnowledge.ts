'use client';

/**
 * 知识库专用Hooks
 * 基于ahooks的useRequest，集成项目的Toast系统和错误处理机制
 * 为组件提供便捷的知识库操作和状态管理接口
 */

import { useCallback, useMemo } from 'react';
import { useRequest } from 'ahooks';
import toast from 'react-hot-toast';
import {
  getKnowledgeList,
  createKnowledge,
  updateKnowledge,
  deleteKnowledge,
  getKnowledgeDetail,
  getKnowledgeStats,
  performRetrieval,
  testRetrieval,
  setKnowledgePermissions
} from '@/services/knowledgeService';
import {
  getDocumentList,
  uploadDocuments,
  deleteDocument,
  reprocessDocument,
  getChunkList,
  updateChunk,
  batchOperateChunks
} from '@/services/documentService';
import {
  KnowledgeListParams,
  KnowledgeCreateParams,
  KnowledgeUpdateParams,
  RetrievalParams,
  PermissionSetParams,
  DocumentListParams,
  UploadFileParams,
  ChunkListParams,
  ChunkUpdateParams,
  ChunkBatchOperationParams
} from '@/types/knowledge-api';
import {
  IKnowledge,
  RetrievalResult
} from '@/types/knowledge';
import {
  KnowledgePermission
} from '@/types/knowledge-enums';

// ==================== 知识库管理相关Hooks ====================

/**
 * 知识库列表管理Hook
 * 提供知识库列表的完整功能，包括加载、搜索、筛选、排序等
 */
export function useKnowledgeList(initialParams: KnowledgeListParams = {}) {
  const {
    data: response,
    loading,
    error,
    refresh,
    mutate,
    run: loadKnowledges
  } = useRequest(
    (params: KnowledgeListParams = initialParams) => getKnowledgeList(params),
    {
      defaultParams: [initialParams],
      refreshDeps: [],
      onError: (error) => {
        toast.error(`获取知识库列表失败: ${error.message}`);
      }
    }
  );

  const knowledges = useMemo(() => {
    if (!response?.data) return [];
    // 根据新的API响应格式处理数据
    const rawData = response.data.kbs || [];
    return rawData.map((kb: any) => ({
      ...kb,
      // 映射字段以保持向后兼容
      doc_num: kb.document_count || 0,
      chunk_num: kb.chunk_count || 0,
      status: 'active', // API未返回status，设为默认值
      similarity_threshold: 0.8, // 设为默认值
      vector_similarity_weight: 0.3, // 设为默认值
      create_time: new Date(kb.create_time).getTime(),
      update_time: new Date(kb.update_time).getTime()
    }));
  }, [response?.data]);
  
  const hasMore = useMemo(() => {
    if (!response?.data) return false;
    const pageSize = 10; // 默认页大小
    return response.data.kbs?.length === pageSize;
  }, [response?.data]);
  
  const total = useMemo(() => response?.data?.total || 0, [response?.data?.total]);

  // 分页加载更多
  const loadMore = useCallback(async () => {
    if (!hasMore || loading) return;
    
    const nextPage = Math.floor(knowledges.length / 10) + 1;
    const moreData = await getKnowledgeList({
      ...initialParams,
      page: nextPage
    });
    
    if (moreData?.data) {
      mutate({
        ...response!,
        data: {
          kbs: [
            ...(response?.data?.kbs || []),
            ...(moreData.data.kbs || [])
          ],
          total: moreData.data.total
        }
      });
    }
  }, [hasMore, loading, response, knowledges, initialParams, mutate]);

  return {
    knowledges,
    loading,
    error,
    hasMore,
    total,
    refresh,
    loadMore,
    loadKnowledges
  };
}

/**
 * 知识库CRUD操作Hook
 * 提供知识库的创建、更新、删除等操作，集成用户反馈
 */
export function useKnowledgeCRUD() {
  // 创建知识库
  const {
    loading: isCreating,
    run: createKnowledgeRun
  } = useRequest(createKnowledge, {
    manual: true,
    onSuccess: (result, [params]) => {
      toast.success(`知识库 "${params.name}" 创建成功`);
    },
    onError: (error) => {
      toast.error(`创建失败: ${error.message}`);
    }
  });

  // 更新知识库
  const {
    loading: isUpdating,
    run: updateKnowledgeRun
  } = useRequest(
    (id: string, params: KnowledgeUpdateParams) => updateKnowledge(id, params),
    {
      manual: true,
      onSuccess: (result, [id, params]) => {
        toast.success(`知识库${params.name ? ` "${params.name}"` : ''} 更新成功`);
      },
      onError: (error) => {
        toast.error(`更新失败: ${error.message}`);
      }
    }
  );

  // 删除知识库
  const {
    loading: isDeleting,
    run: deleteKnowledgeRun
  } = useRequest(deleteKnowledge, {
    manual: true,
    onSuccess: () => {
      toast.success('知识库删除成功');
    },
    onError: (error) => {
      toast.error(`删除失败: ${error.message}`);
    }
  });

  // 批量删除
  const handleBatchDelete = useCallback(async (ids: string[], names?: string[]) => {
    try {
      const results = await Promise.allSettled(
        ids.map(id => deleteKnowledge(id))
      );
      
      const successCount = results.filter(result => 
        result.status === 'fulfilled' && result.value?.success
      ).length;
      
      const failCount = results.length - successCount;
      
      if (successCount > 0) {
        toast.success(`成功删除 ${successCount} 个知识库`);
      }
      if (failCount > 0) {
        toast.error(`${failCount} 个知识库删除失败`);
      }
      
      return { successCount, failCount };
    } catch (error: any) {
      toast.error(`批量删除失败: ${error.message}`);
      throw error;
    }
  }, []);

  return {
    isCreating,
    isUpdating,
    isDeleting,
    createKnowledge: createKnowledgeRun,
    updateKnowledge: updateKnowledgeRun,
    deleteKnowledge: deleteKnowledgeRun,
    handleBatchDelete
  };
}

/**
 * 知识库详情管理Hook
 * 管理当前选中的知识库详情和相关操作
 */
export function useKnowledgeDetail(knowledgeId?: string) {
  // 获取知识库详情
  const {
    data: knowledgeResponse,
    loading,
    error,
    refresh: refreshDetail
  } = useRequest(
    () => getKnowledgeDetail(knowledgeId!),
    {
      ready: !!knowledgeId,
      refreshDeps: [knowledgeId],
      onError: (error) => {
        toast.error(`获取知识库详情失败: ${error.message}`);
      }
    }
  );

  // 获取知识库统计信息
  const {
    data: statsResponse,
    loading: statsLoading,
    refresh: refreshStats
  } = useRequest(
    () => getKnowledgeStats(knowledgeId!),
    {
      ready: !!knowledgeId,
      refreshDeps: [knowledgeId],
      onError: (error) => {
        console.warn(`获取知识库统计失败: ${error.message}`);
      }
    }
  );

  const currentKnowledge = useMemo(() => knowledgeResponse?.data, [knowledgeResponse?.data]);
  const stats = useMemo(() => statsResponse?.data, [statsResponse?.data]);

  const refresh = useCallback(() => {
    refreshDetail();
    refreshStats();
  }, [refreshDetail, refreshStats]);

  const canEdit = useMemo(() => {
    if (!currentKnowledge) return false;
    // 这里可以添加权限检查逻辑
    return true;
  }, [currentKnowledge]);

  return {
    currentKnowledge,
    stats,
    loading,
    statsLoading,
    error,
    isLoaded: !!currentKnowledge,
    canEdit,
    refresh,
    refreshDetail,
    refreshStats
  };
}

// ==================== 知识检索相关Hooks ====================

/**
 * 知识库检索Hook
 * 提供知识检索功能，支持设置保存和结果管理
 */
export function useKnowledgeRetrieval() {
  // 执行检索
  const {
    data: retrievalResponse,
    loading,
    error,
    run: performRetrievalRun,
    cancel
  } = useRequest(performRetrieval, {
    manual: true,
    onError: (error) => {
      toast.error(`检索失败: ${error.message}`);
    }
  });

  // 测试检索
  const {
    data: testResponse,
    loading: testLoading,
    run: testRetrievalRun
  } = useRequest(testRetrieval, {
    manual: true,
    onError: (error) => {
      toast.error(`检索测试失败: ${error.message}`);
    }
  });

  const results = useMemo(() => retrievalResponse?.data || [], [retrievalResponse?.data]);
  const testResults = useMemo(() => testResponse?.data || [], [testResponse?.data]);

  const clearResults = useCallback(() => {
    // 由于使用useRequest，可以通过取消请求来清理状态
    cancel();
  }, [cancel]);

  return {
    results,
    testResults,
    loading,
    testLoading,
    error,
    performRetrieval: performRetrievalRun,
    testRetrieval: testRetrievalRun,
    clearResults,
    hasResults: Array.isArray(results) ? results.length > 0 : false
  };
}

// ==================== 权限管理相关Hooks ====================

/**
 * 知识库权限管理Hook
 * 处理知识库的权限设置和检查
 */
export function useKnowledgePermissions() {
  const {
    loading,
    run: setPermissions
  } = useRequest(
    (knowledgeId: string, params: PermissionSetParams) => 
      setKnowledgePermissions(knowledgeId, params),
    {
      manual: true,
      onSuccess: () => {
        toast.success('权限设置成功');
      },
      onError: (error) => {
        toast.error(`权限设置失败: ${error.message}`);
      }
    }
  );

  return {
    loading,
    setPermissions
  };
}

// ==================== 文档管理相关Hooks ====================

/**
 * 文档列表管理Hook
 * 管理知识库下的文档列表
 */
export function useDocumentList(knowledgeId?: string, initialParams: Partial<DocumentListParams> = {}) {
  const {
    data: response,
    loading,
    error,
    refresh,
    mutate,
    run: loadDocuments
  } = useRequest(
    (params: Partial<DocumentListParams> = initialParams) => 
      getDocumentList(knowledgeId!, { kb_id: knowledgeId!, ...params }),
    {
      ready: !!knowledgeId,
      defaultParams: [initialParams],
      refreshDeps: [knowledgeId, initialParams],
      onError: (error) => {
        toast.error(`获取文档列表失败: ${error.message}`);
      }
    }
  );

  const documents = useMemo(() => {
    if (!response?.data) return [];
    // 根据新的API响应格式处理数据
    const rawDocs = response.data.docs || [];
    return rawDocs.map((doc: any) => ({
      ...doc,
      // 映射字段以保持向后兼容
      chunk_num: doc.chunks_count || 0,
      progress: 100, // 默认设为完成状态
      status: doc.status || 'completed',
      create_time: new Date(doc.create_time).getTime(),
      update_time: new Date(doc.update_time).getTime()
    }));
  }, [response?.data]);
  
  const hasMore = useMemo(() => {
    if (!response?.data) return false;
    const pageSize = 20; // 文档列表默认页大小
    return response.data.docs?.length === pageSize;
  }, [response?.data]);
  
  const total = useMemo(() => response?.data?.total || 0, [response?.data?.total]);

  return {
    documents,
    loading,
    error,
    hasMore,
    total,
    refresh,
    loadDocuments
  };
}

/**
 * 文档上传管理Hook
 * 处理文件上传、进度显示、错误处理
 */
export function useDocumentUpload(knowledgeId?: string) {
  const {
    loading: isUploading,
    run: uploadFiles
  } = useRequest(
    (params: UploadFileParams) => uploadDocuments(knowledgeId!, params),
    {
      manual: true,
      onSuccess: (result, [params]) => {
        toast.success(`成功上传 ${params.files.length} 个文件`);
      },
      onError: (error) => {
        toast.error(`上传失败: ${error.message}`);
      }
    }
  );

  const handleUpload = useCallback(async (
    files: File[], 
    options?: {
      runType?: 'immediate' | 'manual';
      parserConfig?: any;
      onProgress?: (progress: number) => void;
    }
  ) => {
    if (!knowledgeId) {
      toast.error('请先选择知识库');
      return false;
    }

    return await uploadFiles({
      files,
      run_type: options?.runType || 'immediate',
      parser_config: options?.parserConfig,
      onProgress: options?.onProgress
    });
  }, [knowledgeId, uploadFiles]);

  return {
    isUploading,
    handleUpload,
    canUpload: !!knowledgeId && !isUploading
  };
}

// ==================== 知识块管理相关Hooks ====================

/**
 * 知识块列表管理Hook
 * 管理文档的知识块
 */
export function useChunkList(knowledgeId?: string, initialParams: Partial<ChunkListParams> = {}) {
  const {
    data: response,
    loading,
    error,
    refresh,
    run: loadChunks
  } = useRequest(
    (params: Partial<ChunkListParams> = initialParams) => 
      getChunkList(knowledgeId!, { kb_id: knowledgeId!, ...params }),
    {
      ready: !!knowledgeId,
      defaultParams: [initialParams],
      refreshDeps: [knowledgeId, initialParams],
      onError: (error) => {
        toast.error(`获取知识块列表失败: ${error.message}`);
      }
    }
  );

  const chunks = useMemo(() => response?.data || [], [response?.data]);
  const hasMore = useMemo(() => response?.pagination?.hasMore || false, [response?.pagination?.hasMore]);
  const total = useMemo(() => response?.pagination?.total || 0, [response?.pagination?.total]);

  // 统计信息
  const statistics = useMemo(() => {
    const enabled = chunks.filter(chunk => chunk.available_int === 1).length;
    const disabled = chunks.filter(chunk => chunk.available_int === 0).length;
    
    return {
      total: chunks.length,
      enabled,
      disabled,
      enabledPercent: chunks.length > 0 ? Math.round((enabled / chunks.length) * 100) : 0
    };
  }, [chunks]);

  return {
    chunks,
    loading,
    error,
    hasMore,
    total,
    statistics,
    refresh,
    loadChunks
  };
}

/**
 * 知识块批量操作Hook
 * 处理知识块的批量启用、禁用、删除等操作
 */
export function useChunkBatchActions(knowledgeId?: string) {
  const {
    loading: isBatchOperating,
    run: batchOperate
  } = useRequest(
    (params: ChunkBatchOperationParams) => 
      batchOperateChunks(knowledgeId!, params),
    {
      manual: true,
      onSuccess: (result, [params]) => {
        const actionMap: Record<string, string> = {
          enable: '启用',
          disable: '禁用',
          delete: '删除'
        };
        const actionName = actionMap[params.action] || params.action;
        toast.success(`成功${actionName} ${params.chunk_ids.length} 个知识块`);
      },
      onError: (error, [params]) => {
        const actionMap: Record<string, string> = {
          enable: '启用',
          disable: '禁用', 
          delete: '删除'
        };
        const actionName = actionMap[params.action] || params.action;
        toast.error(`批量${actionName}失败: ${error.message}`);
      }
    }
  );

  // 批量启用
  const handleBatchEnable = useCallback(async (chunkIds: string[]) => {
    if (!knowledgeId || chunkIds.length === 0) return;
    
    return await batchOperate({
      action: 'enable',
      ids: chunkIds
    } as any);
  }, [knowledgeId, batchOperate]);

  // 批量禁用
  const handleBatchDisable = useCallback(async (chunkIds: string[]) => {
    if (!knowledgeId || chunkIds.length === 0) return;
    
    return await batchOperate({
      action: 'disable',
      ids: chunkIds
    } as any);
  }, [knowledgeId, batchOperate]);

  // 批量删除
  const handleBatchDelete = useCallback(async (chunkIds: string[]) => {
    if (!knowledgeId || chunkIds.length === 0) return;
    
    return await batchOperate({
      action: 'delete',
      ids: chunkIds
    } as any);
  }, [knowledgeId, batchOperate]);

  return {
    isBatchOperating,
    handleBatchEnable,
    handleBatchDisable,
    handleBatchDelete
  };
}