'use client';

import { 
  ChatState, 
  ChatStateData, 
  ChatMessage,
  ChatEngine,
  EventBus
} from '@/lib/chat-engine/types';
import { chatEngine } from '@/lib/chat-engine/chat-engine';

/**
 * ChatEngine适配器
 * 负责连接ChatEngine和ChatStore，保持ChatEngine的纯净性
 * 处理数据格式转换和状态映射
 */

// 会话级别状态接口（与ChatStore保持一致）
export interface ConversationState {
  chatState: ChatState;
  isSending: boolean;
  isReceiving: boolean;
  isStreaming: boolean;
  error: string | null;
  streamingMessageId: string | null;
}

// 适配器状态接口
export interface AdapterState {
  // ChatEngine状态数据
  engineState: ChatStateData;
  // 会话级别状态映射
  conversationStates: Record<string, ConversationState>;
  // 连接状态
  isConnected: boolean;
  // 初始化状态
  isInitialized: boolean;
}

// 适配器事件回调
export type AdapterStateListener = (state: AdapterState) => void;

/**
 * ChatEngine适配器实现
 */
export class ChatEngineAdapter {
  private engine: ChatEngine;
  private eventBus: EventBus;
  private listeners = new Set<AdapterStateListener>();
  private state: AdapterState;
  private enableLogging = false;

  constructor(options?: { enableLogging?: boolean }) {
    this.engine = chatEngine;
    this.eventBus = this.engine.getEventBus();
    this.enableLogging = options?.enableLogging ?? false;
    
    // 初始化状态
    this.state = {
      engineState: this.engine.getStateMachine().getStateData(),
      conversationStates: {},
      isConnected: false,
      isInitialized: false
    };

    this.setupEventListeners();
  }

  /**
   * 初始化适配器
   */
  async initialize(config: { agentId: string; conversationId?: string }): Promise<void> {
    if (this.state.isInitialized) {
      console.warn('[ChatEngineAdapter] Already initialized');
      return;
    }

    // 初始化ChatEngine
    await this.engine.initialize({
      agentId: config.agentId,
      conversationId: config.conversationId || '',
      enableLogging: this.enableLogging
    });

    this.state.isInitialized = true;
    this.state.isConnected = true;

    if (this.enableLogging) {
      console.log('[ChatEngineAdapter] Initialized successfully', config);
    }

    this.notifyListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听StateMachine状态变化
    this.engine.getStateMachine().subscribe((stateData) => {
      this.handleStateMachineUpdate(stateData);
    });

    // 监听连接状态变化
    this.eventBus.on('CONNECTION_STATUS', (event) => {
      const payload = event.payload as any;
      this.state = {
        ...this.state,
        isConnected: payload.status === 'connected'
      };
      this.notifyListeners();
    });

    // 设置AGUI事件监听器
    this.setupAGUIEventListeners();
  }

  /**
   * 处理StateMachine状态更新
   */
  private handleStateMachineUpdate(stateData: ChatStateData): void {
    // 更新引擎状态
    this.state = {
      ...this.state,
      engineState: stateData
    };

    // 更新会话级别状态
    if (stateData.conversationId) {
      this.updateConversationState(stateData.conversationId, stateData);
    }

    this.notifyListeners();
  }

  /**
   * 更新会话状态
   */
  private updateConversationState(conversationId: string, stateData: ChatStateData): void {
    const conversationState: ConversationState = {
      chatState: stateData.currentState,
      isSending: stateData.currentState === ChatState.SENDING,
      isReceiving: stateData.currentState === ChatState.RECEIVING,
      isStreaming: stateData.currentState === ChatState.STREAMING,
      error: stateData.error,
      streamingMessageId: stateData.streamingMessageId
    };

    this.state = {
      ...this.state,
      conversationStates: {
        ...this.state.conversationStates,
        [conversationId]: conversationState
      }
    };
  }

  /**
   * 发送消息
   */
  sendMessage(conversationId: string, content: string): void {
    if (!conversationId || !content.trim()) {
      console.warn('[ChatEngineAdapter] Cannot send: missing conversationId or content');
      return;
    }

    // 设置发送状态
    this.updateConversationSendingState(conversationId, true);

    // 通过EventBus发送消息
    this.eventBus.emit({
      type: 'USER_SEND_MESSAGE',
      payload: {
        content: content.trim(),
        conversationId
      },
      timestamp: Date.now(),
      source: 'ChatEngineAdapter'
    });
  }

  /**
   * 更新会话发送状态
   */
  private updateConversationSendingState(conversationId: string, isSending: boolean): void {
    const currentState = this.state.conversationStates[conversationId] || this.getDefaultConversationState();

    this.state = {
      ...this.state,
      conversationStates: {
        ...this.state.conversationStates,
        [conversationId]: {
          ...currentState,
          isSending,
          chatState: isSending ? ChatState.SENDING : ChatState.IDLE
        }
      }
    };

    this.notifyListeners();
  }

  /**
   * 更新输入内容
   */
  updateInput(conversationId: string, content: string): void {
    if (!conversationId) {
      console.warn('[ChatEngineAdapter] Cannot update input: missing conversationId');
      return;
    }

    this.eventBus.emit({
      type: 'USER_TYPING',
      payload: {
        content,
        conversationId
      },
      timestamp: Date.now(),
      source: 'ChatEngineAdapter'
    });
  }

  /**
   * 切换会话
   */
  async switchConversation(conversationId: string): Promise<void> {
    // 通知ChatEngine会话切换
    this.eventBus.emit({
      type: 'CONVERSATION_SWITCHED',
      payload: { conversationId },
      timestamp: Date.now(),
      source: 'ChatEngineAdapter'
    });

    // 加载消息
    try {
      await this.engine.getMessageService().loadMessages(conversationId);
    } catch (error) {
      console.error('[ChatEngineAdapter] Failed to load messages:', error);
    }
  }

  /**
   * 加载消息
   */
  async loadMessages(conversationId: string): Promise<void> {
    try {
      await this.engine.getMessageService().loadMessages(conversationId);
    } catch (error) {
      console.error('[ChatEngineAdapter] Failed to load messages:', error);
      throw error;
    }
  }

  /**
   * 获取当前状态
   */
  getState(): AdapterState {
    return { ...this.state };
  }

  /**
   * 获取会话状态
   */
  getConversationState(conversationId: string): ConversationState | null {
    return this.state.conversationStates[conversationId] || null;
  }

  /**
   * 订阅状态变化
   */
  subscribe(listener: AdapterStateListener): () => void {
    this.listeners.add(listener);
    
    // 立即通知当前状态
    listener(this.getState());
    
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(): void {
    const state = this.getState();
    
    if (this.enableLogging) {
      console.log('[ChatEngineAdapter] State updated', {
        messagesCount: state.engineState.messages.length,
        conversationId: state.engineState.conversationId,
        currentState: state.engineState.currentState,
        listenersCount: this.listeners.size
      });
    }

    this.listeners.forEach(listener => {
      try {
        listener(state);
      } catch (error) {
        console.error('[ChatEngineAdapter] Error in listener:', error);
      }
    });
  }

  /**
   * 设置AGUI事件监听器
   */
  private setupAGUIEventListeners(): void {
    if (this.enableLogging) {
      console.log('[ChatEngineAdapter] 设置AGUI事件监听器');
    }
    
    // 监听流式消息事件
    this.eventBus.on('STREAMING_START', (event) => {
      this.handleStreamingStart(event);
    });
    
    this.eventBus.on('STREAMING_END', (event) => {
      this.handleStreamingEnd(event);
    });
    
    // 监听运行生命周期事件
    this.eventBus.on('RUN_STARTED', (event) => {
      this.handleRunStarted(event);
    });
    
    this.eventBus.on('RUN_FINISHED', (event) => {
      this.handleRunFinished(event);
    });
  }

  // 简化的AGUI事件处理方法
  
  /**
   * 处理流式消息开始
   */
  private handleStreamingStart(event: any): void {
    const { conversationId } = event.payload;
    
    if (conversationId) {
      this.updateConversationStreamingState(conversationId, true);
    }
  }
  
  /**
   * 处理流式消息结束
   */
  private handleStreamingEnd(event: any): void {
    const { conversationId } = event.payload;
    
    if (conversationId) {
      this.updateConversationStreamingState(conversationId, false);
    }
  }
  
  /**
   * 处理运行开始
   */
  private handleRunStarted(event: any): void {
    const { conversationId } = event.payload;
    
    if (conversationId) {
      this.updateConversationReceivingState(conversationId, true);
    }
  }
  
  /**
   * 处理运行结束
   */
  private handleRunFinished(event: any): void {
    const { conversationId } = event.payload;
    
    if (conversationId) {
      this.updateConversationReceivingState(conversationId, false);
      this.updateConversationSendingState(conversationId, false);
    }
  }

  /**
   * 更新会话流式状态
   */
  private updateConversationStreamingState(conversationId: string, isStreaming: boolean): void {
    const currentState = this.state.conversationStates[conversationId] || this.getDefaultConversationState();
    
    this.state = {
      ...this.state,
      conversationStates: {
        ...this.state.conversationStates,
        [conversationId]: {
          ...currentState,
          isStreaming,
          chatState: isStreaming ? ChatState.STREAMING : ChatState.IDLE
        }
      }
    };
    
    this.notifyListeners();
  }

  /**
   * 更新会话接收状态
   */
  private updateConversationReceivingState(conversationId: string, isReceiving: boolean): void {
    const currentState = this.state.conversationStates[conversationId] || this.getDefaultConversationState();
    
    this.state = {
      ...this.state,
      conversationStates: {
        ...this.state.conversationStates,
        [conversationId]: {
          ...currentState,
          isReceiving,
          chatState: isReceiving ? ChatState.RECEIVING : ChatState.IDLE
        }
      }
    };
    
    this.notifyListeners();
  }

  /**
   * 获取默认会话状态
   */
  private getDefaultConversationState(): ConversationState {
    return {
      chatState: ChatState.IDLE,
      isSending: false,
      isReceiving: false,
      isStreaming: false,
      error: null,
      streamingMessageId: null
    };
  }

  /**
   * 清除错误
   */
  clearError(conversationId?: string): void {
    if (conversationId && this.state.conversationStates[conversationId]) {
      const updatedConversationState = {
        ...this.state.conversationStates[conversationId],
        error: null
      };
      
      this.state = {
        ...this.state,
        conversationStates: {
          ...this.state.conversationStates,
          [conversationId]: updatedConversationState
        }
      };
      
      this.notifyListeners();
    }
    
    this.engine.getStateMachine().clearError();
  }

  /**
   * 重置状态
   */
  reset(): void {
    this.state = {
      ...this.state,
      conversationStates: {}
    };
    this.engine.getStateMachine().reset();
  }

  /**
   * 获取EventBus（向后兼容）
   */
  getEventBus(): EventBus {
    return this.eventBus;
  }

  /**
   * 销毁适配器
   */
  async destroy(): Promise<void> {
    this.listeners.clear();
    await this.engine.destroy();
    
    if (this.enableLogging) {
      console.log('[ChatEngineAdapter] Destroyed');
    }
  }
}

// 创建全局适配器实例
export const chatEngineAdapter = new ChatEngineAdapter({
  enableLogging: process.env.NODE_ENV === 'development'
});