{"name": "agentfoundry-ziko", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3303", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "prepare": "husky"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{js,jsx,ts,tsx,json,css,md}": ["prettier --write"]}, "dependencies": {"@ant-design/cssinjs": "^1.24.0", "@ant-design/icons": "^5.6.1", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@antv/g6": "^5.0.49", "@microsoft/signalr": "^8.0.7", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/dompurify": "^3.0.5", "@types/uuid": "^10.0.0", "ahooks": "^3.9.0", "antd": "^5.26.5", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "eventsource-parser": "^3.0.3", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "immer": "^10.1.1", "input-otp": "^1.4.2", "keycloak-js": "24.0.1", "less": "^4.4.0", "less-loader": "^12.3.0", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "next": "15.3.5", "rc-tween-one": "^3.0.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.6.0", "react-infinite-scroll-component": "^6.1.0", "react-pdf-highlighter": "^6.1.0", "react-string-replace": "^1.1.1", "tailwind-merge": "^3.3.1", "umi-request": "^1.4.0", "uuid": "^11.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "critters": "^0.0.23", "css-loader": "^7.1.2", "eslint": "^9", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "style-loader": "^4.0.0", "tailwindcss": "^4", "typescript": "^5"}}