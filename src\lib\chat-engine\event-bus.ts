'use client';

import { ChatEvent, EventBus, EventListener } from './types';

/**
 * 事件总线实现
 * 提供完全解耦的事件通信机制
 */
export class ChatEventBus implements EventBus {
  private listeners = new Map<string, Set<EventListener>>();
  private onceListeners = new Map<string, Set<EventListener>>();
  private eventHistory: ChatEvent[] = [];
  private maxHistorySize = 1000;
  private enableLogging = false;

  constructor(options?: { enableLogging?: boolean; maxHistorySize?: number }) {
    this.enableLogging = options?.enableLogging ?? false;
    this.maxHistorySize = options?.maxHistorySize ?? 1000;
  }

  /**
   * 发射事件
   */
  emit<T extends ChatEvent>(event: T): void {
    if (this.enableLogging) {
      console.log(`[EventBus] Emitting event: ${event.type}`, event);
    }

    // 记录事件历史
    this.eventHistory.push(event);
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }

    // 触发常规监听器
    const listeners = this.listeners.get(event.type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error(`[EventBus] Error in listener for ${event.type}:`, error);
        }
      });
    }

    // 触发一次性监听器
    const onceListeners = this.onceListeners.get(event.type);
    if (onceListeners) {
      onceListeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error(`[EventBus] Error in once listener for ${event.type}:`, error);
        }
      });
      // 清空一次性监听器
      this.onceListeners.delete(event.type);
    }
  }

  /**
   * 监听事件
   */
  on<T extends ChatEvent>(eventType: string, listener: EventListener<T>): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    
    const listeners = this.listeners.get(eventType)!;
    listeners.add(listener as EventListener);

    if (this.enableLogging) {
      console.log(`[EventBus] Added listener for: ${eventType}`);
    }

    // 返回取消监听的函数
    return () => {
      listeners.delete(listener as EventListener);
      if (listeners.size === 0) {
        this.listeners.delete(eventType);
      }
    };
  }

  /**
   * 取消监听事件
   */
  off<T extends ChatEvent>(eventType: string, listener: EventListener<T>): void {
    const listeners = this.listeners.get(eventType);
    if (listeners) {
      listeners.delete(listener as EventListener);
      if (listeners.size === 0) {
        this.listeners.delete(eventType);
      }
    }

    if (this.enableLogging) {
      console.log(`[EventBus] Removed listener for: ${eventType}`);
    }
  }

  /**
   * 监听事件一次
   */
  once<T extends ChatEvent>(eventType: string, listener: EventListener<T>): void {
    if (!this.onceListeners.has(eventType)) {
      this.onceListeners.set(eventType, new Set());
    }
    
    const onceListeners = this.onceListeners.get(eventType)!;
    onceListeners.add(listener as EventListener);

    if (this.enableLogging) {
      console.log(`[EventBus] Added once listener for: ${eventType}`);
    }
  }

  /**
   * 移除所有监听器
   */
  removeAllListeners(eventType?: string): void {
    if (eventType) {
      this.listeners.delete(eventType);
      this.onceListeners.delete(eventType);
    } else {
      this.listeners.clear();
      this.onceListeners.clear();
    }

    if (this.enableLogging) {
      console.log(`[EventBus] Removed all listeners${eventType ? ` for: ${eventType}` : ''}`);
    }
  }

  /**
   * 获取事件历史
   */
  getEventHistory(): ChatEvent[] {
    return [...this.eventHistory];
  }

  /**
   * 清空事件历史
   */
  clearEventHistory(): void {
    this.eventHistory = [];
  }

  /**
   * 获取当前监听器数量
   */
  getListenerCount(eventType?: string): number {
    if (eventType) {
      const regularCount = this.listeners.get(eventType)?.size ?? 0;
      const onceCount = this.onceListeners.get(eventType)?.size ?? 0;
      return regularCount + onceCount;
    }
    
    let total = 0;
    this.listeners.forEach(listeners => {
      total += listeners.size;
    });
    this.onceListeners.forEach(listeners => {
      total += listeners.size;
    });
    return total;
  }

  /**
   * 获取调试信息
   */
  getDebugInfo(): {
    totalListeners: number;
    eventTypes: string[];
    recentEvents: ChatEvent[];
  } {
    const eventTypes = Array.from(new Set([
      ...this.listeners.keys(),
      ...this.onceListeners.keys()
    ]));

    return {
      totalListeners: this.getListenerCount(),
      eventTypes,
      recentEvents: this.eventHistory.slice(-10)
    };
  }

  /**
   * 启用/禁用日志
   */
  setLogging(enabled: boolean): void {
    this.enableLogging = enabled;
  }

  /**
   * 销毁事件总线
   */
  destroy(): void {
    this.removeAllListeners();
    this.clearEventHistory();
    
    if (this.enableLogging) {
      console.log('[EventBus] Destroyed');
    }
  }
}