/**
 * API端点配置
 * 统一管理所有知识库相关的API端点
 * 支持环境变量配置和动态端点构建
 */

// 获取API基础URL
const getApiBaseUrl = (): string => {
  // 优先使用环境变量
  if (typeof window !== 'undefined') {
    // 客户端环境
    return process.env.NEXT_PUBLIC_API_BASE_URL || '';
  } else {
    // 服务端环境
    return process.env.API_BASE_URL || process.env.NEXT_PUBLIC_API_BASE_URL || '';
  }
};

const API_BASE_URL = getApiBaseUrl();

// API版本配置
export const API_VERSION = {
  V1: 'v1',
  V2: 'v2'
} as const;

// 当前使用的API版本
const CURRENT_VERSION = API_VERSION.V1;

// 构建完整的API端点URL
const buildEndpoint = (path: string, version: string = CURRENT_VERSION): string => {
  // 移除路径开头的斜杠，避免重复
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  return `${API_BASE_URL}/${version}/${cleanPath}`;
};

// ==================== 知识库管理端点 ====================

export const knowledgeEndpoints = {
  // 基础CRUD操作
  list: buildEndpoint('kb/list'),
  create: buildEndpoint('kb/create'),
  detail: buildEndpoint('kb/detail'),
  update: buildEndpoint('kb/update'),
  delete: buildEndpoint('kb/rm'),
  
  // 统计和分析
  stats: (id: string) => buildEndpoint(`knowledge/collections/${id}/stats`),
  analytics: (id: string) => buildEndpoint(`knowledge/collections/${id}/analytics`),
  
  // 检索功能
  retrieval: buildEndpoint('knowledge/retrieval'),
  testRetrieval: buildEndpoint('knowledge/test-retrieval'),
  
  // 权限管理
  permissions: (id: string) => buildEndpoint(`knowledge/collections/${id}/permissions`),
  
  permissionManagement: {
    get: (id: string) => buildEndpoint(`knowledge/collections/${id}/permissions`),
    set: (id: string) => buildEndpoint(`knowledge/collections/${id}/permissions`),
    check: (id: string, userId: string) => buildEndpoint(`knowledge/collections/${id}/permissions/check/${userId}`),
    users: (id: string) => buildEndpoint(`knowledge/collections/${id}/permissions/users`),
    groups: (id: string) => buildEndpoint(`knowledge/collections/${id}/permissions/groups`)
  },
  
  // 配置管理
  config: {
    get: (id: string) => buildEndpoint(`knowledge/collections/${id}/config`),
    update: (id: string) => buildEndpoint(`knowledge/collections/${id}/config`),
    reset: (id: string) => buildEndpoint(`knowledge/collections/${id}/config/reset`)
  },
  
  // 操作日志
  logs: (id: string) => buildEndpoint(`knowledge/collections/${id}/logs`),
  
  // 导入导出
  export: (id: string) => buildEndpoint(`knowledge/collections/${id}/export`),
  import: (id: string) => buildEndpoint(`knowledge/collections/${id}/import`),
  
  // 克隆和模板
  clone: (id: string) => buildEndpoint(`knowledge/collections/${id}/clone`),
  template: (id: string) => buildEndpoint(`knowledge/collections/${id}/template`)
} as const;

// ==================== 文档管理端点 ====================

export const documentEndpoints = {
  // 基础操作
  list: buildEndpoint('document/list'),
  detail: (docId: string) => buildEndpoint(`document/get/${docId}`),
  upload: buildEndpoint('document/upload'),
  uploadAndParse: buildEndpoint('document/upload_and_parse'),
  delete: buildEndpoint('document/rm'),
  batchDelete: buildEndpoint('document/batch-rm'),
  
  // 文档处理
  process: (kbId: string, docId: string) => buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/process`),
  reprocess: (kbId: string, docId: string) => buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/reprocess`),
  processStatus: (kbId: string, docId: string) => buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/status`),
  stopProcess: (kbId: string, docId: string) => buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/stop`),
  
  // 知识块管理
  chunks: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/chunks`),
  updateChunk: (kbId: string, chunkId: string) => buildEndpoint(`knowledge/collections/${kbId}/chunks/${chunkId}`),
  deleteChunk: (kbId: string, chunkId: string) => buildEndpoint(`knowledge/collections/${kbId}/chunks/${chunkId}`),
  batchChunks: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/chunks/batch`),
  
  // 文档预览和下载
  preview: (kbId: string, docId: string) => buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/preview`),
  download: (kbId: string, docId: string) => buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/download`),
  thumbnail: (kbId: string, docId: string) => buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/thumbnail`),
  
  // 文档元数据
  metadata: (kbId: string, docId: string) => buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/metadata`),
  updateMetadata: (kbId: string, docId: string) => buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/metadata`),
  
  // 文档版本管理
  versions: (kbId: string, docId: string) => buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/versions`),
  createVersion: (kbId: string, docId: string) => buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/versions`),
  revertVersion: (kbId: string, docId: string, versionId: string) => 
    buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/versions/${versionId}/revert`),
    
  // 文档标签和分类
  tags: (kbId: string, docId: string) => buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/tags`),
  addTag: (kbId: string, docId: string) => buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/tags`),
  removeTag: (kbId: string, docId: string, tagId: string) => 
    buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/tags/${tagId}`)
} as const;

// ==================== 知识块管理端点 ====================

export const chunkEndpoints = {
  // 基础CRUD
  list: (kbId: string, docId?: string) => 
    docId 
      ? buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/chunks`)
      : buildEndpoint(`knowledge/collections/${kbId}/chunks`),
  detail: (chunkId: string) => buildEndpoint(`knowledge/chunks/${chunkId}`),
  create: (kbId: string, docId: string) => buildEndpoint(`knowledge/collections/${kbId}/documents/${docId}/chunks`),
  update: (chunkId: string) => buildEndpoint(`knowledge/chunks/${chunkId}`),
  delete: (chunkId: string) => buildEndpoint(`knowledge/chunks/${chunkId}`),
  
  // 批量操作
  batchUpdate: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/chunks/batch-update`),
  batchDelete: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/chunks/batch-delete`),
  batchEnable: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/chunks/batch-enable`),
  batchDisable: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/chunks/batch-disable`),
  
  // 知识块内容管理
  content: (chunkId: string) => buildEndpoint(`knowledge/chunks/${chunkId}/content`),
  updateContent: (chunkId: string) => buildEndpoint(`knowledge/chunks/${chunkId}/content`),
  
  // 知识块关键词管理
  keywords: (chunkId: string) => buildEndpoint(`knowledge/chunks/${chunkId}/keywords`),
  addKeywords: (chunkId: string) => buildEndpoint(`knowledge/chunks/${chunkId}/keywords`),
  removeKeywords: (chunkId: string) => buildEndpoint(`knowledge/chunks/${chunkId}/keywords/remove`),
  
  // 知识块向量管理
  embeddings: (chunkId: string) => buildEndpoint(`knowledge/chunks/${chunkId}/embeddings`),
  regenerateEmbeddings: (chunkId: string) => buildEndpoint(`knowledge/chunks/${chunkId}/embeddings/regenerate`),
  
  // 知识块关系管理
  relations: (chunkId: string) => buildEndpoint(`knowledge/chunks/${chunkId}/relations`),
  addRelation: (chunkId: string) => buildEndpoint(`knowledge/chunks/${chunkId}/relations`),
  removeRelation: (chunkId: string, relationId: string) => 
    buildEndpoint(`knowledge/chunks/${chunkId}/relations/${relationId}`)
} as const;

// ==================== 检索和搜索端点 ====================

export const searchEndpoints = {
  // 基础检索
  search: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/search`),
  semanticSearch: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/search/semantic`),
  hybridSearch: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/search/hybrid`),
  
  // 检索测试和调试
  test: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/search/test`),
  debug: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/search/debug`),
  explain: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/search/explain`),
  
  // 推荐和相关性
  recommend: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/recommend`),
  similar: (kbId: string, chunkId: string) => buildEndpoint(`knowledge/collections/${kbId}/chunks/${chunkId}/similar`),
  
  // 搜索历史和分析
  history: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/search/history`),
  analytics: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/search/analytics`),
  
  // 搜索配置
  config: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/search/config`),
  updateConfig: (kbId: string) => buildEndpoint(`knowledge/collections/${kbId}/search/config`)
} as const;

// ==================== 任务管理端点 ====================

export const taskEndpoints = {
  // 任务查询
  list: buildEndpoint('tasks'),
  detail: (taskId: string) => buildEndpoint(`tasks/${taskId}`),
  status: (taskId: string) => buildEndpoint(`tasks/${taskId}/status`),
  
  // 任务控制
  cancel: (taskId: string) => buildEndpoint(`tasks/${taskId}/cancel`),
  retry: (taskId: string) => buildEndpoint(`tasks/${taskId}/retry`),
  pause: (taskId: string) => buildEndpoint(`tasks/${taskId}/pause`),
  resume: (taskId: string) => buildEndpoint(`tasks/${taskId}/resume`),
  
  // 任务日志
  logs: (taskId: string) => buildEndpoint(`tasks/${taskId}/logs`),
  progress: (taskId: string) => buildEndpoint(`tasks/${taskId}/progress`),
  
  // 批量任务操作
  batchCancel: buildEndpoint('tasks/batch-cancel'),
  batchRetry: buildEndpoint('tasks/batch-retry'),
  
  // 任务统计
  stats: buildEndpoint('tasks/stats'),
  queue: buildEndpoint('tasks/queue')
} as const;

// ==================== 用户和权限端点 ====================

export const userEndpoints = {
  // 用户信息
  profile: buildEndpoint('users/profile'),
  updateProfile: buildEndpoint('users/profile'),
  
  // 用户权限
  permissions: buildEndpoint('users/permissions'),
  checkPermission: (resource: string, action: string) => 
    buildEndpoint(`users/permissions/check?resource=${resource}&action=${action}`),
  
  // 用户偏好设置
  preferences: buildEndpoint('users/preferences'),
  updatePreferences: buildEndpoint('users/preferences'),
  
  // 用户活动日志
  activities: buildEndpoint('users/activities'),
  loginHistory: buildEndpoint('users/login-history')
} as const;

// ==================== 系统管理端点 ====================

export const systemEndpoints = {
  // 系统状态
  health: buildEndpoint('system/health'),
  version: buildEndpoint('system/version'),
  info: buildEndpoint('system/info'),
  
  // 系统配置
  config: buildEndpoint('system/config'),
  updateConfig: buildEndpoint('system/config'),
  
  // 系统监控
  metrics: buildEndpoint('system/metrics'),
  logs: buildEndpoint('system/logs'),
  
  // 数据备份和恢复
  backup: buildEndpoint('system/backup'),
  restore: buildEndpoint('system/restore'),
  
  // 缓存管理
  cache: {
    status: buildEndpoint('system/cache/status'),
    clear: buildEndpoint('system/cache/clear'),
    refresh: buildEndpoint('system/cache/refresh')
  }
} as const;

// ==================== WebSocket端点 ====================

export const websocketEndpoints = {
  // 实时通知
  notifications: (userId: string) => buildEndpoint(`ws/notifications/${userId}`).replace('http', 'ws'),
  
  // 任务进度推送
  taskProgress: (taskId: string) => buildEndpoint(`ws/tasks/${taskId}/progress`).replace('http', 'ws'),
  
  // 知识库实时更新
  knowledgeUpdates: (kbId: string) => buildEndpoint(`ws/knowledge/${kbId}/updates`).replace('http', 'ws'),
  
  // 系统状态推送
  systemStatus: buildEndpoint('ws/system/status').replace('http', 'ws')
} as const;

// ==================== 第三方集成端点 ====================

export const integrationEndpoints = {
  // OAuth认证
  oauth: {
    google: buildEndpoint('auth/oauth/google'),
    github: buildEndpoint('auth/oauth/github'),
    microsoft: buildEndpoint('auth/oauth/microsoft')
  },
  
  // 文件存储集成
  storage: {
    s3: buildEndpoint('integrations/storage/s3'),
    aliyunOss: buildEndpoint('integrations/storage/aliyun'),
    qiniuCloud: buildEndpoint('integrations/storage/qiniu')
  },
  
  // 向量数据库集成
  vectorDb: {
    pinecone: buildEndpoint('integrations/vectordb/pinecone'),
    weaviate: buildEndpoint('integrations/vectordb/weaviate'),
    qdrant: buildEndpoint('integrations/vectordb/qdrant')
  },
  
  // LLM模型集成
  llm: {
    openai: buildEndpoint('integrations/llm/openai'),
    anthropic: buildEndpoint('integrations/llm/anthropic'),
    azure: buildEndpoint('integrations/llm/azure')
  }
} as const;

// ==================== 工具函数 ====================

/**
 * 构建带查询参数的URL
 */
export const buildUrlWithParams = (baseUrl: string, params: Record<string, any>): string => {
  const url = new URL(baseUrl, window.location.origin);
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      url.searchParams.append(key, String(value));
    }
  });
  return url.toString();
};

/**
 * 获取API端点信息
 */
export const getEndpointInfo = () => ({
  baseUrl: API_BASE_URL,
  version: CURRENT_VERSION,
  fullBaseUrl: `${API_BASE_URL}/${CURRENT_VERSION}`,
  endpoints: {
    knowledge: Object.keys(knowledgeEndpoints).length,
    document: Object.keys(documentEndpoints).length,
    chunk: Object.keys(chunkEndpoints).length,
    search: Object.keys(searchEndpoints).length,
    task: Object.keys(taskEndpoints).length,
    user: Object.keys(userEndpoints).length,
    system: Object.keys(systemEndpoints).length
  }
});

/**
 * 验证端点URL格式
 */
export const validateEndpoint = (endpoint: string): boolean => {
  try {
    new URL(endpoint, window.location.origin);
    return true;
  } catch {
    return false;
  }
};

// ==================== 导出所有端点 ====================

export const apiEndpoints = {
  knowledge: knowledgeEndpoints,
  document: documentEndpoints,
  chunk: chunkEndpoints,
  search: searchEndpoints,
  task: taskEndpoints,
  user: userEndpoints,
  system: systemEndpoints,
  websocket: websocketEndpoints,
  integration: integrationEndpoints
} as const;

// 默认导出
export default apiEndpoints;

// 调试信息（仅在开发环境输出）
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  console.log('📡 API端点配置加载完成:', getEndpointInfo());
  (window as any).apiEndpoints = apiEndpoints;
}