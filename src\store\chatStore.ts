'use client';

/**
 * 聊天状态管理器
 * 集成事件驱动架构和 Zustand 状态管理的最佳实践
 * 统一管理会话列表、当前消息和聊天状态
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { 
  ChatState, 
  ChatMessage, 
  EventBus,
  ChatStateMachine 
} from '@/lib/chat-engine/types';
import { 
  chatEngineAdapter, 
  ConversationState, 
  AdapterState 
} from './chat-engine-adapter';
import {
  conversationApi
} from '@/services/apiManager';
import { ConversationItem } from '@/types/conversation';
import { useCallback } from 'react';

// 请求去重管理器
class RequestDeduplicator {
  private pendingRequests = new Map<string, Promise<any>>();
  
  async deduplicate<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key)!;
    }
    
    const requestPromise = requestFn().finally(() => {
      this.pendingRequests.delete(key);
    });
    
    this.pendingRequests.set(key, requestPromise);
    return requestPromise;
  }
  
  clear(key?: string) {
    if (key) {
      this.pendingRequests.delete(key);
    } else {
      this.pendingRequests.clear();
    }
  }
}

// 缓存管理器
class CacheManager {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  
  set(key: string, data: any, ttl: number = 5 * 60 * 1000) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }
  
  get(key: string): any | null {
    const cached = this.cache.get(key);
    if (!cached) return null;
    
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }
  
  invalidate(key: string) {
    this.cache.delete(key);
  }
  
  clear() {
    this.cache.clear();
  }
}

// ConversationState 现在从 adapter 导入

// 聊天状态接口
interface ChatStoreState {
  // 基础配置
  agentId: string;
  isInitialized: boolean;
  
  // 会话列表状态 (来自原 conversationStore)
  conversations: ConversationItem[];
  conversationsLoading: boolean;
  conversationsError: string | null;
  hasMoreConversations: boolean;
  currentPage: number;
  
  // 当前聊天状态 (来自原 ChatEngine)
  currentConversationId: string | null;
  messages: ChatMessage[];
  currentInput: string;
  isConnected: boolean;
  messagesLoading: boolean;
  
  // 会话级别状态映射
  conversationStates: Record<string, ConversationState>;
  
  // 操作状态
  isCreatingConversation: boolean;
  isDeletingConversation: string | null;
  isRenamingConversation: string | null;
}

// 聊天操作接口
interface ChatStoreActions {
  // 初始化
  initialize: (config: { agentId: string; conversationId?: string }) => Promise<void>;
  
  // 会话列表操作
  loadConversations: (refresh?: boolean) => Promise<void>;
  loadMoreConversations: () => Promise<void>;
  createConversation: (params?: { initialMessage?: string; metadata?: any }) => Promise<ConversationItem | null>;
  deleteConversation: (conversationId: string) => Promise<boolean>;
  renameConversation: (conversationId: string, newTitle: string) => Promise<boolean>;
  
  // 聊天操作
  switchConversation: (conversationId: string) => Promise<void>;
  sendMessage: (content: string) => void;
  updateInput: (content: string) => void;
  loadMessages: (conversationId?: string) => Promise<void>;
  clearMessages: () => void;
  
  // 状态管理
  setConnected: (connected: boolean) => void;
  clearError: () => void;
  reset: () => void;
  
  // 事件系统
  getEventBus: () => EventBus;
  getStateMachine: () => ChatStateMachine;
}

// 全局实例
const requestDeduplicator = new RequestDeduplicator();
const cacheManager = new CacheManager();

// 事件监听初始化函数
const initializeEventListeners = () => {
  // 监听适配器状态变化
  chatEngineAdapter.subscribe((adapterState: AdapterState) => {
    const currentState = useChatStore.getState();
    
    console.log('🔄 [ChatStore] 订阅适配器状态更新', {
      engineMessagesCount: adapterState.engineState.messages.length,
      currentMessagesCount: currentState.messages.length,
      engineConversationId: adapterState.engineState.conversationId,
      currentConversationId: currentState.currentConversationId
    });
    
    useChatStore.setState((state) => {
      // 同步ChatEngine数据
      state.messages = adapterState.engineState.messages;
      state.currentInput = adapterState.engineState.currentInput;
      state.isConnected = adapterState.isConnected;
      
      // 同步会话 ID
      if (adapterState.engineState.conversationId !== state.currentConversationId) {
        state.currentConversationId = adapterState.engineState.conversationId;
      }
      
      // 同步会话级别状态
      state.conversationStates = adapterState.conversationStates;
    });
  });
};

// 创建统一的聊天状态管理
export const useChatStore = create<ChatStoreState & ChatStoreActions>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // 初始状态
      agentId: process.env.NEXT_PUBLIC_CONVERSATION_AGENT_ID || '',
      isInitialized: false,
      
      // 会话列表状态
      conversations: [],
      conversationsLoading: false,
      conversationsError: null,
      hasMoreConversations: true,
      currentPage: 1,
      
      // 聊天状态
      currentConversationId: null,
      messages: [],
      currentInput: '',
      isConnected: false,
      messagesLoading: false,
      
      // 会话级别状态映射
      conversationStates: {},
      
      // 操作状态
      isCreatingConversation: false,
      isDeletingConversation: null,
      isRenamingConversation: null,
      
      // 初始化
      initialize: async (config) => {
        const { agentId, conversationId } = config;
        
        set((state) => {
          state.agentId = agentId;
          state.currentConversationId = conversationId || null;
          state.isInitialized = true;
        });
        
        // 初始化适配器
        await chatEngineAdapter.initialize({ agentId, conversationId });
        
        // 初始化事件监听
        initializeEventListeners();
        
        // 加载会话列表
        await get().loadConversations();
        
        // 如果有指定会话，加载消息
        if (conversationId) {
          await get().loadMessages(conversationId);
        }
      },
      
      
      // 会话列表操作
      loadConversations: async (refresh = false) => {
        const state = get();
        if (!state.agentId) return;
        
        const cacheKey = `conversations-${state.agentId}-page-1`;
        
        // 检查缓存
        if (!refresh) {
          const cached = cacheManager.get(cacheKey);
          if (cached) {
            set((state) => {
              state.conversations = cached.data;
              state.hasMoreConversations = cached.hasMore;
              state.currentPage = 1;
            });
            return;
          }
        }
        
        // 请求去重
        return requestDeduplicator.deduplicate(`load-conversations-${state.agentId}`, async () => {
          set((state) => {
            state.conversationsLoading = true;
            state.conversationsError = null;
          });
          
          try {
            const normalizedResponse = await conversationApi.getConversations(state.agentId, 1, 20);
            
            // 缓存结果
            cacheManager.set(cacheKey, {
              data: normalizedResponse.data,
              hasMore: normalizedResponse.pagination?.hasMore || false
            });
            
            set((state) => {
              state.conversations = normalizedResponse.data;
              state.hasMoreConversations = normalizedResponse.pagination?.hasMore || false;
              state.currentPage = 1;
              state.conversationsLoading = false;
            });
          } catch (error: any) {
            set((state) => {
              state.conversationsError = error.message;
              state.conversationsLoading = false;
            });
            throw error;
          }
        });
      },
      
      loadMoreConversations: async () => {
        const state = get();
        if (!state.agentId || !state.hasMoreConversations || state.conversationsLoading) return;
        
        const nextPage = state.currentPage + 1;
        const cacheKey = `conversations-${state.agentId}-page-${nextPage}`;
        
        // 检查缓存
        const cached = cacheManager.get(cacheKey);
        if (cached) {
          set((state) => {
            state.conversations = [...state.conversations, ...cached.data];
            state.hasMoreConversations = cached.hasMore;
            state.currentPage = nextPage;
          });
          return;
        }
        
        // 请求去重
        return requestDeduplicator.deduplicate(`load-more-conversations-${state.agentId}-${nextPage}`, async () => {
          set((state) => {
            state.conversationsLoading = true;
          });
          
          try {
            const normalizedResponse = await conversationApi.getConversations(state.agentId, nextPage, 20);
            
            // 缓存结果
            cacheManager.set(cacheKey, {
              data: normalizedResponse.data,
              hasMore: normalizedResponse.pagination?.hasMore || false
            });
            
            set((state) => {
              state.conversations = [...state.conversations, ...normalizedResponse.data];
              state.hasMoreConversations = normalizedResponse.pagination?.hasMore || false;
              state.currentPage = nextPage;
              state.conversationsLoading = false;
            });
          } catch (error: any) {
            set((state) => {
              state.conversationsError = error.message;
              state.conversationsLoading = false;
            });
            throw error;
          }
        });
      },
      
      createConversation: async (params) => {
        const state = get();
        if (!state.agentId) return null;
        
        set((state) => {
          state.isCreatingConversation = true;
        });
        
        try {
          const newConversation = await conversationApi.createConversation(
            state.agentId,
            {
              initialMessage: params?.initialMessage,
              metadata: params?.metadata
            }
          );
          
          // 乐观更新
          set((state) => {
            state.conversations = [newConversation, ...state.conversations];
            state.isCreatingConversation = false;
          });
          
          // 清除相关缓存
          cacheManager.invalidate(`conversations-${state.agentId}-page-1`);
          
          return newConversation;
        } catch (error: any) {
          set((state) => {
            state.conversationsError = error.message;
            state.isCreatingConversation = false;
          });
          return null;
        }
      },
      
      deleteConversation: async (conversationId: string) => {
        const state = get();
        
        set((state) => {
          state.isDeletingConversation = conversationId;
        });
        
        try {
          await conversationApi.deleteConversation(conversationId, state.agentId);
          
          // 乐观更新
          set((state) => {
            state.conversations = state.conversations.filter(c => c.id !== conversationId);
            state.isDeletingConversation = null;
            
            // 如果删除的是当前会话，清空相关状态
            if (state.currentConversationId === conversationId) {
              state.currentConversationId = null;
              state.messages = [];
              state.currentInput = '';
            }
          });
          
          // 清除相关缓存
          cacheManager.invalidate(`conversations-${state.agentId}-page-1`);
          cacheManager.invalidate(`messages-${conversationId}`);
          
          return true;
        } catch (error: any) {
          set((state) => {
            state.conversationsError = error.message;
            state.isDeletingConversation = null;
          });
          return false;
        }
      },
      
      renameConversation: async (conversationId: string, newTitle: string) => {
        if (!newTitle.trim()) return false;
        
        set((state) => {
          state.isRenamingConversation = conversationId;
        });
        
        try {
          const currentState = get();
          await conversationApi.renameConversation(conversationId, newTitle.trim(), currentState.agentId);
          
          // 乐观更新
          set((state) => {
            state.conversations = state.conversations.map(c => 
              c.id === conversationId 
                ? { ...c, titleAlias: newTitle.trim() }
                : c
            );
            state.isRenamingConversation = null;
          });
          
          // 清除相关缓存
          cacheManager.invalidate(`conversations-${get().agentId}-page-1`);
          
          return true;
        } catch (error: any) {
          set((state) => {
            state.conversationsError = error.message;
            state.isRenamingConversation = null;
          });
          return false;
        }
      },
      
      // 聊天操作
      switchConversation: async (conversationId: string) => {
        const state = get();
        
        // 如果是同一个会话，不需要切换
        if (state.currentConversationId === conversationId) {
          return;
        }
        
        console.log('🔄 [ChatStore] switchConversation: 切换会话开始', {
          from: state.currentConversationId,
          to: conversationId
        });
        
        set((state) => {
          state.currentConversationId = conversationId;
          state.messages = [];
          state.currentInput = '';
          state.messagesLoading = true;
        });
        
        // 通过适配器切换会话
        await chatEngineAdapter.switchConversation(conversationId);
        
        set((state) => {
          state.messagesLoading = false;
        });
      },
      
      sendMessage: (content: string) => {
        const state = get();
        console.log('📤 [ChatStore] 发送消息:', { 
          content: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
          conversationId: state.currentConversationId,
          hasConversation: !!state.currentConversationId,
          hasContent: !!content.trim()
        });
        
        if (!state.currentConversationId || !content.trim()) {
          console.warn('❌ [ChatStore] 无法发送消息: 缺少会话ID或内容为空');
          return;
        }
        
        // 通过适配器发送消息
        chatEngineAdapter.sendMessage(state.currentConversationId, content.trim());
      },
      
      updateInput: (content: string) => {
        const state = get();
        if (!state.currentConversationId) {
          console.warn('🚨 [ChatStore] updateInput: 无当前会话ID');
          return;
        }
        
        console.log('📝 [ChatStore] updateInput:', {
          content: content.slice(0, 50) + (content.length > 50 ? '...' : ''),
          conversationId: state.currentConversationId,
          currentMessagesCount: state.messages.length
        });
        
        // 通过适配器更新输入
        chatEngineAdapter.updateInput(state.currentConversationId, content);
      },
      
      loadMessages: async (conversationId?: string) => {
        const state = get();
        const targetConversationId = conversationId || state.currentConversationId;
        
        if (!targetConversationId) return;
        
        console.log('📂 [ChatStore] loadMessages: 开始加载消息', {
          conversationId: targetConversationId
        });
        
        set((state) => {
          state.messagesLoading = true;
        });
        
        try {
          // 通过适配器加载消息
          await chatEngineAdapter.loadMessages(targetConversationId);
          
          set((state) => {
            state.messagesLoading = false;
          });
        } catch (error: any) {
          set((state) => {
            state.messagesLoading = false;
            // 错误状态通过适配器同步
          });
          throw error;
        }
      },
      
      clearMessages: () => {
        const beforeState = get();
        console.log('🗑️ [ChatStore] clearMessages: 手动清空消息', {
          conversationId: beforeState.currentConversationId,
          messagesCount: beforeState.messages.length,
          currentInput: beforeState.currentInput,
          stackTrace: new Error().stack?.split('\n').slice(1, 4).join('\n')
        });
        
        set((state) => {
          state.messages = [];
          state.currentInput = '';
        });
      },
      
      // 状态管理
      setConnected: (connected: boolean) => {
        set((state) => {
          state.isConnected = connected;
        });
        
        // 连接状态通过适配器同步
      },
      
      clearError: () => {
        const state = get();
        
        // 通过适配器清除错误
        if (state.currentConversationId) {
          chatEngineAdapter.clearError(state.currentConversationId);
        }
      },
      
      reset: () => {
        const beforeState = get();
        console.log('🔄 [ChatStore] reset: 重置整个Store', {
          conversationId: beforeState.currentConversationId,
          messagesCount: beforeState.messages.length
        });
        
        set((state) => {
          // 重置聊天状态
          state.currentConversationId = null;
          state.messages = [];
          state.currentInput = '';
          state.messagesLoading = false;
          
          // 清空所有会话级别状态
          state.conversationStates = {};
          
          // 不重置会话列表状态，因为可能还需要使用
        });
        
        // 通过适配器重置
        chatEngineAdapter.reset();
        
        // 清除相关缓存
        cacheManager.clear();
        requestDeduplicator.clear();
      },
      
      // 事件系统（向后兼容）
      getEventBus: () => chatEngineAdapter.getEventBus(),
      getStateMachine: () => chatEngineAdapter.getEventBus() as any // 临时兼容
    }))
  )
);

// 便捷的选择器 Hook
export const useConversationList = () => {
  const conversations = useChatStore(state => state.conversations);
  const loading = useChatStore(state => state.conversationsLoading);
  const error = useChatStore(state => state.conversationsError);
  const hasMore = useChatStore(state => state.hasMoreConversations);
  const loadConversations = useChatStore(state => state.loadConversations);
  const loadMore = useChatStore(state => state.loadMoreConversations);
  const createConversation = useChatStore(state => state.createConversation);
  const deleteConversation = useChatStore(state => state.deleteConversation);
  const renameConversation = useChatStore(state => state.renameConversation);
  const isCreating = useChatStore(state => state.isCreatingConversation);
  const isDeletingConversation = useChatStore(state => state.isDeletingConversation);
  const isRenamingConversation = useChatStore(state => state.isRenamingConversation);
  
  return {
    conversations,
    loading,
    error,
    hasMore,
    loadConversations,
    loadMore,
    createConversation,
    deleteConversation,
    renameConversation,
    isCreating,
    isDeletingConversation,
    isRenamingConversation
  };
};

export const useChatMessages = () => {
  const messages = useChatStore(state => state.messages);
  const loading = useChatStore(state => state.messagesLoading);
  const currentConversationId = useChatStore(state => state.currentConversationId);
  const conversationStates = useChatStore(state => state.conversationStates);
  const loadMessages = useChatStore(state => state.loadMessages);
  
  // 获取当前会话的状态
  const currentConversationState = currentConversationId ? conversationStates[currentConversationId] : null;
  const isStreaming = currentConversationState?.isStreaming || false;
  const streamingMessageId = currentConversationState?.streamingMessageId || null;
  
  return {
    messages,
    loading,
    isStreaming,
    streamingMessageId,
    loadMessages
  };
};

export const useChatInput = () => {
  const value = useChatStore(state => state.currentInput);
  const currentConversationId = useChatStore(state => state.currentConversationId);
  const conversationStates = useChatStore(state => state.conversationStates);
  const updateInput = useChatStore(state => state.updateInput);
  const sendMessage = useChatStore(state => state.sendMessage);
  
  // 获取当前会话的状态
  const currentConversationState = currentConversationId ? conversationStates[currentConversationId] : null;
  const currentState = currentConversationState?.chatState || ChatState.IDLE;
  const isSendingMessage = currentConversationState?.isSending || false;
  
  // 修复canSend逻辑：不依赖于store的currentInput，而是依赖于传入的本地值
  const canSend = useCallback((localValue: string) => {
    return (
      !!currentConversationId && // 必须有会话ID
      localValue.trim().length > 0 && // 必须有输入内容
      currentState !== ChatState.SENDING && 
      currentState !== ChatState.RECEIVING &&
      currentState !== ChatState.STREAMING &&
      !isSendingMessage && // 不能正在发送
      true // 暂时不依赖连接状态，因为连接状态可能不准确
    );
  }, [currentConversationId, currentState, isSendingMessage]);
  
  return {
    value,
    canSend,
    isSending: isSendingMessage,
    onChange: updateInput,
    onSend: sendMessage
  };
};

export const useChatState = () => {
  const currentConversationId = useChatStore(state => state.currentConversationId);
  const conversationStates = useChatStore(state => state.conversationStates);
  const isConnected = useChatStore(state => state.isConnected);
  const switchConversation = useChatStore(state => state.switchConversation);
  const clearError = useChatStore(state => state.clearError);
  const isInitialized = useChatStore(state => state.isInitialized);
  
  // 获取当前会话的状态
  const currentConversationState = currentConversationId ? conversationStates[currentConversationId] : null;
  const currentState = currentConversationState?.chatState || ChatState.IDLE;
  const error = currentConversationState?.error || null;
  
  return {
    currentConversationId,
    currentState,
    isConnected,
    error,
    isInitialized,
    switchConversation,
    clearError,
    
    // 派生状态
    isSending: currentState === ChatState.SENDING,
    isReceiving: currentState === ChatState.RECEIVING,
    isStreaming: currentState === ChatState.STREAMING,
    isTyping: currentState === ChatState.TYPING,
    hasError: currentState === ChatState.ERROR,
    isDisconnected: currentState === ChatState.DISCONNECTED,
    isIdle: currentState === ChatState.IDLE
  };
};

export const useConversationState = () => {
  const agentId = useChatStore(state => state.agentId);
  const currentConversationId = useChatStore(state => state.currentConversationId);
  const isConnected = useChatStore(state => state.isConnected);
  const setConnected = useChatStore(state => state.setConnected);
  const conversations = useChatStore(state => state.conversations);
  const switchConversation = useChatStore(state => state.switchConversation);
  const reset = useChatStore(state => state.reset);
  
  const getConversationById = (conversationId: string) => {
    return conversations.find(c => c.id === conversationId) || null;
  };
  
  return {
    agentId,
    currentConversationId,
    isConnected,
    setConnected,
    getConversationById,
    switchConversation,
    clearAll: reset,
    
    // 向后兼容
    setAgentId: () => {
      // 这里可以实现 agentId 的动态切换逻辑
      console.warn('setAgentId 需要在初始化时设置');
    },
    setCurrentConversation: switchConversation
  };
};