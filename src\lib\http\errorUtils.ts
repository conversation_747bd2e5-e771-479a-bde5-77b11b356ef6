/**
 * 全局错误处理工具函数
 * 提供便捷的错误触发和处理方法
 */

import { ErrorType, ErrorSeverity } from '@/providers/GlobalErrorProvider';

// 全局错误处理器引用
let globalErrorTrigger: ((error: {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  details?: any;
  source?: string;
}) => void) | null = null;

/**
 * 设置全局错误触发器
 * 由GlobalErrorProvider内部调用
 */
export function setGlobalErrorTrigger(trigger: typeof globalErrorTrigger): void {
  globalErrorTrigger = trigger;
}

/**
 * 手动触发认证错误
 * 常用于token过期、认证失败等场景
 */
export function triggerAuthError(message?: string, details?: any): void {
  if (!globalErrorTrigger) {
    console.warn('全局错误处理器未初始化');
    return;
  }
  
  globalErrorTrigger({
    type: ErrorType.AUTH,
    severity: ErrorSeverity.HIGH,
    message: message || '认证失败，请重新登录',
    details,
    source: 'Manual Trigger'
  });
}

/**
 * 手动触发权限错误
 * 常用于用户尝试访问无权限的功能
 */
export function triggerPermissionError(message?: string, details?: any): void {
  if (!globalErrorTrigger) {
    console.warn('全局错误处理器未初始化');
    return;
  }
  
  globalErrorTrigger({
    type: ErrorType.PERMISSION,
    severity: ErrorSeverity.HIGH,
    message: message || '权限不足，无法执行此操作',
    details,
    source: 'Manual Trigger'
  });
}

/**
 * 手动触发网络错误
 * 常用于自定义网络检测、WebSocket连接失败等
 */
export function triggerNetworkError(message?: string, details?: any): void {
  if (!globalErrorTrigger) {
    console.warn('全局错误处理器未初始化');
    return;
  }
  
  globalErrorTrigger({
    type: ErrorType.NETWORK,
    severity: ErrorSeverity.MEDIUM,
    message: message || '网络连接失败，请检查网络状态',
    details,
    source: 'Manual Trigger'
  });
}

/**
 * 手动触发服务器错误
 * 常用于业务逻辑错误、自定义API错误等
 */
export function triggerServerError(message?: string, details?: any, severity: ErrorSeverity = ErrorSeverity.MEDIUM): void {
  if (!globalErrorTrigger) {
    console.warn('全局错误处理器未初始化');
    return;
  }
  
  globalErrorTrigger({
    type: ErrorType.SERVER,
    severity,
    message: message || '服务器处理失败，请稍后重试',
    details,
    source: 'Manual Trigger'
  });
}

/**
 * 手动触发验证错误
 * 常用于表单验证、数据格式错误等
 */
export function triggerValidationError(message?: string, details?: any): void {
  if (!globalErrorTrigger) {
    console.warn('全局错误处理器未初始化');
    return;
  }
  
  globalErrorTrigger({
    type: ErrorType.VALIDATION,
    severity: ErrorSeverity.MEDIUM,
    message: message || '数据验证失败，请检查输入内容',
    details,
    source: 'Manual Trigger'
  });
}

/**
 * 手动触发自定义错误
 * 通用的错误触发方法，可以完全自定义错误信息
 */
export function triggerCustomError(
  type: ErrorType,
  severity: ErrorSeverity,
  message: string,
  details?: any,
  source?: string
): void {
  if (!globalErrorTrigger) {
    console.warn('全局错误处理器未初始化');
    return;
  }
  
  globalErrorTrigger({
    type,
    severity,
    message,
    details,
    source: source || 'Manual Trigger'
  });
}

/**
 * 创建错误处理装饰器
 * 用于包装异步函数，自动捕获和处理错误
 */
export function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options?: {
    errorType?: ErrorType;
    severity?: ErrorSeverity;
    customMessage?: string;
    source?: string;
  }
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args);
    } catch (error: any) {
      const errorType = options?.errorType || ErrorType.UNKNOWN;
      const severity = options?.severity || ErrorSeverity.MEDIUM;
      const message = options?.customMessage || error.message || '操作失败';
      const source = options?.source || fn.name || 'Unknown Function';
      
      triggerCustomError(errorType, severity, message, error, source);
      throw error; // 重新抛出错误，让调用方可以选择如何处理
    }
  }) as T;
}

/**
 * 创建组件错误边界用的错误报告函数
 * 用于React错误边界组件中报告错误
 */
export function reportComponentError(
  error: Error,
  errorInfo: any,
  componentName?: string
): void {
  triggerCustomError(
    ErrorType.UNKNOWN,
    ErrorSeverity.HIGH,
    `组件渲染错误: ${error.message}`,
    {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      errorInfo,
      timestamp: Date.now()
    },
    componentName || 'React Component'
  );
}

/**
 * 检查是否为认证相关错误
 */
export function isAuthRelatedError(error: any): boolean {
  if (typeof error !== 'object' || !error) return false;
  
  // 检查HTTP状态码
  if (error.response?.status === 401 || error.response?.status === 403) {
    return true;
  }
  
  // 检查错误消息
  const message = error.message?.toLowerCase() || '';
  return message.includes('auth') || 
         message.includes('token') || 
         message.includes('login') ||
         message.includes('unauthorized') ||
         message.includes('forbidden');
}

/**
 * 检查是否为网络相关错误
 */
export function isNetworkRelatedError(error: any): boolean {
  if (typeof error !== 'object' || !error) return false;
  
  // 检查错误代码
  const code = error.code?.toLowerCase() || '';
  if (code.includes('network') || code.includes('timeout') || code.includes('abort')) {
    return true;
  }
  
  // 检查是否为请求超时或网络错误
  return !error.response && error.request;
}

/**
 * 从错误对象中提取有用信息
 */
export function extractErrorInfo(error: any): {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  details: any;
} {
  if (isAuthRelatedError(error)) {
    return {
      type: ErrorType.AUTH,
      severity: ErrorSeverity.HIGH,
      message: '认证失败，请重新登录',
      details: error
    };
  }
  
  if (isNetworkRelatedError(error)) {
    return {
      type: ErrorType.NETWORK,
      severity: ErrorSeverity.MEDIUM,
      message: '网络连接失败，请检查网络状态',
      details: error
    };
  }
  
  // 默认错误处理
  return {
    type: ErrorType.UNKNOWN,
    severity: ErrorSeverity.MEDIUM,
    message: error.message || '操作失败，请稍后重试',
    details: error
  };
} 