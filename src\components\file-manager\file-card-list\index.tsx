import { IFile } from '@/interfaces/database/file-manager';
import { Col, Row } from 'antd';
import FileCard from '../file-card';

interface FileCardListProps {
    files: IFile[];
    onNavigate: (folderId: string) => void;
    onRename: (file: IFile) => void;
    onDelete: (fileId: string, parentId: string, fileName: string) => void;
}

const FileCardList = ({ files, onNavigate, onRename, onDelete }: FileCardListProps) => {
    if (!files || files.length === 0) {
        return null;
    }

    return (
        <Row gutter={[16, 16]} className="w-full flex-1 overflow-auto">
            {files.map((file) => (
                <Col
                    key={file.id}
                    xs={24}
                    sm={12}
                    md={8}
                    lg={6}
                    xl={6}
                    className="p-0 m-0"
                >
                    <FileCard
                        file={file}
                        onNavigate={onNavigate}
                        onRename={onRename}
                        onDelete={onDelete}
                    />
                </Col>
            ))}
        </Row>
    );
};

export default FileCardList;