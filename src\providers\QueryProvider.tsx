'use client';

import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// 创建全局QueryClient实例
const createQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // 数据被认为过期的时间（5分钟）
        staleTime: 5 * 60 * 1000,
        // 缓存时间（10分钟）
        gcTime: 10 * 60 * 1000,
        // 错误重试次数
        retry: (failureCount, error: any) => {
          // 4xx错误不重试
          if (error?.status >= 400 && error?.status < 500) {
            return false;
          }
          // 最多重试2次
          return failureCount < 2;
        },
        // 重试延迟（指数退避）
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        // 窗口重新聚焦时重新获取
        refetchOnWindowFocus: false,
        // 网络重连时重新获取
        refetchOnReconnect: true,
      },
      mutations: {
        // 错误重试次数
        retry: (failureCount, error: any) => {
          // POST/PUT/DELETE等操作一般不重试
          return false;
        },
      },
    },
  });
};

let queryClient: QueryClient;

function getQueryClient() {
  if (typeof window === 'undefined') {
    // 服务端：总是创建新的client
    return createQueryClient();
  } else {
    // 客户端：重用现有client
    if (!queryClient) {
      queryClient = createQueryClient();
    }
    return queryClient;
  }
}

interface QueryProviderProps {
  children: React.ReactNode;
}

export default function QueryProvider({ children }: QueryProviderProps) {
  const client = getQueryClient();

  return (
    <QueryClientProvider client={client}>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  );
}

// 导出QueryClient实例供其他地方使用
export { getQueryClient };