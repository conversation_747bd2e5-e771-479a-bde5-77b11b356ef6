'use client';

import { AGUIEvent, EventType } from '@/lib/agui/types';
import { EventBus } from './types';

/**
 * AGUI到ChatEngine的事件桥接器
 * 负责将AGUIProvider的事件转发到ChatEngine事件总线
 */
export class AGUIEventBridge {
  private eventBus: EventBus;
  private enableLogging: boolean = false;

  constructor(eventBus: EventBus, options?: { enableLogging?: boolean }) {
    this.eventBus = eventBus;
    this.enableLogging = options?.enableLogging ?? false;
  }

  /**
   * 处理AGUIProvider的事件并转发到ChatEngine
   */
  handleAGUIEvent(event: AGUIEvent, conversationId: string): void {
    if (this.enableLogging) {
      console.log('🌉 [EventBridge] 接收AGUI事件:', event.type, event);
    }

    // 转换并发射到ChatEngine事件总线
    this.convertAndForwardEvent(event, conversationId);
  }

  /**
   * 转换AGUI事件为ChatEngine事件并转发
   */
  private convertAndForwardEvent(event: AGUIEvent, conversationId: string): void {
    const timestamp = Date.now();
    const source = 'AGUIEventBridge';

    // 🔧 首先处理非EventType枚举的事件（如CONNECTION_STATUS）
    if ((event as any).type === 'CONNECTION_STATUS') {
      this.eventBus.emit({
        type: 'CONNECTION_STATUS',
        payload: {
          status: (event as any).payload.status,
          conversationId
        },
        timestamp,
        source
      });
      return;
    }

    // 处理标准的EventType枚举事件
    switch (event.type) {
      case EventType.RUN_STARTED:
        this.eventBus.emit({
          type: 'RUN_STARTED',
          payload: {
            runId: event.runId,
            conversationId
          },
          timestamp,
          source
        });
        break;

      case EventType.TEXT_MESSAGE_START:
        this.eventBus.emit({
          type: 'STREAMING_START',
          payload: {
            messageId: event.messageId,
            conversationId
          },
          timestamp,
          source
        });
        break;

      case EventType.TEXT_MESSAGE_CONTENT:
        this.eventBus.emit({
          type: 'STREAMING_CONTENT',
          payload: {
            messageId: event.messageId,
            content: '', // AGUI只提供delta，累积content在状态机处理
            delta: event.delta,
            conversationId
          },
          timestamp,
          source
        });
        break;

      case EventType.TEXT_MESSAGE_END:
        this.eventBus.emit({
          type: 'STREAMING_END',
          payload: {
            messageId: event.messageId,
            finalContent: '', // 最终内容从状态机获取
            conversationId
          },
          timestamp,
          source
        });
        break;

      case EventType.TOOL_CALL_START:
        this.eventBus.emit({
          type: 'TOOL_CALL_START',
          payload: {
            toolCallId: event.toolCallId,
            toolName: event.toolCallName,
            conversationId
          },
          timestamp,
          source
        });
        break;

      case EventType.TOOL_CALL_ARGS:
        this.eventBus.emit({
          type: 'TOOL_CALL_ARGS',
          payload: {
            toolCallId: event.toolCallId,
            args: '', // AGUI只提供delta，完整args需要累积
            delta: event.delta,
            conversationId
          },
          timestamp,
          source
        });
        break;

      case EventType.TOOL_CALL_END:
        this.eventBus.emit({
          type: 'TOOL_CALL_END',
          payload: {
            toolCallId: event.toolCallId,
            finalArgs: '', // 最终args从累积的delta获取
            conversationId
          },
          timestamp,
          source
        });
        break;

      case EventType.TOOL_CALL_RESULT:
        this.eventBus.emit({
          type: 'TOOL_CALL_RESULT',
          payload: {
            toolCallId: event.toolCallId,
            result: event.content,
            conversationId
          },
          timestamp,
          source
        });
        break;

      case EventType.RUN_FINISHED:
        this.eventBus.emit({
          type: 'RUN_FINISHED',
          payload: {
            runId: event.runId,
            conversationId
          },
          timestamp,
          source
        });
        break;

      case EventType.RUN_ERROR:
        this.eventBus.emit({
          type: 'ERROR',
          payload: {
            error: event.message,
            code: event.code || 'AGUI_ERROR',
            conversationId
          },
          timestamp,
          source
        });
        break;

      default:
        if (this.enableLogging) {
          console.log(`🌉 [EventBridge] 未处理的AGUI事件类型: ${event.type}`);
        }
    }
  }

  /**
   * 转发连接状态变化
   */
  handleConnectionStateChange(state: string, conversationId: string): void {
    if (this.enableLogging) {
      console.log('🌉 [EventBridge] 连接状态变化:', state);
    }

    this.eventBus.emit({
      type: 'CONNECTION_STATUS',
      payload: {
        status: this.mapConnectionState(state),
        conversationId
      },
      timestamp: Date.now(),
      source: 'AGUIEventBridge'
    });
  }

  /**
   * 映射连接状态
   */
  private mapConnectionState(state: string): string {
    switch (state) {
      case 'CONNECTED':
        return 'connected';
      case 'DISCONNECTED':
        return 'disconnected';
      case 'CONNECTING':
        return 'connecting';
      case 'RECONNECTING':
        return 'reconnecting';
      case 'FAILED':
        return 'disconnected';
      default:
        return 'disconnected';
    }
  }

  /**
   * 设置日志开关
   */
  setLogging(enabled: boolean): void {
    this.enableLogging = enabled;
  }

  /**
   * 销毁桥接器
   */
  destroy(): void {
    if (this.enableLogging) {
      console.log('🌉 [EventBridge] 已销毁');
    }
  }
}