import { IChunk } from '../interfaces/knowledge';
import { ChunkTextMode } from './constant';
import {
  Card,
  Checkbox,
  CheckboxProps,
  Flex,
  Popover,
  Switch,
  Divider,
  Button,
  Tag,
} from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
import DOMPurify from 'dompurify';
import { useEffect, useState } from 'react';
import Image from '../components/image';
interface IProps {
  item: IChunk;
  checked: boolean;
  switchChunk: (available?: number, chunkIds?: string[]) => void;
  editChunk: (chunkId: string) => void;
  handleCheckboxClick: (chunkId: string, checked: boolean) => void;
  selected: boolean;
  clickChunkCard: (chunkId: string) => void;
  textMode: ChunkTextMode;
  rowIndex: number;
  removeChunk: (selectedChunkIds: string[]) => Promise<number>;
  showBatchOperate: boolean;
}

const ChunkCard = ({
  item,
  checked,
  handleCheckboxClick,
  editChunk,
  switchChunk,
  selected,
  clickChunkCard,
  textMode,
  rowIndex,
  removeChunk,
  showBatchOperate,
}: IProps) => {
  const available = Number(item.available_int);
  const [enabled, setEnabled] = useState(false);

  const handleCheck: CheckboxProps['onChange'] = (e) => {
    handleCheckboxClick(item.chunk_id, e.target.checked);
  };

  const handleEdit = () => {
    editChunk(item.chunk_id);
  };

  const handleRemove = async (chunk_id: string) => {
    await removeChunk([chunk_id]);
  };

  const onChange = (checked: boolean) => {
    setEnabled(checked);
    switchChunk(available === 0 ? 1 : 0, [item.chunk_id]);
  };

  useEffect(() => {
    setEnabled(available === 1);
  }, [available]);

  return (
    <Card className="group/card" hoverable>
      <Flex gap={'middle'} justify={'space-between'}>
        {/* {item.image_id ? (
          <Popover
            placement="right"
            content={
              <Image
                id={item.image_id}
                className={'max-w-[50vw] max-h-[50vh] object-contain'}
              ></Image>
            }
          >
            <Image
              id={item.image_id}
              className={'!w-full object-contain'}
            ></Image>
          </Popover>
        ) : (
          <div className="w-[120px] h-[80px] rounded-[8px] bg-[#fafafa] border border-[#ebebeb]"></div>
        )} */}
        <section className="flex-1" onClick={() => clickChunkCard(item.chunk_id)}>
          <div className="relative">
            <div className="text-[#c2c2c2] text-[13px] mb-[8px]">
              分段{rowIndex + 1}
            </div>
            <div className="absolute top-0 right-0">
              {showBatchOperate ? (
                <Checkbox onChange={handleCheck} checked={checked}></Checkbox>
              ) : (
                <>
                  <div className="items-center group-hover/card:opacity-100 group-hover/card:flex opacity-0 transition-opacity hidden">
                    <Button
                      className="mr-[8px]"
                      onClick={handleEdit}
                      icon={
                        <EditOutlined
                          style={{ color: '#999', fontSize: '16px' }}
                          twoToneColor="#999"
                        />
                      }
                      type="text"
                    />
                    <Button
                      onClick={() => handleRemove(item.chunk_id)}
                      icon={
                        <DeleteOutlined
                          style={{ color: '#999', fontSize: '16px' }}
                        />
                      }
                      type="text"
                    />
                    <Divider type="vertical" />
                    <Switch checked={enabled} onChange={onChange} />
                  </div>
                  <div className="group-hover/card:opacity-0 group-hover/card:hidden transition-opacity">
                    {available === 1 ? (
                      <Tag color="green">启用</Tag>
                    ) : (
                      <Tag color="red">禁用</Tag>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
          <div
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(item.content_with_weight),
            }}
          ></div>
        </section>
      </Flex>
    </Card>
  );
};

export default ChunkCard;
