"use client";

import React, { useEffect, useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAuthContext } from "@/lib/auth/AuthProvider";
import { useBoolean, useClickAway } from "ahooks";
import { triggerLogout } from "@/lib/auth/keycloak";

interface MenuItem {
  id: string;
  label: string;
  href: string;
  icon: (isActive: boolean) => React.ReactNode;
  badge?: string;
}

const menuItems: MenuItem[] = [
  {
    id: "chat",
    label: "聊天",
    href: "/chat",
    icon: (isActive: boolean) => (
      <img
        src={
          isActive
            ? "/assets/sidebar/chat-active.svg"
            : "/assets/sidebar/chat.svg"
        }
        alt="聊天"
        className="flex-shrink-0"
      />
    ),
  },
  {
    id: "files",
    label: "文件",
    href: "/files",
    icon: (isActive: boolean) => (
      <img
        src={
          isActive
            ? "/assets/sidebar/files-active.svg"
            : "/assets/sidebar/files.svg"
        }
        alt="文件"
        className="flex-shrink-0"
      />
    ),
  },
  {
    id: "knowledge",
    label: "知识库",
    href: "/knowledge",
    icon: (isActive: boolean) => (
      <img
        src={
          isActive
            ? "/assets/sidebar/knowledge-active.svg"
            : "/assets/sidebar/knowledge.svg"
        }
        alt="知识库"
        className="flex-shrink-0"
      />
    ),
  },
  {
    id: "ai-staff",
    label: "AI员工",
    href: "/ai-staff",
    icon: (isActive: boolean) => (
      <img
        src={
          isActive
            ? "/assets/sidebar/ai-staff-active.svg"
            : "/assets/sidebar/ai-staff.svg"
        }
        alt="AI员工"
        className="flex-shrink-0"
      />
    ),
  },
  {
    id: "history",
    label: "历史会话",
    href: "/history",
    icon: (isActive: boolean) => (
      <img
        src={
          isActive
            ? "/assets/sidebar/history-active.svg"
            : "/assets/sidebar/history.svg"
        }
        alt="历史会话"
        className="flex-shrink-0"
      />
    ),
  },
];

interface SidebarProps {
  collapsed: boolean;
  onToggle: () => void;
}

export default function Sidebar({ collapsed }: SidebarProps) {
  const pathname = usePathname();
  const { user, isAuthenticated } = useAuthContext();
  const [isClient, setIsClient] = useState(false);

  // 修复hooks调用顺序：所有hooks放在组件顶层
  const [open, { setTrue, setFalse, toggle }] = useBoolean(false);
  const menuRef = React.useRef(null);
  useClickAway(() => {
    if (open) setFalse();
  }, menuRef);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div
      className={`
      flex flex-col bg-[#F5F5F5] border-r border-border-light transition-all duration-300 p-[8px] gap-[16px]
      ${collapsed ? "w-[56px]" : "w-[264px]"}
    `}
    >
      {/* 头部区域 - 品牌信息 */}
      <div className="flex items-center space-x-[12px] h-[40px] px-2 w-full">
        <img src="/assets/logo/logo.svg" alt="logo" />
        {!collapsed && (
          <span className="text-[16px] font-bold color-[#000000E0]">
            知识库
          </span>
        )}
      </div>

      {/* 用户信息区域 */}
      <div className={`${collapsed ? "px-2" : ""}`}>
        {collapsed ? (
          <div className="flex items-center justify-center h-[40px]">
            {isAuthenticated ? (
              <div className="w-[24px] h-[24px] bg-text-primary rounded-full flex items-center justify-center">
                <span className="text-bg-base text-sm font-medium">
                  {isClient
                    ? user?.name?.[0] || user?.username?.[0] || "U"
                    : "U"}
                </span>
              </div>
            ) : (
              <img
                src="/assets/sidebar/default-avatar.svg"
                alt="默认头像"
                className="w-[24px] h-[24px] rounded-full object-cover"
              />
            )}
          </div>
        ) : isAuthenticated ? (
          <div className="relative">
            <div
              className="flex items-center space-x-3 bg-[#FFFFFF] rounded-[8px] box-border p-[8px] h-[40px] cursor-pointer select-none border-[1px] border-[#0000000F]"
              onClick={toggle}
            >
              <div className="w-[24px] h-[24px] bg-text-primary rounded-full flex items-center justify-center">
                <span className="text-bg-base text-sm font-medium">
                  {isClient
                    ? user?.name?.[0] || user?.username?.[0] || "U"
                    : "U"}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-[14px] text-[#000000E0] font-medium truncate">
                  {isClient ? user?.name || user?.username || "ytf" : "ytf"}
                </p>
              </div>
              <img
                src="/assets/sidebar/chevron-down.svg"
                alt="chevron-down"
                className="flex-shrink-0"
              />
            </div>
            {open && (
              <div
                ref={menuRef}
                className="absolute left-0 mt-2 w-full bg-white rounded-[8px] shadow-lg z-50 p-[6px] border-[1px] border-[#0000000F]"
                style={{ boxShadow: "0px 2px 4px 0px #00000014" }}
              >
                <button
                  className="cursor-pointer flex items-center w-full px-[6px] text-[14px] text-[#000000E0] hover:bg-layer-2 transition-colors mb-[6px] rounded-[8px] h-[33px]"
                  onClick={() => {
                    /* TODO: 跳转设置页 */ setFalse();
                  }}
                >
                  <img
                    src="/assets/sidebar/setting.svg"
                    alt="setting"
                    className="mr-[12px]"
                  />
                  <span className="font-normal text-sm">设置</span>
                </button>
                <button
                  className="cursor-pointer  flex items-center w-full px-[6px] text-[14px] text-[#000000E0] hover:bg-layer-2 transition-colors rounded-[8px] h-[33px]"
                  onClick={() => {
                    triggerLogout();
                    setFalse();
                  }}
                >
                  <img
                    src="/assets/sidebar/logout.svg"
                    alt="logout"
                    className="mr-[12px]"
                  />
                  <span className="font-normal text-sm">登出</span>
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center space-x-3 bg-[#FFFFFF] rounded-[8px] box-border p-[8px] h-[40px] border-[1px] border-[#0000000F]">
            <img src="/assets/sidebar/default-avatar.svg" alt="默认头像" />
            <div className="flex-1 min-w-0">
              <span className="text-[14px] text-[#000000E0] font-medium">
                未登录
              </span>
            </div>
            <img
              src="/assets/sidebar/chevron-down.svg"
              alt="chevron-down"
              className="flex-shrink-0"
            />
          </div>
        )}
      </div>

      {/* 菜单区域 */}
      <nav className="flex-1">
        <ul className="space-y-1">
          {menuItems.map((item) => {
            const isActive =
              item.href === "/history"
                ? pathname.startsWith("/history")
                : pathname === item.href;

            return (
              <li key={item.id}>
                <Link
                  href={item.href}
                  className={`
                    flex items-center px-2 rounded-[8px] h-[40px] w-full  text-[14px]
                    ${
                      isActive
                        ? "bg-[#EBEBEB] text-[#000000E0] font-medium"
                        : "hover:bg-layer-2 text-[#00000066] hover:text-[#000000E0] font-medium"
                    }
                  `}
                >
                  {item.icon(isActive)}

                  {!collapsed && (
                    <>
                      <span className="ml-3 text-[14px] font-medium">
                        {item.label}
                      </span>
                      {item.badge && (
                        <span className="ml-auto bg-error text-bg-base text-xs px-2 py-1 rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </>
                  )}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>
    </div>
  );
}
