'use client';

// AGUIProvider - 精简的SignalR事件桥接器

import React, { createContext, useContext, useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { 
  AGUIContext, 
  AGUIContextState, 
  AgentConfig,
  ConnectionState,
  AGUIEvent,
  EventType,
  ToolDefinition,
  RegisteredAction
} from './types';
import { SignalRClient, createSignalRClient } from './signalr-client';

// 默认配置
const defaultConfig: AgentConfig = {
  agentId: 'agentfoundry-agent',
  conversationId: `conv-${Date.now()}`,
  hubUrl: 'http://localhost:5000/agenthub',
  autoConnect: true,
  initialState: {}
};

const initialState: AGUIContextState = {
  connectionState: ConnectionState.DISCONNECTED,
  connectionError: undefined,
  config: defaultConfig,
  registeredActions: new Map(), 
  activeToolCalls: new Map(),
  conversationId: defaultConfig.conversationId!,
  toolCallHistory: []
};

// 创建 Context
const AGUIContextInstance = createContext<AGUIContext | null>(null);

// Provider Props
interface AGUIProviderProps {
  children: React.ReactNode;
  config?: Partial<AgentConfig>;
  onProviderReady?: (disconnect: () => Promise<void>) => void;
  onAGUIEvent?: (event: AGUIEvent) => void; 
}

export function AGUIProvider({ children, config = {}, onProviderReady, onAGUIEvent }: AGUIProviderProps) {
  const [state, setState] = useState<AGUIContextState>(initialState);
  
  const connectionRef = useRef<SignalRClient | null>(null);
  const isConnectedRef = useRef(false);
  const isInitializingRef = useRef(false);
  const connectionConfigRef = useRef<string>('');

  const finalConfig = useMemo(() => {
    return { ...defaultConfig, ...config };
  }, [config]);

  // 初始化 SignalR 连接
  useEffect(() => {
    const currentConfigKey = `${finalConfig.hubUrl}-${finalConfig.autoConnect}`;
    
    // 防止重复初始化
    if (connectionRef.current && connectionConfigRef.current === currentConfigKey) {
      return;
    }
    
    if (isInitializingRef.current) {
      return;
    }

    isInitializingRef.current = true;

    const initializeConnection = async () => {
      try {
        // 如果已存在连接但配置不同，先停止旧连接
        if (connectionRef.current) {
          await connectionRef.current.stop();
          connectionRef.current = null;
        }

        connectionRef.current = createSignalRClient({
          hubUrl: finalConfig.hubUrl || 'http://localhost:5000/agenthub',
          withCredentials: true,
          automaticReconnect: true
        });

        connectionConfigRef.current = currentConfigKey;
        
        setupEventListeners();

        // 连接状态监听
        connectionRef.current.onreconnecting(() => {
          setState(prev => ({ ...prev, connectionState: ConnectionState.RECONNECTING }));
        });

        connectionRef.current.onreconnected(() => {
          setState(prev => ({ ...prev, connectionState: ConnectionState.CONNECTED }));
          isConnectedRef.current = true;
          if (onProviderReady) {
            onProviderReady(disconnect);
          }
        });

        connectionRef.current.onclose(() => {
          setState(prev => ({ ...prev, connectionState: ConnectionState.DISCONNECTED }));
          isConnectedRef.current = false;
        });

        // 自动连接
        if (finalConfig.autoConnect) {
          await connect();
        }
      } catch (error) {
        console.error('初始化SignalR连接失败:', error);
        setState(prev => ({ 
          ...prev, 
          connectionError: error instanceof Error ? error.message : '连接初始化失败' 
        }));
      } finally {
        isInitializingRef.current = false;
      }
    };

    initializeConnection();

    return () => {
      if (connectionConfigRef.current !== currentConfigKey) {
        if (connectionRef.current) {
          connectionRef.current.stop();
          connectionRef.current = null;
        }
        connectionConfigRef.current = '';
        isInitializingRef.current = false;
      }
    };
  }, [finalConfig.hubUrl, finalConfig.autoConnect]);

  // 设置事件监听器
  const setupEventListeners = useCallback(() => {
    if (!connectionRef.current) return;
    connectionRef.current.setAGUIEventListener(handleAGUIEvent);
  }, []);

  // 处理 AG-UI 事件
  const handleAGUIEvent = useCallback((event: AGUIEvent) => {
    console.log('🔔 收到AGUIEvent事件', event);
    
    if (onAGUIEvent) {
      onAGUIEvent(event);
    }
    
    setState(prevState => {
      const newState = { ...prevState };
      
      switch (event.type) {
        case EventType.RUN_STARTED:
          break;
        case EventType.RUN_FINISHED:
        case EventType.RUN_ERROR:
          break;
        case EventType.TOOL_CALL_START:
          // 简化工具调用状态管理
          if (event.toolCallId && event.toolCallName) {
            newState.activeToolCalls = new Map(prevState.activeToolCalls);
            newState.activeToolCalls.set(event.toolCallId, {
              id: event.toolCallId,
              name: event.toolCallName,
              args: '',
              status: 'receiving_args',
              startTime: Date.now()
            });
          }
          break;
        case EventType.TOOL_CALL_END:
          if (event.toolCallId) {
            // 执行工具调用
            executeSimpleToolCall(event.toolCallId);
          }
          break;
        case EventType.TOOL_CALL_RESULT:
          // 清理工具调用状态
          if (event.toolCallId) {
            newState.activeToolCalls = new Map(prevState.activeToolCalls);
            newState.activeToolCalls.delete(event.toolCallId);
          }
          break;
      }

      return newState;
    });
  }, [onAGUIEvent]);

  const executeSimpleToolCall = useCallback(async (toolCallId: string) => {
    try {
      const toolCall = state.activeToolCalls.get(toolCallId);
      if (!toolCall) return;

      const action = state.registeredActions.get(toolCall.name);
      if (!action) {
        throw new Error(`Action ${toolCall.name} not found`);
      }
      
      await action.handler({});
      
      // 发送成功信号
      await connectionRef.current?.invoke('ToolExecuted', {
        toolCallId,
        success: true
      });
      
    } catch (error) {
      console.error('Tool execution failed:', error);
      const errorMessage = error instanceof Error ? error.message : '工具执行失败';
      
      await connectionRef.current?.invoke('ToolExecuted', {
        toolCallId,
        success: false,
        error: errorMessage
      });
    }
  }, [state.activeToolCalls, state.registeredActions]);

  // 连接操作
  const connect = useCallback(async (): Promise<void> => {
    if (!connectionRef.current || isConnectedRef.current) return;

    try {
      setState(prev => ({ ...prev, connectionState: ConnectionState.CONNECTING }));
      
      await connectionRef.current.connect();
      
      setState(prev => ({ 
        ...prev, 
        connectionState: ConnectionState.CONNECTED,
        connectionError: undefined 
      }));
      
      isConnectedRef.current = true;
      
      // 通知 ChatEngine 连接已建立
      if (onAGUIEvent) {
        const connectionEvent = {
          type: 'CONNECTION_STATUS' as const,
          payload: {
            status: 'connected',
            conversationId: state.conversationId
          },
          timestamp: Date.now(),
          source: 'AGUIProvider'
        };
        
        onAGUIEvent(connectionEvent as any);
        console.log('✅ [AGUIProvider] 已通知ChatEngine连接成功');
      }
      
      if (onProviderReady) {
        onProviderReady(disconnect);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '连接失败';
      setState(prev => ({
        ...prev,
        connectionState: ConnectionState.FAILED,
        connectionError: errorMessage
      }));
      isConnectedRef.current = false;
      throw error;
    }
  }, [state.conversationId, onAGUIEvent, onProviderReady]);

  const disconnect = useCallback(async (): Promise<void> => {
    console.log(`🔌 开始断开AGUIProvider连接 (conversationId: ${state.conversationId})`);
    
    if (!connectionRef.current || !isConnectedRef.current) {
      console.log('🔌 AGUIProvider连接已处于断开状态，无需重复断开');
      return;
    }
    
    try {
      console.log('🔌 正在断开SignalR连接...');
      await connectionRef.current.disconnect();
      console.log('✅ AGUIProvider连接已成功断开');
    } catch (error) {
      console.warn('⚠️ AGUIProvider断开连接时发生错误:', error);
    } finally {
      isConnectedRef.current = false;
      setState(prev => ({ ...prev, connectionState: ConnectionState.DISCONNECTED }));
      console.log('🧹 AGUIProvider连接状态已重置');
    }
  }, [state.conversationId]);





  const stopGeneration = useCallback((): void => {
    setState(prevState => ({ ...prevState, isLoading: false }));
  }, []);


  // 获取工具定义
  const getToolDefinitions = useCallback((): ToolDefinition[] => {
    return Array.from(state.registeredActions.values()).map(action => ({
      name: action.name,
      description: action.description,
      parameters: {
        type: 'object' as const,
        properties: action.parameters.reduce((props, param) => {
          props[param.name] = {
            type: param.type,
            description: param.description,
            ...(param.enum && { enum: param.enum }),
          };
          return props;
        }, {} as Record<string, any>),
        required: action.parameters.filter(p => p.required).map(p => p.name)
      }
    }));
  }, [state.registeredActions]);

  // Action管理
  const registerAction = useCallback((action: RegisteredAction): void => {
    setState(prevState => {
      const newActions = new Map(prevState.registeredActions);
      newActions.set(action.name, action);
      return { ...prevState, registeredActions: newActions };
    });
  }, []);

  const unregisterAction = useCallback((name: string): void => {
    setState(prevState => {
      const newActions = new Map(prevState.registeredActions);
      newActions.delete(name);
      return { ...prevState, registeredActions: newActions };
    });
  }, []);

  // 简化的配置更新
  const updateConfig = useCallback((newConfig: Partial<AgentConfig>): void => {
    setState(prevState => ({
      ...prevState,
      config: { ...prevState.config, ...newConfig },
      ...(newConfig.conversationId && { conversationId: newConfig.conversationId })
    }));
  }, []);

  const contextValue: AGUIContext = {
    // 核心状态
    ...state,
    config: finalConfig,
    
    // 连接操作
    connect,
    disconnect,
    isConnected: isConnectedRef.current,
      
    // Action管理
    registerAction,
    unregisterAction,
    
    // 核心操作
    executeToolCall: executeSimpleToolCall,
    getToolDefinitions,
    
    // 配置更新
    updateConfig,
  };

  return (
    <AGUIContextInstance.Provider value={contextValue}>
      {children}
    </AGUIContextInstance.Provider>
  );
}

export function useAGUIContext(): AGUIContext {
  const context = useContext(AGUIContextInstance);
  if (!context) {
    throw new Error('useAGUIContext must be used within an AGUIProvider');
  }
  return context;
}

export type { AGUIProviderProps };