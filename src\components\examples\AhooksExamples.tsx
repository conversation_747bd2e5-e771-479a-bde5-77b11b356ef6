'use client';

import { 
  useRequest, 
  useLocalStorageState, 
  useDebounce, 
  useThrottle, 
  useBoolean, 
  useToggle,
  useSetState,
  useCounter,
  useTitle,
  useMount,
  useUpdateEffect
} from 'ahooks';

// 模拟API调用
const mockApiCall = (query: string): Promise<string[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const results = [
        `${query} - 结果1`,
        `${query} - 结果2`,
        `${query} - 结果3`,
      ];
      resolve(results);
    }, 1000);
  });
};

// useRequest 示例
function UseRequestExample() {
  const { data, loading, error, run } = useRequest(
    (query: string) => mockApiCall(query),
    {
      manual: true,
      onSuccess: (result) => {
        console.log('请求成功:', result);
      },
      onError: (err) => {
        console.error('请求失败:', err);
      }
    }
  );

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">useRequest 示例</h3>
      <div className="space-y-4">
        <button
          onClick={() => run('搜索内容')}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400"
        >
          {loading ? '请求中...' : '发起请求'}
        </button>
        
        {error && (
          <div className="text-red-500">错误: {error.message}</div>
        )}
        
        {data && (
          <div className="bg-gray-50 p-4 rounded">
            <h4 className="font-medium mb-2">请求结果:</h4>
            <ul className="list-disc pl-5">
              {data.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}

// useLocalStorageState 示例
function UseLocalStorageStateExample() {
  const [theme, setTheme] = useLocalStorageState('app-theme', {
    defaultValue: 'light'
  });

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">useLocalStorageState 示例</h3>
      <div className="space-y-4">
        <p>当前主题: <span className="font-medium">{theme}</span></p>
        <div className="space-x-2">
          <button
            onClick={() => setTheme('light')}
            className={`px-4 py-2 rounded ${
              theme === 'light' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            浅色主题
          </button>
          <button
            onClick={() => setTheme('dark')}
            className={`px-4 py-2 rounded ${
              theme === 'dark' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            深色主题
          </button>
        </div>
        <p className="text-sm text-gray-600">
          主题设置会保存在 localStorage 中，刷新页面后仍然有效
        </p>
      </div>
    </div>
  );
}

// useDebounce 示例
function UseDebounceExample() {
  const [searchTerm, setSearchTerm] = useLocalStorageState('search-term', {
    defaultValue: ''
  });
  const debouncedSearchTerm = useDebounce(searchTerm, { wait: 500 });

  const { data, loading } = useRequest(
    () => mockApiCall(debouncedSearchTerm),
    {
      ready: !!debouncedSearchTerm,
      refreshDeps: [debouncedSearchTerm]
    }
  );

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">useDebounce 示例</h3>
      <div className="space-y-4">
        <div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="输入搜索内容..."
            className="w-full p-2 border border-gray-300 rounded"
          />
          <p className="text-sm text-gray-600 mt-1">
            输入内容: {searchTerm}
          </p>
          <p className="text-sm text-gray-600">
            防抖后内容: {debouncedSearchTerm}
          </p>
        </div>
        
        {loading && (
          <div className="text-blue-500">搜索中...</div>
        )}
        
        {data && (
          <div className="bg-gray-50 p-4 rounded">
            <h4 className="font-medium mb-2">搜索结果:</h4>
            <ul className="list-disc pl-5">
              {data.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}

// useThrottle 示例
function UseThrottleExample() {
  const [scrollCount, { inc: incrementScrollCount, reset: resetScrollCount }] = useCounter(0);
  const throttledScrollCount = useThrottle(scrollCount, { wait: 100 });

  const handleScroll = () => {
    incrementScrollCount();
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">useThrottle 示例</h3>
      <div className="space-y-4">
        <div
          className="h-32 bg-gray-100 border-2 border-gray-300 rounded p-4 overflow-y-auto"
          onScroll={handleScroll}
        >
          <div className="h-64 bg-gradient-to-b from-blue-100 to-blue-200 rounded">
            <p className="p-4">滚动这个区域来测试节流效果</p>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-gray-50 p-3 rounded">
            <p className="text-sm text-gray-600">原始滚动次数</p>
            <p className="text-2xl font-bold text-blue-600">{scrollCount}</p>
          </div>
          <div className="bg-gray-50 p-3 rounded">
            <p className="text-sm text-gray-600">节流后次数</p>
            <p className="text-2xl font-bold text-green-600">{throttledScrollCount}</p>
          </div>
        </div>
        
        <button
          onClick={resetScrollCount}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          重置计数
        </button>
      </div>
    </div>
  );
}

// useBoolean 示例
function UseBooleanExample() {
  const [isModalOpen, { setTrue: openModal, setFalse: closeModal, toggle }] = useBoolean(false);

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">useBoolean 示例</h3>
      <div className="space-y-4">
        <div className="space-x-2">
          <button
            onClick={openModal}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            打开弹窗
          </button>
          <button
            onClick={closeModal}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            关闭弹窗
          </button>
          <button
            onClick={toggle}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            切换状态
          </button>
        </div>
        
        <p>弹窗状态: <span className="font-medium">{isModalOpen ? '打开' : '关闭'}</span></p>
        
        {isModalOpen && (
          <div className="bg-blue-50 border border-blue-200 p-4 rounded">
            <h4 className="font-medium mb-2">这是一个模拟弹窗</h4>
            <p className="text-gray-700 mb-3">这里是弹窗内容...</p>
            <button
              onClick={closeModal}
              className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              关闭
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

// useToggle 示例
function UseToggleExample() {
  const [status, { toggle, set }] = useToggle('pending', 'success');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600 bg-yellow-50';
      case 'success': return 'text-green-600 bg-green-50';
      case 'error': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">useToggle 示例</h3>
      <div className="space-y-4">
        <div className={`p-3 rounded font-medium ${getStatusColor(status)}`}>
          当前状态: {status}
        </div>
        
        <div className="space-x-2">
          <button
            onClick={toggle}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            切换状态
          </button>
          <button
            onClick={() => set('pending')}
            className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
          >
            设置为待处理
          </button>
          <button
            onClick={() => set('success')}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            设置为成功
          </button>
        </div>
      </div>
    </div>
  );
}

// useSetState 示例
function UseSetStateExample() {
  const [state, setState] = useSetState({
    name: '',
    age: 0,
    email: ''
  });

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">useSetState 示例</h3>
      <div className="space-y-4">
        <div className="grid grid-cols-1 gap-3">
          <input
            type="text"
            value={state.name}
            onChange={(e) => setState({ name: e.target.value })}
            placeholder="姓名"
            className="p-2 border border-gray-300 rounded"
          />
          <input
            type="number"
            value={state.age}
            onChange={(e) => setState({ age: parseInt(e.target.value) || 0 })}
            placeholder="年龄"
            className="p-2 border border-gray-300 rounded"
          />
          <input
            type="email"
            value={state.email}
            onChange={(e) => setState({ email: e.target.value })}
            placeholder="邮箱"
            className="p-2 border border-gray-300 rounded"
          />
        </div>
        
        <div className="bg-gray-50 p-4 rounded">
          <h4 className="font-medium mb-2">当前状态:</h4>
          <pre className="text-sm text-gray-700">
            {JSON.stringify(state, null, 2)}
          </pre>
        </div>
        
        <button
          onClick={() => setState({ name: '', age: 0, email: '' })}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          重置
        </button>
      </div>
    </div>
  );
}

// useCounter 示例
function UseCounterExample() {
  const [count, { inc, dec, set, reset }] = useCounter(0, { min: 0, max: 10 });

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">useCounter 示例</h3>
      <div className="space-y-4">
        <div className="text-center">
          <p className="text-3xl font-bold text-blue-600">{count}</p>
          <p className="text-sm text-gray-600">计数器 (范围: 0-10)</p>
        </div>
        
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={() => dec()}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            减少
          </button>
          <button
            onClick={() => inc()}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            增加
          </button>
          <button
            onClick={() => set(5)}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            设置为5
          </button>
          <button
            onClick={() => reset()}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            重置
          </button>
        </div>
      </div>
    </div>
  );
}

// useTitle 示例
function UseTitleExample() {
  const [title, setTitle] = useLocalStorageState('page-title', {
    defaultValue: 'AgentFoundry-Ziko'
  });

  useTitle(title);

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">useTitle 示例</h3>
      <div className="space-y-4">
        <div>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="修改页面标题"
            className="w-full p-2 border border-gray-300 rounded"
          />
        </div>
        
        <div className="text-sm text-gray-600">
          <p>当前页面标题: <span className="font-medium">{title}</span></p>
          <p>修改输入框内容会动态更新浏览器标题</p>
        </div>
        
        <div className="space-x-2">
          <button
            onClick={() => setTitle('生产环境')}
            className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
          >
            生产环境
          </button>
          <button
            onClick={() => setTitle('开发环境')}
            className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
          >
            开发环境
          </button>
          <button
            onClick={() => setTitle('测试环境')}
            className="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600"
          >
            测试环境
          </button>
        </div>
      </div>
    </div>
  );
}

// useMount 和 useUpdateEffect 示例
function UseLifecycleExample() {
  const [count, { inc }] = useCounter(0);
  const [mountTime, setMountTime] = useLocalStorageState('mount-time', {
    defaultValue: ''
  });

  useMount(() => {
    const time = new Date().toLocaleString();
    setMountTime(time);
    console.log('组件挂载时间:', time);
  });

  useUpdateEffect(() => {
    console.log('计数器更新:', count);
  }, [count]);

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">useMount & useUpdateEffect 示例</h3>
      <div className="space-y-4">
        <div className="bg-gray-50 p-3 rounded">
          <p className="text-sm text-gray-600">组件挂载时间</p>
          <p className="font-medium">{mountTime}</p>
        </div>
        
        <div className="bg-gray-50 p-3 rounded">
          <p className="text-sm text-gray-600">当前计数 (更新时会打印日志)</p>
          <p className="text-2xl font-bold text-blue-600">{count}</p>
        </div>
        
        <button
          onClick={() => inc()}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          增加计数 (查看控制台)
        </button>
        
        <div className="text-sm text-gray-600">
          <p>• useMount 在组件挂载时执行一次</p>
          <p>• useUpdateEffect 只在依赖更新时执行，跳过首次渲染</p>
        </div>
      </div>
    </div>
  );
}

// 主组件
export default function AhooksExamples() {
  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold text-center mb-8">ahooks 使用示例</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <UseRequestExample />
        <UseLocalStorageStateExample />
        <UseDebounceExample />
        <UseThrottleExample />
        <UseBooleanExample />
        <UseToggleExample />
        <UseSetStateExample />
        <UseCounterExample />
        <UseTitleExample />
        <UseLifecycleExample />
      </div>
      
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">使用说明</h2>
        <ul className="space-y-2 text-gray-700">
          <li>• <strong>useRequest</strong>: 处理异步请求，支持手动触发、错误处理等</li>
          <li>• <strong>useLocalStorageState</strong>: 同步状态与 localStorage</li>
          <li>• <strong>useDebounce</strong>: 防抖处理，适用于搜索输入等场景</li>
          <li>• <strong>useThrottle</strong>: 节流处理，适用于滚动事件等高频操作</li>
          <li>• <strong>useBoolean</strong>: 简化布尔值状态管理</li>
          <li>• <strong>useToggle</strong>: 在两个值之间切换</li>
          <li>• <strong>useSetState</strong>: 类似 Class 组件的 setState，支持对象合并</li>
          <li>• <strong>useCounter</strong>: 计数器管理，支持范围限制和各种操作</li>
          <li>• <strong>useTitle</strong>: 动态管理页面标题</li>
          <li>• <strong>useMount/useUpdateEffect</strong>: 生命周期管理，更精确的副作用控制</li>
        </ul>
        
        <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded p-4">
          <h3 className="font-semibold text-yellow-800 mb-2">🎯 ahooks 优先使用原则</h3>
          <p className="text-yellow-700 text-sm">
            项目中优先使用 ahooks 提供的 Hook，而不是手动实现或使用 React 原生 Hook。
            这样可以减少代码量，提高开发效率，并获得更好的功能性和可靠性。
          </p>
        </div>
      </div>
    </div>
  );
}