'use client';

import React, { useEffect, useRef } from 'react';

interface ConversationContextMenuProps {
  isVisible: boolean;
  position: { x: number; y: number };
  onClose: () => void;
  onRename: () => void;
  onDelete: () => void;
  isRenaming?: boolean;
  isDeleting?: boolean;
}

export default function ConversationContextMenu({
  isVisible,
  position,
  onClose,
  onRename,
  onDelete,
  isRenaming = false,
  isDeleting = false,
}: ConversationContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isVisible) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  return (
    <div
      ref={menuRef}
      className="fixed z-50"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: 'translate(100%, 0%)'
      }}
    >
      {/* 按设计稿样式实现菜单卡片 */}
      <div className="flex flex-col items-start p-1.5 mt-1.5 gap-1 w-40 h-[82px] bg-white border border-border-light shadow-lg rounded-lg">
        {/* 重命名按钮 */}
        <button
          onClick={onRename}
          disabled={isRenaming || isDeleting}
          className="flex flex-row items-center px-2 py-1.5 gap-3 w-37 h-[33px] rounded-md hover:bg-layer-2 transition-colors disabled:opacity-50"
        >
          {/* 重命名图标 */}
          <div className="w-4 h-4 flex-shrink-0">
            <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.5 15.5H9M9 15.5V1.5M9 15.5H10.5M9 1.5H7.5M9 1.5H10.5M5.5 4.50004H3C1.89543 4.50004 1 5.39548 1 6.50004V10.5C1 11.6046 1.89543 12.5 3 12.5H5.5M12.5 4.5L13 4.50001C14.1046 4.50003 15 5.39545 15 6.50001V10.5C15 11.6046 14.1045 12.5 13 12.5L12.5 12.5" stroke="black" strokeOpacity="0.72" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
</svg>

          </div>
          <span className="text-sm font-normal leading-5 text-text-primary">
            {isRenaming ? '重命名中...' : '重命名'}
          </span>
        </button>

        {/* 删除按钮 */}
        <button
          onClick={onDelete}
          disabled={isRenaming || isDeleting}
          className="flex flex-row items-center px-2 py-1.5 gap-3 w-37 h-[33px] rounded-md hover:bg-layer-2 transition-colors disabled:opacity-50"
        >
          {/* 删除图标 */}
          <div className="w-4 h-4 flex-shrink-0">
            {isDeleting ? (
              <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3 4.5V13.5C3 14.6046 3.89543 15.5 5 15.5H11C12.1046 15.5 13 14.6046 13 13.5V4.5M3 4.5H1M3 4.5L5.5 4.50004M13 4.5H15M13 4.5H10.5M10.5 4.5V2.49996C10.5 1.94768 10.0523 1.49996 9.49999 1.49996L6.49999 1.49999C5.94771 1.5 5.5 1.94771 5.5 2.49999V4.50004M10.5 4.5L5.5 4.50004M6.5 8.5V11.5M9.5 11.5V8.5" stroke="black" strokeOpacity="0.72" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
</svg>

            )}
          </div>
          <span className="text-sm font-normal leading-5 text-text-primary">
            {isDeleting ? '删除中...' : '删除'}
          </span>
        </button>
      </div>
    </div>
  );
}