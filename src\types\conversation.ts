/**
 * 会话系统类型定义
 */

// ============== 基础类型 ==============

/** 会话项 */
export interface ConversationItem {
  id: string;
  title: string;
  titleAlias?: string; // 用户自定义标题
  agent_id: string;
  agent_name: string;
  channel: string;
  created_time: string;
  updated_time: string;
  status: 'active' | 'archived' | 'deleted';
  task_id?: string; // agent task id
  tags?: string[];
  messageCount?: number;
  lastMessage?: string;
}

/** 会话消息 */
export interface ConversationMessage {
  message_id: string;
  conversation_id: string;
  text: string;
  states: object | null;
  rich_content: string;
  role: 'user' | 'assistant' | 'system';
  created_at: string;
  sender?: {
    id: string;
    name: string;
    role: string;
  };
  isStreaming?: boolean; // 流式消息标识
  payload?: string;
}

/** 分页参数 */
export interface PagerParams {
  page: number;
  size: number;
  sort?: 'created_time' | 'updated_time';
  order?: 'asc' | 'desc';
  offset?: number;
  returnTotal?: boolean;
}

/** 分页响应 */
export interface PaginatedResponse<T> {
  items: T[];
  count: number;
}

// ============== API 接口类型 ==============

/** 获取会话列表请求参数 */
export interface GetConversationsParams {
  user_id: string; // 本项目必须带user_id, 否则如果user为admin，会把整个租户下的会话都返回
  agent_id?: string | null;
  pager: PagerParams;
  channel: string; // 本项目写死为 "employee"
  status?: string | null;
  taskId?: string | null;
  states?: string[];
  tags?: string[];
}

/** 创建会话请求参数 */
export interface CreateConversationParams {
  agentId: string;
}

/** 发送消息请求参数 */
export interface SendMessageParams {
  agentId: string;
  conversationId: string;
  text: string;
  states?: string[];
  postback?: any;
}

/** 更新会话标题请求参数 */
export interface UpdateConversationTitleParams {
  conversationId: string;
  newTitleAlias: string;
}

// ============== Hook 状态类型 ==============

/** 会话列表状态 */
export interface ConversationListState {
  conversations: ConversationItem[];
  loading: boolean;
  error?: string | null;
  hasMore: boolean;
  currentPage: number;
  total: number;
}

/** 消息列表状态 */
export interface ConversationMessagesState {
  messages: ConversationMessage[];
  loading: boolean;
  error?: string | null;
  streamingMessageId?: string | null;
}

/** 会话操作状态 */
export interface ConversationActionsState {
  creating: boolean;
  deleting: string | null; // 正在删除的会话ID
  renaming: string | null; // 正在重命名的会话ID
  sending: boolean;
}

/** 全局会话状态 */
export interface ConversationGlobalState {
  currentConversationId?: string | null;
  agentId: string;
  isConnected: boolean;
  lastActivity: number;
}

// ============== Hook 返回值类型 ==============

/** useConversationList Hook 返回值 */
export interface UseConversationListReturn {
  // 状态
  conversations: ConversationItem[];
  loading: boolean;
  error?: string | null;
  hasMore: boolean;
  
  // 操作方法
  loadConversations: () => Promise<void>;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  createConversation: (params?: Partial<CreateConversationParams>) => Promise<ConversationItem | null>;
  deleteConversation: (conversationId: string) => Promise<boolean>;
  renameConversation: (conversationId: string, newTitle: string) => Promise<boolean>;
  
  // 状态查询
  isCreating: boolean;
  isDeletingConversation: (conversationId: string) => boolean;
  isRenamingConversation: (conversationId: string) => boolean;
}

/** useConversationMessages Hook 返回值 (已废弃 - 使用新的聊天引擎) */
export interface UseConversationMessagesReturn {
  // 状态
  messages: ConversationMessage[];
  loading: boolean;
  error?: string | null;
  
  // 操作方法
  sendMessage: (content: string) => Promise<boolean>;
  loadMessages: (conversationId: string) => Promise<void>;
  clearMessages: () => void;
  retryMessage: (messageId: string) => Promise<boolean>;
  
  // 状态查询
  isSending: boolean;
  isStreaming: boolean;
  streamingMessageId?: string | null;
}

/** useConversationActions Hook 返回值 */
export interface UseConversationActionsReturn {
  // 会话操作
  createConversation: (params?: Partial<CreateConversationParams>) => Promise<ConversationItem | null>;
  deleteConversation: (conversationId: string) => Promise<boolean>;
  renameConversation: (conversationId: string, newTitle: string) => Promise<boolean>;
  
  // 消息操作
  sendMessage: (conversationId: string, content: string) => Promise<boolean>;
  
  // 状态查询
  isCreating: boolean;
  isDeletingConversation: (conversationId: string) => boolean;
  isRenamingConversation: (conversationId: string) => boolean;
  isSendingMessage: boolean;
}

/** useConversationState Hook 返回值 */
export interface UseConversationStateReturn {
  // 全局状态
  currentConversationId?: string | null;
  agentId: string;
  isConnected: boolean;
  
  // 状态操作
  setCurrentConversation: (conversationId: string | null) => void;
  updateAgentId: (agentId: string) => void;
  
  // 连接状态
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
}

/** useConversation Hook 返回值 (核心 Hook) */
export interface UseConversationReturn {
  // 会话列表相关
  conversations: ConversationItem[];
  conversationsLoading: boolean;
  conversationsError?: string | null;
  hasMoreConversations: boolean;
  
  // 消息相关
  messages: ConversationMessage[];
  messagesLoading: boolean;
  messagesError?: string | null;
  
  // 当前状态
  currentConversationId?: string | null;
  agentId: string;
  isConnected: boolean;
  
  // 会话操作
  loadConversations: () => Promise<void>;
  loadMoreConversations: () => Promise<void>;
  refreshConversations: () => Promise<void>;
  createConversation: (params?: Partial<CreateConversationParams>) => Promise<ConversationItem | null>;
  deleteConversation: (conversationId: string) => Promise<boolean>;
  renameConversation: (conversationId: string, newTitle: string) => Promise<boolean>;
  
  // 消息操作
  setCurrentConversation: (conversationId: string | null) => Promise<void>;
  sendMessage: (content: string) => Promise<boolean>;
  loadMessages: (conversationId: string) => Promise<void>;
  clearMessages: () => void;
  
  // 状态查询
  isCreatingConversation: boolean;
  isDeletingConversation: (conversationId: string) => boolean;
  isRenamingConversation: (conversationId: string) => boolean;
  isSendingMessage: boolean;
  isStreamingMessage: boolean;
}

// ============== 配置类型 ==============

/** 会话系统配置 */
export interface ConversationConfig {
  agentId: string;
  apiBaseUrl?: string;
  wsUrl?: string;
  pageSize?: number;
  autoConnect?: boolean;
  retryConfig?: {
    maxRetries: number;
    retryDelay: number;
  };
}

// ============== 错误类型 ==============

/** 会话系统错误 */
export interface ConversationError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
}

/** 网络错误类型 */
export type NetworkErrorType = 
  | 'NETWORK_ERROR'
  | 'TIMEOUT_ERROR'
  | 'AUTH_ERROR'
  | 'PERMISSION_ERROR'
  | 'SERVER_ERROR'
  | 'VALIDATION_ERROR'
  | 'UNKNOWN_ERROR';