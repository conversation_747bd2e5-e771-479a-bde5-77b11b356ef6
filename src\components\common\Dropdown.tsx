import React, { useState, useRef, useEffect } from "react";
import { useClickAway } from "ahooks";

interface DropdownProps {
  options: { label: string; value: string }[];
  value: string | string[];
  onChange: (value: string | string[]) => void;
  placeholder?: string;
  className?: string;
  multiple?: boolean;
  showClearButton?: boolean; // 添加控制是否显示清空按钮的属性
  onClose?: () => void; // 添加关闭回调
}

export default function Dropdown({
  options,
  value,
  onChange,
  placeholder = "请选择",
  className = "",
  multiple = false,
  showClearButton = true, // 默认显示清空按钮
  onClose,
}: DropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  // 临时存储选中的值，用于多选模式
  const [tempSelectedValues, setTempSelectedValues] = useState<string[]>([]);

  // 初始化临时选中值
  useEffect(() => {
    if (multiple) {
      setTempSelectedValues(value as string[]);
    }
  }, [value, multiple]);

  // 点击外部区域关闭下拉框
  useClickAway(() => {
    if (isOpen) {
      setIsOpen(false);
      // 如果是多选模式，应用临时选中的值
      if (multiple && onClose) {
        onClose();
      }
    }
  }, dropdownRef);

  // 获取当前选中项的标签
  const getSelectedLabel = () => {
    if (multiple) {
      const selectedValues = value as string[];
      if (selectedValues.length === 0)
        return { label: placeholder, isPlaceholder: true };

      // 获取选中项的标签
      const selectedLabels = selectedValues
        .map((val) => {
          const option = options.find((opt) => opt.value === val);
          return option ? option.label : "";
        })
        .filter((label) => label); // 过滤掉空标签

      if (selectedLabels.length === 0)
        return { label: placeholder, isPlaceholder: true };

      // 只显示前两个标签，用 · 分隔
      if (selectedLabels.length === 1) {
        return { label: selectedLabels[0], isPlaceholder: false };
      } else if (selectedLabels.length === 2) {
        return {
          label: `${selectedLabels[0]} · ${selectedLabels[1]}`,
          isPlaceholder: false,
        };
      } else {
        return {
          label: `${selectedLabels[0]} · ${selectedLabels[1]} ...`,
          isPlaceholder: false,
        };
      }
    } else {
      const selectedOption = options.find((option) => option.value === value);
      if (selectedOption) {
        return { label: selectedOption.label, isPlaceholder: false };
      } else {
        return { label: placeholder, isPlaceholder: true };
      }
    }
  };

  // 处理选项点击
  const handleOptionClick = (optionValue: string) => {
    if (multiple) {
      // 更新临时选中值
      const newTempValue = tempSelectedValues.includes(optionValue)
        ? tempSelectedValues.filter((v) => v !== optionValue)
        : [...tempSelectedValues, optionValue];
      setTempSelectedValues(newTempValue);
      // 同时更新实际值
      onChange(newTempValue);
    } else {
      onChange(optionValue);
      setIsOpen(false);
    }
  };

  // 清除所有选择
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (multiple) {
      setTempSelectedValues([]);
    }
    onChange(multiple ? [] : "");
  };

  // 检查选项是否被选中
  const isOptionSelected = (optionValue: string) => {
    if (multiple) {
      return tempSelectedValues.includes(optionValue);
    } else {
      return value === optionValue;
    }
  };

  // 判断是否应该显示清空按钮
  const shouldShowClearButton = () => {
    if (!showClearButton) return false;

    if (multiple) {
      return (value as string[]).length > 0;
    } else {
      return !!value;
    }
  };

  // 判断是否有选中的标签
  const hasSelectedValues = () => {
    if (multiple) {
      return (value as string[]).length > 0;
    } else {
      return !!value;
    }
  };

  return (
    <div ref={dropdownRef} className={`relative ${className}`}>
      {/* 下拉框触发器 */}
      <div
        className="flex items-center justify-between bg-[#F3F3F3] h-[37px] px-3 rounded-[8px] cursor-pointer border border-[#0000000F]"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex-1 overflow-hidden">
          {(() => {
            const { label, isPlaceholder } = getSelectedLabel();
            return (
              <span
                className={`text-[14px] font-normal truncate block ${
                  isPlaceholder ? "text-[#0000003D]" : "text-[#000000B8]"
                }`}
              >
                {label}
              </span>
            );
          })()}
        </div>

        {/* 根据是否有选中值显示不同的图标 */}
        {hasSelectedValues() && showClearButton ? (
          <button
            onClick={handleClear}
            className="focus:outline-none flex-shrink-0"
          >
            <img src="/clean.svg" alt="clear" />
          </button>
        ) : (
          <img
            src="/assets/sidebar/chevron-down.svg"
            alt="chevron-down"
            className="flex-shrink-0"
          />
        )}
      </div>

      {/* 下拉选项 */}
      {isOpen && (
        <div className="p-[6px] absolute left-0 mt-1 w-full bg-white rounded-[8px] shadow-lg z-50 border border-[#0000000F] max-h-[280px] overflow-y-auto">
          {options.map((option) => (
            <div
              key={option.value}
              className={`px-2 py-[6px] h-[32px] text-[13px] font-normal text-[#000000B8] rounded-[6px] cursor-pointer hover:bg-[#0000000A] flex items-center justify-between`}
              onClick={() => handleOptionClick(option.value)}
            >
              <span>{option.label}</span>
              {isOptionSelected(option.value) && (
                <img src="/sucess.svg" alt="sucess" />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
