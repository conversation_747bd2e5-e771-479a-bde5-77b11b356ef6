# AgentFoundry-<PERSON><PERSON> 项目文档

## 项目概述

AgentFoundry-<PERSON><PERSON> 是一个基于最新技术栈的现代 Web 应用项目，实现了完整的 AG-UI hooks 系统和 CopilotKit 风格的 AI 交互功能。

## 技术栈

### 核心框架
- **Next.js**: 15.3.5 (App Router)
- **React**: 19.0.0 
- **TypeScript**: 5.x
- **Tailwind CSS**: 4.x

### 关键依赖
- **ahooks**: 3.9.0 (React Hooks 库)
- **@microsoft/signalr**: SignalR 客户端
- **AG-UI 协议**: 自研 hooks 系统

## 项目结构

```
├── src/
│   ├── app/           # App Router 页面
│   ├── components/    # 通用组件
│   ├── hooks/         # 自定义 Hooks
│   ├── lib/           # 工具库
│   │   └── agui/      # AG-UI 系统
│   └── types/         # TypeScript 类型
├── docs/              # 项目文档
│   ├── README.md          # 项目总览 (本文件)
│   ├── development-guide.md  # 开发规范
│   └── tech-reference.md    # 技术参考
└── public/            # 静态资源
```

## 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发命令
```bash
# 启动开发服务器 (使用 Turbopack)
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start

# 代码检查
npm run lint

# 类型检查
npm run type-check
```

### 访问应用
- 开发环境: http://localhost:3000
- 生产环境: 根据部署配置

## 核心特性

### 1. AG-UI Hooks 系统
- 基于 conversationId 的有状态会话管理
- 工具定义随消息发送的机制
- SignalR 实时通信
- 完整的 CopilotKit API 兼容

### 2. ahooks 优先开发
- 优先使用 ahooks 而非 React 原生 Hook
- 完整的 SSR 支持
- 稳定的函数引用

### 3. 主题系统
- 完整的 Light 主题颜色系统
- 语义化 CSS 变量
- 禁用硬编码颜色

## 开发规范

详细的开发规范请参考 [开发规范文档](./development-guide.md)

## 技术参考

详细的技术实现参考 [技术参考文档](./tech-reference.md)

## 贡献指南

1. 遵循项目的三阶段工作流
2. 优先使用 ahooks
3. 使用主题变量而非硬编码颜色
4. 保持代码整洁和类型安全

## 许可证

本项目基于 MIT 许可证开源。