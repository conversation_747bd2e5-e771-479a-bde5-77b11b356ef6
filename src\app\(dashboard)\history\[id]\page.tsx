'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import ChatViewer from '@/components/history/ChatViewer';
import { ChatEngineProvider } from '@/components/ChatEngineProvider';

export default function HistoryDetailPage() {
  const params = useParams();
  const conversationId = params.id as string;

  // 验证 conversationId 参数
  if (!conversationId || typeof conversationId !== 'string') {
    return (
      <div className="flex items-center justify-center h-full bg-layer-1">
        <div className="text-center space-y-4">
          <div className="text-error text-lg font-medium">
            ❌ 无效的会话ID
          </div>
          <div className="text-text-secondary text-sm">
            请检查URL参数是否正确
          </div>
        </div>
      </div>
    );
  }

  const agentId = process.env.NEXT_PUBLIC_CONVERSATION_AGENT_ID || '1';
  return (
    <ChatEngineProvider conversationId={conversationId}>
      <ChatViewer conversationId={conversationId} />
    </ChatEngineProvider>
  );
}