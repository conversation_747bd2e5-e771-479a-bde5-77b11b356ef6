"use client";

import React, { useEffect, useRef } from "react";
import { Action } from "./KnowledgeCard";

interface KnowledgeContextMenuProps {
  isVisible: boolean;
  position: { x: number; y: number };
  onClose: () => void;
  onAction: (type: Action["type"]) => void;
  actionButtons: Action[];
}

export default function KnowledgeContextMenu({
  isVisible,
  position,
  onClose,
  onAction,
  actionButtons,
}: KnowledgeContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isVisible) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  const getActionIcon = (type: Action["type"]) => {
    switch (type) {
      case "edit":
        return "/assets/ai-employees/edit.svg";
      case "del":
        return "/assets/ai-employees/delete.svg";
      case "enable":
        return "/assets/ai-employees/Enable.svg";
      case "disable":
        return "/assets/ai-employees/Enable.svg";
      default:
        return "/assets/ai-employees/edit.svg";
    }
  };

  const getActionColor = (type: Action["type"]) => {
    switch (type) {
      case "del":
        return "text-[#000000B8]";
      case "enable":
        return "text-[#000000B8]";
      case "disable":
        return "text-[#000000B8]";
      default:
        return "text-[#000000B8]";
    }
  };

  return (
    <div
      ref={menuRef}
      className="fixed z-50"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: "translate(-100%, 0%)",
      }}
    >
      <div className="flex flex-col items-start p-1.5 mt-1.5 gap-1 w-[160px] bg-white border border-[#0000000F] shadow-lg rounded-lg">
        {actionButtons.map((action, index) => (
          <React.Fragment key={action.type}>
            <button
              onClick={() => {
                onAction(action.type);
                onClose();
              }}
              className="cursor-pointer flex flex-row items-center px-2 py-1.5 gap-3 w-full h-[33px] rounded-md hover:bg-[#0000000A] transition-colors"
            >
              <img 
                src={getActionIcon(action.type)} 
                alt={action.type}
                className="w-4 h-4"
              />
              <span className={`text-[14px] font-normal leading-[100%] ${getActionColor(action.type)}`}>
                {action.label}
              </span>
            </button>
            {/* 在编辑和删除后添加分割线 */}
            {(action.type === "del" && index < actionButtons.length - 1) && (
              <div className="w-full h-[1px] bg-[#0000000F]"></div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}
