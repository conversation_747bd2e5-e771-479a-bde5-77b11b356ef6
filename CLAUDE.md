# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# AI助手核心规则

- 用中文回答。
- 文档类交付物，请使用markdown格式，归档到docs目录。

## 三阶段工作流

### 阶段一：分析问题
**声明格式**：`【分析问题】`

**必须做的事**：
- 深入理解需求本质
- 搜索所有相关代码
- 识别问题根因
- 发现架构问题
- 如果有不清楚的，请向我收集必要的信息
- 提供1~3个解决方案（如果方案与用户想达成的目标有冲突，则不应该成为一个方案）。
- 评估每个方案的优劣

**融入的原则**：
- 系统性思维：看到具体问题时，思考整个系统
- 第一性原理：从功能本质出发，而不是现有代码
- DRY原则：发现重复代码必须指出
- 长远考虑：评估技术债务和维护成本

**绝对禁止**：
- ❌ 修改任何代码
- ❌ 急于给出解决方案
- ❌ 跳过搜索和理解步骤
- ❌ 不分析就推荐方案

### 阶段二：细化方案
**声明格式**：`【细化方案】`

**前置条件**：
- 用户明确选择了方案（如："用方案1"、"实现这个"）

**必须做的事**：
- 避免过度工程化，不要引入不必要的复杂性
- 列出变更（新增、修改、删除）的文件，简要描述每个文件的变化。


### 阶段三：执行方案
**声明格式**：`【执行方案】`


**必须做的事**：
- 严格按照选定方案实现
- 修改后运行类型检查（npm run type-check， 要选择子目录）

**绝对禁止**：
- ❌ 提交代码（除非用户明确要求）
- 启动开发服务器

## 🚨 阶段切换规则

1. **默认阶段**：收到新问题时，始终从【分析问题】开始
2. **切换条件**：只有用户明确指示时才能切换阶段
3. **禁止行为**：不允许在一次回复中同时进行两个阶段

## ⚠️ 每次回复前的强制检查

```
□ 我在回复开头声明了阶段吗？
□ 我的行为符合当前阶段吗？
□ 如果要切换阶段，用户同意了吗？
```

## 项目概述

AgentFoundry-Ziko 是一个基于最新 Next.js 15.3.5 和 React 19 的现代 Web 应用项目。

## 开发环境配置

### 主要依赖
- **Next.js**: 15.3.5 (最新版本)
- **React**: 19.0.0 (最新版本)
- **TypeScript**: 5.x
- **Tailwind CSS**: 4.x (最新版本)
- **ESLint**: 9.x
- **ahooks**: 3.9.0 (React Hooks 库)

## ahooks 集成

### 使用说明
项目已集成 ahooks 库，提供了丰富的 React Hooks 来简化开发。

### 常用 Hooks
- `useRequest`: 处理异步请求
- `useLocalStorageState`: 本地存储状态同步
- `useDebounce`: 防抖处理
- `useThrottle`: 节流处理
- `useBoolean`: 布尔值状态管理
- `useToggle`: 多值切换

## ahooks 优先使用原则
### 🎯 核心原则
**当 ahooks 提供了相应功能时，优先使用 ahooks 而非 React 原生 Hook**

### 优先级规则
1. **第一优先级**: 使用 ahooks 提供的 Hook
2. **第二优先级**: 基于 ahooks 封装的自定义 Hook
3. **第三优先级**: React 原生 Hook

### 替换策略
- `useState` → `useBoolean` / `useToggle` / `useCounter` / `useSetState` / `useLocalStorageState`
- `useEffect` → `useMount` / `useUnmount` / `useUpdateEffect` / `useRequest`
- 手动防抖节流 → `useDebounce` / `useThrottle`
- 手动网络请求 → `useRequest`

### 开发规范
- 新功能开发必须遵循 ahooks 优先原则
- 代码审查时检查是否有可优化的 Hook 使用
- 优先重构高频使用的组件
- 建立团队共识和培训机制

## 主题样式开发规范

### 🎨 强制要求：避免非主题样式

**核心原则**：开发过程中严禁使用硬编码颜色和非主题样式，必须使用项目的主题变量系统。

### 禁止的样式写法
```html
❌ 硬编码颜色
<div class="bg-[#fafafb] text-[#333333]">
<div class="bg-gray-50 text-gray-900">

❌ 非主题的 Tailwind 颜色
<button class="bg-blue-500 hover:bg-blue-600">
<span class="text-red-500">
```

### 正确的样式写法
```html
✅ 使用主题变量
<div class="bg-layer-1 text-text-primary">
<button class="bg-primary hover:bg-blue-600">
<span class="text-error">
<div class="bg-purple border-border-light">
```

### 开发检查清单
- [ ] 所有颜色都使用主题变量（`bg-layer-*`、`text-*`、`border-*`）
- [ ] 功能色使用语义化命名（`bg-success`、`text-error`、`bg-brand`）
- [ ] 设计稿色板正确应用（`bg-purple`、`text-blue-light`）
- [ ] 避免任何硬编码的十六进制颜色值
- [ ] 不使用 Tailwind 默认颜色系统（如 `gray-*`、`blue-*`）

### 相关文档
- **主题使用指南**: `docs/theme-usage-guide.md`
- **设计稿色板**: 参考项目根目录的设计稿图片
