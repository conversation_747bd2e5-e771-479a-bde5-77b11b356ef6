'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface GlobalLoadingProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  text?: string;
}

export default function GlobalLoading({ 
  className, 
  size = 'md', 
  text = '加载中...' 
}: GlobalLoadingProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <div className={cn(
      'flex flex-col items-center justify-center gap-3 p-8',
      className
    )}>
      <div className={cn(
        'animate-spin rounded-full border-2 border-border-light border-t-primary',
        sizeClasses[size]
      )} />
      {text && (
        <span className="text-text-secondary text-sm font-medium">
          {text}
        </span>
      )}
    </div>
  );
}

export function PageLoading() {
  return (
    <div className="fixed inset-0 bg-bg-base flex items-center justify-center z-50">
      <GlobalLoading size="lg" text="正在加载页面..." />
    </div>
  );
}

export function ComponentLoading({ className }: { className?: string }) {
  return (
    <div className={cn('flex-1 flex items-center justify-center min-h-[200px]', className)}>
      <GlobalLoading />
    </div>
  );
}