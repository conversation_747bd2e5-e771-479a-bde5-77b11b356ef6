'use client';

import React, { useEffect } from 'react';
import { 
  useChatState, 
  useChatMessages, 
  useChatStore 
} from '@/store/chatStore';
import ChatInput from '@/components/chat/ChatInput';
import MessageList from '@/components/chat/MessageList';
import ChatStatus from '@/components/chat/ChatStatus';

interface ChatViewerProps {
  conversationId: string;
}

export default function ChatViewer({ conversationId }: ChatViewerProps) {
  // 获取统一聊天状态
  const { 
    error, 
    isDisconnected, 
    isInitialized, 
    switchConversation, 
    clearError 
  } = useChatState();

  // 获取消息加载状态
  const { loading: messagesLoading } = useChatMessages();

  // 获取初始化方法
  const initialize = useChatStore(state => state.initialize);

  // 初始化聊天引擎并切换会话
  useEffect(() => {
    if (!isInitialized) {
      initialize({
        agentId: process.env.NEXT_PUBLIC_CONVERSATION_AGENT_ID || 'default-agent',
        conversationId
      });
    } else if (conversationId) {
      switchConversation(conversationId);
    }
  }, [isInitialized, initialize, conversationId, switchConversation]);

  // 错误状态处理
  if (error) {
    return (
      <div className="flex flex-col h-full items-center justify-center space-y-4">
        <div className="text-center">
          <svg className="w-12 h-12 mx-auto text-error mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div className="text-error text-sm font-medium">聊天出现错误</div>
          <div className="text-text-muted text-xs mt-1">{error}</div>
        </div>
        <button 
          onClick={clearError}
          className="px-4 py-2 bg-primary text-bg-base rounded-lg hover:bg-blue-600 transition-colors text-sm"
        >
          重试
        </button>
      </div>
    );
  }

  // 会话不存在的处理
  if (!conversationId) {
    return (
      <div className="flex flex-col h-full items-center justify-center space-y-4">
        <svg className="w-16 h-16 text-text-muted" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
        <div className="text-center">
          <div className="text-text-muted text-sm">请选择一个会话开始查看</div>
        </div>
      </div>
    );
  }

  // 引擎未初始化处理
  if (!isInitialized) {
    return (
      <div className="flex flex-col h-full items-center justify-center space-y-4">
        <div className="text-center">
          <svg className="w-4 h-4 animate-spin mx-auto mb-4" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <div className="text-text-muted text-sm">正在初始化聊天引擎...</div>
        </div>
      </div>
    );
  }

  // 连接断开处理
  if (isDisconnected) {
    return (
      <div className="flex flex-col h-full items-center justify-center space-y-4">
        <div className="text-center">
          <svg className="w-12 h-12 mx-auto text-text-muted mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364" />
          </svg>
          <div className="text-text-muted text-sm font-medium">连接已断开</div>
          <div className="text-text-muted text-xs mt-1">正在尝试重新连接...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-layer-1">
      {/* 消息区域 - 占据大部分空间，可滚动 */}
      <div className="flex-1 min-h-0 overflow-hidden">
        <MessageList />
      </div>

      {/* 底部输入区域 - 固定在底部 */}
      <div className="flex-shrink-0 p-6">
        <ChatInput />
      </div>
    </div>
  );
}