import React from "react";

interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  onClear?: () => void;
  placeholder?: string;
  className?: string;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
}

export default function SearchInput({
  value,
  onChange,
  onClear,
  placeholder = "搜索...",
  className = "",
  onKeyDown,
}: SearchInputProps) {
  return (
    <div
      className={`flex items-center bg-[#FFFFFF] h-[48px] w-full px-4 border rounded-[12px] shadow-[0px_2px_4px_0px_#00000014] border-[#0000000F]`}
      style={{ minWidth: 0 }}
    >
      {/* 搜索图标 */}
      <img src="/assets/search/search.svg" alt="" />
      {/* 输入框 */}
      <input
        className="flex-1 px-3 bg-transparent font-normal outline-none text-[16px] text-[#000000B8] placeholder:text-[#00000040]"
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        onKeyDown={onKeyDown}
      />
      {/* 清空图标 */}
      {value && (
        <button
          type="button"
          className="ml-2 focus:outline-none cursor-pointer hover:bg-[#00000014] rounded-full p-1 transition-colors"
          onClick={onClear}
          tabIndex={-1}
        >
          <img src="/assets/search/clean.svg" alt="" />
        </button>
      )}
    </div>
  );
}
