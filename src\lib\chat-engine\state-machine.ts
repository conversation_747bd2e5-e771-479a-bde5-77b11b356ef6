'use client';

import { 
  ChatState, 
  ChatStateData, 
  ChatStateMachine, 
  ChatEvent, 
  ChatMessage,
  EventBus
} from './types';

/**
 * 聊天状态机实现
 * 管理聊天的所有状态转换和数据
 */
export class ChatStateMachineImpl implements ChatStateMachine {
  private currentState: ChatState = ChatState.IDLE;
  private stateData: ChatStateData;
  private subscribers = new Set<(state: ChatStateData) => void>();
  private eventBus: EventBus;
  private enableLogging = false;

  /**
   * 安全获取事件payload
   */
  private getPayload(event: ChatEvent): any {
    return event.payload as any;
  }

  constructor(eventBus: EventBus, options?: { enableLogging?: boolean }) {
    this.eventBus = eventBus;
    this.enableLogging = options?.enableLogging ?? false;
    
    // 初始化状态数据
    this.stateData = {
      currentState: this.currentState,
      conversationId: null,
      messages: [],
      currentInput: '',
      isConnected: false,
      error: null,
      streamingMessageId: null
    };

    // 监听相关事件
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 用户输入事件
    this.eventBus.on('USER_TYPING', (event) => {
      this.handleUserTyping(event);
    });

    // 用户发送消息事件
    this.eventBus.on('USER_SEND_MESSAGE', (event) => {
      this.handleUserSendMessage(event);
    });

    // 消息发送成功事件
    this.eventBus.on('MESSAGE_SENT', (event) => {
      this.handleMessageSent(event);
    });

    // 消息接收事件
    this.eventBus.on('MESSAGE_RECEIVED', (event) => {
      this.handleMessageReceived(event);
    });

    // 流式消息事件
    this.eventBus.on('STREAMING_START', (event) => {
      this.handleStreamingStart(event);
    });

    this.eventBus.on('STREAMING_CONTENT', (event) => {
      this.handleStreamingContent(event);
    });

    this.eventBus.on('STREAMING_END', (event) => {
      this.handleStreamingEnd(event);
    });

    // RUN_FINISHED事件处理
    this.eventBus.on('RUN_FINISHED', (event) => {
      this.handleRunFinished(event);
    });

    // 连接状态事件
    this.eventBus.on('CONNECTION_STATUS', (event) => {
      this.handleConnectionStatus(event);
    });

    // 错误事件
    this.eventBus.on('ERROR', (event) => {
      this.handleError(event);
    });

    // 会话切换事件
    this.eventBus.on('CONVERSATION_SWITCHED', (event) => {
      this.handleConversationSwitched(event);
    });

    // 消息加载完成事件
    this.eventBus.on('MESSAGES_LOADED', (event) => {
      this.handleMessagesLoaded(event);
    });
  }

  /**
   * 获取当前状态
   */
  getCurrentState(): ChatState {
    return this.currentState;
  }

  /**
   * 获取状态数据
   */
  getStateData(): ChatStateData {
    return { ...this.stateData };
  }

  /**
   * 状态转换
   */
  transition(event: ChatEvent): void {
    const prevState = this.currentState;
    const newState = this.calculateNextState(this.currentState, event);

    if (newState !== prevState) {
      this.currentState = newState;
      this.stateData.currentState = newState;

      console.log(`🔄 [StateMachine] 状态转换: ${prevState} → ${newState}`, {
        eventType: event.type,
        messagesCount: this.stateData.messages.length,
        conversationId: this.stateData.conversationId,
        currentInput: this.stateData.currentInput?.slice(0, 30) || ''
      });

      this.notifySubscribers();
    }
  }

  /**
   * 计算下一个状态
   */
  private calculateNextState(currentState: ChatState, event: ChatEvent): ChatState {
    switch (currentState) {
      case ChatState.IDLE:
        switch (event.type) {
          case 'USER_TYPING':
            return ChatState.TYPING;
          case 'USER_SEND_MESSAGE':
            return ChatState.SENDING;
          case 'CONNECTION_STATUS':
            const payload = this.getPayload(event);
            return payload.status === 'connected' ? ChatState.IDLE : ChatState.DISCONNECTED;
          case 'ERROR':
            return ChatState.ERROR;
          case 'RUN_FINISHED':
            return ChatState.IDLE; // 确保保持IDLE状态
          default:
            return currentState;
        }

      case ChatState.TYPING:
        switch (event.type) {
          case 'USER_TYPING':
            return ChatState.TYPING; // 保持打字状态
          case 'USER_SEND_MESSAGE':
            return ChatState.SENDING;
          case 'ERROR':
            return ChatState.ERROR;
          case 'RUN_FINISHED':
            return ChatState.IDLE; // RUN_FINISHED重置为IDLE
          default:
            // 打字状态超时回到空闲
            return ChatState.IDLE;
        }

      case ChatState.SENDING:
        switch (event.type) {
          case 'MESSAGE_SENT':
            return ChatState.RECEIVING;
          case 'ERROR':
            return ChatState.ERROR;
          case 'RUN_FINISHED':
            return ChatState.IDLE; // RUN_FINISHED重置为IDLE
          default:
            return currentState;
        }

      case ChatState.RECEIVING:
        switch (event.type) {
          case 'MESSAGE_RECEIVED':
            return ChatState.IDLE;
          case 'STREAMING_START':
            return ChatState.STREAMING;
          case 'ERROR':
            return ChatState.ERROR;
          case 'RUN_FINISHED':
            return ChatState.IDLE; // RUN_FINISHED重置为IDLE
          default:
            return currentState;
        }

      case ChatState.STREAMING:
        switch (event.type) {
          case 'STREAMING_END':
            return ChatState.IDLE;
          case 'ERROR':
            return ChatState.ERROR;
          case 'RUN_FINISHED':
            return ChatState.IDLE; // RUN_FINISHED重置为IDLE
          default:
            return currentState;
        }

      case ChatState.ERROR:
        switch (event.type) {
          case 'USER_SEND_MESSAGE':
            return ChatState.SENDING; // 重试
          case 'CONNECTION_STATUS':
            const payload = this.getPayload(event);
            return payload.status === 'connected' ? ChatState.IDLE : ChatState.DISCONNECTED;
          case 'RUN_FINISHED':
            return ChatState.IDLE; // RUN_FINISHED重置为IDLE
          default:
            return currentState;
        }

      case ChatState.DISCONNECTED:
        switch (event.type) {
          case 'CONNECTION_STATUS':
            const payload = this.getPayload(event);
            return payload.status === 'connected' ? ChatState.IDLE : ChatState.DISCONNECTED;
          case 'RUN_FINISHED':
            return ChatState.IDLE; // RUN_FINISHED重置为IDLE
          default:
            return currentState;
        }

      default:
        return currentState;
    }
  }

  /**
   * 处理用户输入事件
   */
  private handleUserTyping(event: ChatEvent): void {
    // const payload = this.getPayload(event);
    // if (payload?.content !== undefined) {
    //   console.log('✍️ [StateMachine] handleUserTyping', {
    //     content: payload.content.slice(0, 50) + (payload.content.length > 50 ? '...' : ''),
    //     conversationId: payload.conversationId,
    //     currentState: this.currentState,
    //     currentMessagesCount: this.stateData.messages.length,
    //     beforeInput: this.stateData.currentInput
    //   });
      
    //   this.stateData.currentInput = payload.content;
    //   this.stateData.conversationId = payload.conversationId;
    //   this.transition(event);
    //   this.notifySubscribers();
    // }
  }

  /**
   * 处理用户发送消息事件
   */
  private handleUserSendMessage(event: ChatEvent): void {
    const payload = this.getPayload(event);
    if (payload?.content) {
      console.log('📤 [StateMachine] handleUserSendMessage', {
        content: payload.content.slice(0, 50),
        conversationId: payload.conversationId,
        currentMessagesCount: this.stateData.messages.length
      });
      
      // 创建用户消息
      const userMessage: ChatMessage = {
        id: `user_${Date.now()}`,
        conversationId: payload.conversationId,
        role: 'user',
        content: payload.content,
        timestamp: Date.now(),
        status: 'pending'
      };

      // 添加到消息列表
      const newMessages = [...this.stateData.messages, userMessage];
      this.stateData.messages = newMessages;
      this.stateData.currentInput = '';
      this.stateData.conversationId = payload.conversationId;
      
      console.log('✅ [StateMachine] 用户消息已添加', {
        messageId: userMessage.id,
        newMessagesCount: newMessages.length,
        messageContent: userMessage.content.slice(0, 50)
      });
      
      this.transition(event);
      this.notifySubscribers();
    }
  }

  /**
   * 处理消息发送成功事件
   */
  private handleMessageSent(event: ChatEvent): void {
    const payload = this.getPayload(event);
    if (payload?.message) {
      // 更新消息状态
      this.stateData.messages = this.stateData.messages.map(msg => 
        msg.id === payload.message.id 
          ? { ...msg, status: 'sent' }
          : msg
      );
      
      this.transition(event);
      this.notifySubscribers();
    }
  }

  /**
   * 处理消息接收事件
   */
  private handleMessageReceived(event: ChatEvent): void {
    const payload = this.getPayload(event);
    if (payload?.message) {
      const message = payload.message;
      
      // 检查是否已存在该消息
      const existingIndex = this.stateData.messages.findIndex(m => m.id === message.id);
      
      if (existingIndex >= 0) {
        // 更新现有消息
        this.stateData.messages = this.stateData.messages.map((msg, index) => 
          index === existingIndex ? message : msg
        );
      } else {
        // 添加新消息
        this.stateData.messages = [...this.stateData.messages, message];
      }
      
      this.transition(event);
      this.notifySubscribers();
    }
  }

  /**
   * 处理流式消息开始事件
   */
  private handleStreamingStart(event: ChatEvent): void {
    const payload = this.getPayload(event);
    if (payload?.messageId) {
      this.stateData.streamingMessageId = payload.messageId;
      
      // 🔧 智能状态管理：AI开始回复时，将最后一条pending的用户消息更新为sent
      // 这表示用户消息已被成功接收和处理
      const lastPendingUserMessage = this.stateData.messages
        .slice()
        .reverse()
        .find(msg => msg.role === 'user' && msg.status === 'pending');
      
      if (lastPendingUserMessage) {
        console.log('✅ [StateMachine] AI开始回复，自动更新用户消息状态', {
          userMessageId: lastPendingUserMessage.id,
          userContent: lastPendingUserMessage.content.slice(0, 50),
          aiMessageId: payload.messageId
        });
        
        // 更新用户消息状态为已发送
        this.stateData.messages = this.stateData.messages.map(msg => 
          msg.id === lastPendingUserMessage.id 
            ? { ...msg, status: 'sent' }
            : msg
        );
      }
      
      // 创建流式消息
      const streamingMessage: ChatMessage = {
        id: payload.messageId,
        conversationId: payload.conversationId,
        role: 'assistant',
        content: '',
        timestamp: Date.now(),
        isStreaming: true
      };
      
      this.stateData.messages = [...this.stateData.messages, streamingMessage];
      
      this.transition(event);
      this.notifySubscribers();
    }
  }

  /**
   * 处理流式内容事件
   */
  private handleStreamingContent(event: ChatEvent): void {
    const payload = this.getPayload(event);
    if (payload?.messageId && payload?.delta !== undefined) {
      const messageId = payload.messageId;
      
      // 累积流式消息内容（追加delta到现有content）
      this.stateData.messages = this.stateData.messages.map(msg => 
        msg.id === messageId 
          ? { ...msg, content: msg.content + payload.delta }
          : msg
      );
      
      if (this.enableLogging) {
        console.log('📝 [StateMachine] handleStreamingContent', {
          messageId,
          deltaLength: payload.delta.length,
          totalContentLength: this.stateData.messages.find(m => m.id === messageId)?.content.length || 0
        });
      }
      
      this.notifySubscribers();
    }
  }

  /**
   * 处理流式消息结束事件
   */
  private handleStreamingEnd(event: ChatEvent): void {
    const payload = this.getPayload(event);
    if (payload?.messageId) {
      const messageId = payload.messageId;
      
      // 结束流式消息
      this.stateData.messages = this.stateData.messages.map(msg => 
        msg.id === messageId 
          ? { ...msg, isStreaming: false, content: payload.finalContent || msg.content }
          : msg
      );
      
      this.stateData.streamingMessageId = null;
      
      this.transition(event);
      this.notifySubscribers();
    }
  }

  /**
   * 处理连接状态事件
   */
  private handleConnectionStatus(event: ChatEvent): void {
    const payload = this.getPayload(event);
    if (payload?.status) {
      this.stateData.isConnected = payload.status === 'connected';
      this.stateData.conversationId = payload.conversationId || this.stateData.conversationId;
      
      this.transition(event);
      this.notifySubscribers();
    }
  }

  /**
   * 处理错误事件
   */
  private handleError(event: ChatEvent): void {
    const payload = this.getPayload(event);
    if (payload?.error) {
      this.stateData.error = payload.error;
      this.stateData.conversationId = payload.conversationId || this.stateData.conversationId;
      
      this.transition(event);
      this.notifySubscribers();
    }
  }

  /**
   * 处理会话切换事件
   */
  private handleConversationSwitched(event: ChatEvent): void {
    const payload = this.getPayload(event);
    if (payload?.conversationId) {
      console.log('🔄 [StateMachine] handleConversationSwitched', {
        fromConversationId: this.stateData.conversationId,
        toConversationId: payload.conversationId,
        currentMessagesCount: this.stateData.messages.length
      });
      
      // 清空当前会话的消息和状态
      this.stateData.conversationId = payload.conversationId;
      this.stateData.messages = [];
      this.stateData.currentInput = '';
      this.stateData.error = null;
      this.stateData.streamingMessageId = null;
      
      // 重置到空闲状态
      this.currentState = ChatState.IDLE;
      this.stateData.currentState = this.currentState;
      
      this.notifySubscribers();
      
      console.log('✅ [StateMachine] 会话切换完成，消息已清空');
    }
  }

  /**
   * 处理消息加载完成事件
   */
  private handleMessagesLoaded(event: ChatEvent): void {
    const payload = this.getPayload(event);
    if (payload?.messages && Array.isArray(payload.messages)) {
      console.log('📥 [StateMachine] handleMessagesLoaded', {
        conversationId: payload.conversationId,
        messagesCount: payload.messages.length,
        currentMessagesCount: this.stateData.messages.length,
        source: event.source
      });
      
      // 更新会话和消息数据
      this.stateData.conversationId = payload.conversationId;
      this.stateData.messages = payload.messages;
      
      // 通知订阅者
      this.notifySubscribers();
      
      console.log('✅ [StateMachine] 消息加载完成', {
        conversationId: payload.conversationId,
        finalMessagesCount: this.stateData.messages.length
      });
    }
  }

  /**
   * 处理RUN_FINISHED事件
   */
  private handleRunFinished(event: ChatEvent): void {
    console.log('🏁 [StateMachine] handleRunFinished: 状态机运行结束，重置为IDLE');
    this.currentState = ChatState.IDLE;
    this.stateData.currentState = this.currentState;
    this.notifySubscribers();
  }

  /**
   * 订阅状态变化
   */
  subscribe(listener: (state: ChatStateData) => void): () => void {
    this.subscribers.add(listener);
    
    // 立即通知当前状态
    listener(this.getStateData());
    
    // 返回取消订阅函数
    return () => {
      this.subscribers.delete(listener);
    };
  }

  /**
   * 通知所有订阅者
   */
  private notifySubscribers(): void {
    const stateData = this.getStateData();
    
    if (this.enableLogging) {
      console.log('📢 [StateMachine] notifySubscribers', {
        subscribersCount: this.subscribers.size,
        state: stateData.currentState,
        messagesCount: stateData.messages.length,
        conversationId: stateData.conversationId
      });
    }
    
    this.subscribers.forEach(listener => {
      try {
        listener(stateData);
      } catch (error) {
        console.error('[StateMachine] Error in subscriber:', error);
      }
    });
  }

  /**
   * 重置状态机
   */
  reset(): void {
    const beforeState = { ...this.stateData };
    
    console.log('🔄 [StateMachine] reset: 重置状态机', {
      beforeState: beforeState.currentState,
      beforeMessagesCount: beforeState.messages.length,
      beforeConversationId: beforeState.conversationId,
      stackTrace: new Error().stack?.split('\n').slice(1, 4).join('\n')
    });
    
    this.currentState = ChatState.IDLE;
    this.stateData = {
      currentState: this.currentState,
      conversationId: null,
      messages: [],
      currentInput: '',
      isConnected: false,
      error: null,
      streamingMessageId: null
    };
    
    this.notifySubscribers();
    
    console.log('✅ [StateMachine] reset完成');
  }

  /**
   * 设置会话ID
   */
  setConversationId(conversationId: string): void {
    this.stateData.conversationId = conversationId;
    this.notifySubscribers();
  }

  /**
   * 清空错误
   */
  clearError(): void {
    this.stateData.error = null;
    this.notifySubscribers();
  }

  /**
   * 启用/禁用日志
   */
  setLogging(enabled: boolean): void {
    this.enableLogging = enabled;
  }

  /**
   * 销毁状态机
   */
  destroy(): void {
    this.subscribers.clear();
    
    if (this.enableLogging) {
      console.log('[StateMachine] Destroyed');
    }
  }
}