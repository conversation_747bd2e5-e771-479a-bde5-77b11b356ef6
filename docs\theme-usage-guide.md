# AgentFoundry-Ziko 主题使用指南

## 概述

本项目基于设计稿实现了完整的 Light 主题颜色系统，使用 Tailwind CSS 4.x 的 `@theme inline` 指令进行配置。所有颜色都已转换为语义化的 CSS 变量和对应的 Tailwind 工具类。

## 颜色系统架构

### 1. 基础颜色

```css
/* 文本颜色 */
--text-primary: #000000      /* 主要文本 */
--text-secondary: #666666    /* 次级文本 */
--text-muted: #999999        /* 静音文本 */

/* 背景颜色 */
--bg-base: #ffffff           /* 基础背景 */
```

**使用方式：**
```html
<div class="bg-bg-base text-text-primary">主要内容</div>
<span class="text-text-secondary">次级说明</span>
<p class="text-text-muted">静音提示</p>
```

### 2. 背景色层次系统

基于设计稿中的黑色透明度层次：

```css
--bg-layer-1: rgba(0, 0, 0, 0.01)  /* 1% 透明度 */
--bg-layer-2: rgba(0, 0, 0, 0.02)  /* 2% 透明度 */
--bg-layer-3: rgba(0, 0, 0, 0.04)  /* 4% 透明度 */
--bg-layer-4: rgba(0, 0, 0, 0.08)  /* 8% 透明度 */
```

**使用方式：**
```html
<div class="bg-layer-1">最浅背景层</div>
<div class="bg-layer-2">浅背景层</div>
<div class="bg-layer-3">中等背景层</div>
<div class="bg-layer-4">深背景层</div>
```

**应用场景：**
- `bg-layer-1`: 页面主背景、大面积区域
- `bg-layer-2`: 卡片背景、面板背景
- `bg-layer-3`: 悬停状态、分隔线
- `bg-layer-4`: 边框、分割区域

### 3. 设计稿色板

完整的 8 色系统，每种颜色提供两个变体：

#### 主色 (100% 饱和度)
```css
--color-purple: #5938E4    /* 紫色 - 品牌色 */
--color-blue: #4285F4      /* 蓝色 - 主要操作 */
--color-cyan: #57CBFF      /* 青色 - 信息提示 */
--color-green: #34A853     /* 绿色 - 成功状态 */
--color-yellow: #FBBC04    /* 黄色 - 警告提示 */
--color-orange: #FF7900    /* 橙色 - 次要警告 */
--color-red: #E74639       /* 红色 - 错误状态 */
--color-pink: #FF7DB0      /* 粉色 - 装饰色 */
```

#### 浅色变体 (88% 透明度)
```css
--color-purple-light: rgba(89, 56, 228, 0.88)
--color-blue-light: rgba(66, 133, 244, 0.88)
/* ...其他颜色的浅色变体 */
```

**使用方式：**
```html
<!-- 主色使用 -->
<button class="bg-purple text-white">品牌按钮</button>
<div class="border-blue">蓝色边框</div>
<span class="text-green">成功文本</span>

<!-- 浅色变体使用 -->
<div class="bg-purple-light">浅紫色背景</div>
<span class="text-blue-light">浅蓝色文本</span>
```

### 4. 语义化功能色

基于色板的语义化映射：

```css
--color-brand: var(--color-purple)     /* 品牌色 */
--color-primary: var(--color-blue)     /* 主要操作色 */
--color-success: var(--color-green)    /* 成功状态 */
--color-warning: var(--color-yellow)   /* 警告状态 */
--color-error: var(--color-red)        /* 错误状态 */
--color-info: var(--color-cyan)        /* 信息提示 */
```

**使用方式：**
```html
<button class="bg-brand">品牌按钮</button>
<button class="bg-primary">主要操作</button>
<div class="bg-success text-white">成功提示</div>
<div class="bg-warning">警告信息</div>
<div class="bg-error text-white">错误提示</div>
<div class="bg-info">信息提示</div>
```

### 5. 边框颜色

基于背景层次的边框系统：

```css
--border-light: var(--bg-layer-2)    /* 浅边框 */
--border-medium: var(--bg-layer-3)   /* 中等边框 */
--border-strong: var(--bg-layer-4)   /* 深边框 */
```

**使用方式：**
```html
<div class="border border-border-light">浅边框</div>
<div class="border border-border-medium">中等边框</div>
<div class="border border-border-strong">深边框</div>
```

## 新模块开发最佳实践

### 1. 颜色选择优先级

1. **首选语义化功能色**
   ```html
   <button class="bg-primary">主要按钮</button>
   <div class="text-success">成功状态</div>
   ```

2. **次选设计稿色板**
   ```html
   <div class="bg-purple">紫色背景</div>
   <span class="text-blue-light">浅蓝色文本</span>
   ```

3. **最后使用基础色**
   ```html
   <div class="bg-layer-2 text-text-primary">内容区域</div>
   ```

### 2. 常用组件样式

#### 卡片组件
```html
<div class="bg-bg-base border border-border-light rounded-lg shadow-sm p-4">
  <h3 class="text-text-primary font-semibold">卡片标题</h3>
  <p class="text-text-secondary">卡片内容描述</p>
</div>
```

#### 按钮组件
```html
<!-- 主要按钮 -->
<button class="bg-primary text-white px-4 py-2 rounded-md hover:bg-blue-600">
  主要操作
</button>

<!-- 次要按钮 -->
<button class="bg-layer-2 text-text-primary px-4 py-2 rounded-md hover:bg-layer-3">
  次要操作
</button>

<!-- 危险按钮 -->
<button class="bg-error text-white px-4 py-2 rounded-md hover:bg-red-600">
  删除操作
</button>
```

#### 输入框组件
```html
<input 
  class="bg-bg-base border border-border-medium rounded-md px-3 py-2 
         text-text-primary placeholder:text-text-muted
         focus:border-primary focus:ring-1 focus:ring-primary"
  placeholder="请输入内容"
/>
```

### 3. 状态管理

#### 悬停状态
```html
<div class="bg-layer-1 hover:bg-layer-2 transition-colors">
  悬停变色
</div>
```

#### 激活状态
```html
<button class="bg-primary active:bg-blue-700 text-white">
  可点击按钮
</button>
```

#### 禁用状态
```html
<button class="bg-layer-2 text-text-muted cursor-not-allowed" disabled>
  禁用按钮
</button>
```

## 迁移指南

### 从硬编码颜色迁移

如果你的组件中使用了硬编码颜色，请按以下方式迁移：

```html
<!-- 迁移前 -->
<div class="bg-[#fafafb] border-[#ebebeb] text-[#333333]">
  旧的硬编码样式
</div>

<!-- 迁移后 -->
<div class="bg-layer-1 border-border-light text-text-primary">
  新的主题化样式
</div>
```

### 常见迁移映射

| 旧颜色值 | 新主题变量 | Tailwind 类名 |
|---------|------------|---------------|
| `#fafafb` | `--bg-layer-1` | `bg-layer-1` |
| `#f6f7fa` | `--bg-layer-2` | `bg-layer-2` |
| `#ebebeb` | `--border-light` | `border-border-light` |
| `#333333` | `--text-primary` | `text-text-primary` |
| `#adadad` | `--text-muted` | `text-text-muted` |

## 开发工具

### VS Code 插件推荐

1. **Tailwind CSS IntelliSense** - 提供类名自动补全
2. **CSS Variable Autocomplete** - CSS 变量自动补全

### 调试技巧

在浏览器开发者工具中，你可以通过修改 CSS 变量来快速调试主题：

```css
:root {
  --color-primary: #ff6b6b; /* 临时修改主色 */
}
```

## 注意事项

1. **避免硬编码颜色**：新开发的组件应该完全使用主题变量
2. **保持语义一致性**：相同功能的元素应该使用相同的颜色变量
3. **测试颜色对比度**：确保文本和背景有足够的对比度
4. **遵循设计稿**：所有颜色变量都基于设计稿，请不要随意修改

## 更新记录

- **2024-07-10**: 初始版本，基于设计稿实现完整的 Light 主题系统
- 去除深色模式支持，专注单一主题
- 实现语义化颜色命名和完整的工具类映射