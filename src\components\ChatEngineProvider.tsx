'use client';

import React, { useRef, useCallback, useMemo, useEffect } from 'react';
import { AGUIProvider } from '@/lib/agui/AGUIProvider';
import { AGUIEvent } from '@/lib/agui/types';
import { chatEngine } from '@/lib/chat-engine/chat-engine';
import { AGUIEventBridge } from '@/lib/chat-engine/event-bridge';
import { AgentConfig } from '@/lib/agui/types';
import { useAuth } from '@/lib/auth/AuthProvider';

interface ChatEngineProviderProps {
  children: React.ReactNode;
  conversationId: string;
  config?: Partial<AgentConfig>;
  enableLogging?: boolean;
  onProviderReady?: (disconnect: () => Promise<void>) => void;
}

/**
 * ChatEngine整合Provider
 * 将AGUIProvider和ChatEngine通过EventBridge连接
 */
export function ChatEngineProvider({
  children,
  conversationId,
  config = {},
  enableLogging = false,
  onProviderReady
}: ChatEngineProviderProps) {
  const bridgeRef = useRef<AGUIEventBridge | null>(null);
  const cleanupRef = useRef<(() => Promise<void>) | null>(null);

  const { token } = useAuth();
  
// 动态构建 hubUrl
  const aguiConfig = useMemo(() => {
    // 确保必要的配置都存在
    const agentId = process.env.NEXT_PUBLIC_CONVERSATION_AGENT_ID;
    
    if (!agentId) {
      console.error('❌ NEXT_PUBLIC_CONVERSATION_AGENT_ID 环境变量未配置');
      return null;
    }
    
    if (!conversationId) {
      console.error('❌ conversationId 参数缺失');
      return null;
    }

    // 构建完整的 hubUrl
    const hubUrl = `/ws/chatHub?conversationId=${conversationId}&access_token=${token || ''}`;
    
    console.log('🔧 AGUIProvider 配置:', {
      hubUrl: hubUrl.replace(/access_token=[^&]*/, 'access_token=***'),
      agentId,
      conversationId,
    });

    return {
      hubUrl,
      agentId,
      conversationId,
      autoConnect: true, // 自动连接，因为在会话页面中
      initialState: {},
      ...config
    };
  }, [conversationId, token, config]);

  // 初始化事件桥接器
  const initializeBridge = useCallback(() => {
    if (!bridgeRef.current) {
      const eventBus = chatEngine.getEventBus();
      bridgeRef.current = new AGUIEventBridge(eventBus, { enableLogging });
      
      if (enableLogging) {
        console.log('🌉 [ChatEngineProvider] 事件桥接器已创建');
      }
    }
    return bridgeRef.current;
  }, [enableLogging]);

  // 处理AGUI事件转发
  const handleAGUIEvent = useCallback((event: AGUIEvent) => {
    const bridge = initializeBridge();
    if (bridge) {
      bridge.handleAGUIEvent(event, conversationId);
    }
  }, [conversationId, initializeBridge]);

  // 组件卸载时清理SignalR连接
  useEffect(() => {
    return () => {
      if (bridgeRef.current) {
        bridgeRef.current.destroy();
      }

      // 执行清理函数
      if (cleanupRef.current) {
        cleanupRef.current().catch(error => {
          console.warn('⚠️ 组件卸载时断开连接失败:', error);
        });
      }
    };
  }, [conversationId]);
  
  return (
    <AGUIProvider
      config={aguiConfig as Partial<AgentConfig>}
      onProviderReady={(disconnect) => {
        onProviderReady?.(disconnect);
        // 保存清理函数的引用
        cleanupRef.current = disconnect;
      }}
      onAGUIEvent={handleAGUIEvent}
    >
      {children}
    </AGUIProvider>
  );
}