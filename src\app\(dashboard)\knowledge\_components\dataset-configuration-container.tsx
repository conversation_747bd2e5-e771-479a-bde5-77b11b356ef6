import { cn } from '../_lib/utils';
import { ReactNode } from 'react';

type DatasetConfigurationContainerProps = {
  className?: string;
  show?: boolean;
  children: ReactNode;
};

export function DatasetConfigurationContainer({
  children,
  className,
  show = true,
}: DatasetConfigurationContainerProps) {
  return show ? (
    <div
      className={cn(
        'border p-2 rounded-lg bg-slate-50 border-[#e4e5e6]',
        className
      )}
    >
      {children}
    </div>
  ) : (
    <>{children}</>
  );
}
