'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useConversationState } from '@/store/chatStore';
import HistorySidebar from '@/components/history/HistorySidebar';
import EnvironmentChecker from '@/components/common/EnvironmentChecker';

interface HistoryLayoutProps {
  children: React.ReactNode;
}

export default function HistoryLayout({ children }: HistoryLayoutProps) {
  return (
    <EnvironmentChecker>
      <HistoryLayoutContent>{children}</HistoryLayoutContent>
    </EnvironmentChecker>
  );
}

function HistoryLayoutContent({ children }: HistoryLayoutProps) {
  const router = useRouter();
  const params = useParams();
  const [searchTerm, setSearchTerm] = useState('');
  
  const {
    currentConversationId,
    switchConversation
  } = useConversationState();
  
  // 从 URL 获取当前会话 ID
  const urlConversationId = params.id as string || '';

  // 当URL中的会话ID变化时，同步到全局状态
  useEffect(() => {
    if (urlConversationId && urlConversationId !== currentConversationId) {
      switchConversation(urlConversationId);
    }
  }, [urlConversationId, currentConversationId, switchConversation]);

  const handleConversationSelect = (id: string) => {
    // 使用 Next.js router 进行导航
    router.push(`/history/${id}`);
  };

  return (
    <div className="flex h-full overflow-hidden">
      {/* 左侧历史会话管理 */}
      <div className="bg-layer-1 flex flex-col h-full">
        <HistorySidebar
          selectedConversationId={urlConversationId}
          onConversationSelect={handleConversationSelect}
        />
      </div>

      {/* 右侧聊天内容查看 */}
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        <div className="flex-1 flex flex-col h-full">
          {children}
        </div>
      </div>
    </div>
  );
}