'use client';

import React from 'react';

interface MainContentProps {
  children: React.ReactNode;
  className?: string;
}

export default function MainContent({ children, className = '' }: MainContentProps) {
  return (
    <main className={`
      flex-1 flex flex-col overflow-hidden
      ${className}
    `}>
      {/* 内容区域 */}
      <div className="flex-1 overflow-hidden bg-bg-base h-full page-container">
          {children}
      </div>
    </main>
  );
}